import DashboardLayout from '@/components/layout/DashboardLayout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import CurrencySettings from '@/components/settings/CurrencySettings'

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Settings
          </h1>
          <p className="text-gray-600 mt-2">Manage your account and application preferences</p>
        </div>

        <CurrencySettings />

        <Card>
          <CardHeader>
            <CardTitle>Account Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Additional account settings will be implemented in future updates.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Application Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Version:</strong> 2.0.0</p>
              <p><strong>Built with:</strong> Next.js, Supabase, TypeScript, Tailwind CSS, Framer Motion</p>
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Transaction Management</li>
                <li>Asset Management</li>
                <li>Liability Tracking</li>
                <li>Receivables Management</li>
                <li>Category Management</li>
                <li>Net Worth Calculation</li>
                <li>Multi-Currency Support (LKR/USD)</li>
                <li>Search and Filtering</li>
                <li>Premium UI with Animations</li>
                <li>Responsive Design</li>
                <li>Secure Authentication</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
