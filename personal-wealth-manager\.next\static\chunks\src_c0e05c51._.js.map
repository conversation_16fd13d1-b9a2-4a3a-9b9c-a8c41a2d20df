{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\n\nexport default function Home() {\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const checkUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n\n      if (user) {\n        router.push('/dashboard')\n      } else {\n        router.push('/login')\n      }\n    }\n\n    checkUser()\n  }, [router, supabase.auth])\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;4CAAY;oBAChB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBAEtD,IAAI,MAAM;wBACR,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;yBAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;GAvBwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}