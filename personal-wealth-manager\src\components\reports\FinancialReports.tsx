'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { BarChart3, TrendingUp, TrendingDown, Calendar, Download, Filter } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Select } from '@/components/ui/Select'
import { formatCurrency, formatDate } from '@/lib/utils'

interface ReportData {
  period: string
  income: number
  expenses: number
  netIncome: number
  assets: number
  liabilities: number
  netWorth: number
}

interface CategoryBreakdown {
  category: string
  amount: number
  percentage: number
  color: string
}

export default function FinancialReports() {
  const [loading, setLoading] = useState(true)
  const [reportPeriod, setReportPeriod] = useState('monthly')
  const [reportData, setReportData] = useState<ReportData[]>([])
  const [incomeBreakdown, setIncomeBreakdown] = useState<CategoryBreakdown[]>([])
  const [expenseBreakdown, setExpenseBreakdown] = useState<CategoryBreakdown[]>([])

  useEffect(() => {
    loadReportData()
  }, [reportPeriod])

  const loadReportData = async () => {
    setLoading(true)
    
    // Mock data - replace with actual API calls
    const mockReportData: ReportData[] = [
      {
        period: '2024-01',
        income: 150000,
        expenses: 95000,
        netIncome: 55000,
        assets: 500000,
        liabilities: 200000,
        netWorth: 300000
      },
      {
        period: '2024-02',
        income: 155000,
        expenses: 98000,
        netIncome: 57000,
        assets: 520000,
        liabilities: 195000,
        netWorth: 325000
      },
      {
        period: '2024-03',
        income: 160000,
        expenses: 102000,
        netIncome: 58000,
        assets: 545000,
        liabilities: 190000,
        netWorth: 355000
      }
    ]

    const mockIncomeBreakdown: CategoryBreakdown[] = [
      { category: 'Salary', amount: 120000, percentage: 75, color: '#10B981' },
      { category: 'Freelance', amount: 25000, percentage: 15.6, color: '#3B82F6' },
      { category: 'Investments', amount: 15000, percentage: 9.4, color: '#8B5CF6' }
    ]

    const mockExpenseBreakdown: CategoryBreakdown[] = [
      { category: 'Food & Dining', amount: 35000, percentage: 34.3, color: '#EF4444' },
      { category: 'Transportation', amount: 20000, percentage: 19.6, color: '#F59E0B' },
      { category: 'Utilities', amount: 15000, percentage: 14.7, color: '#06B6D4' },
      { category: 'Entertainment', amount: 12000, percentage: 11.8, color: '#EC4899' },
      { category: 'Shopping', amount: 10000, percentage: 9.8, color: '#84CC16' },
      { category: 'Others', amount: 10000, percentage: 9.8, color: '#6B7280' }
    ]

    setReportData(mockReportData)
    setIncomeBreakdown(mockIncomeBreakdown)
    setExpenseBreakdown(mockExpenseBreakdown)
    setLoading(false)
  }

  const getCurrentPeriodData = () => {
    return reportData[reportData.length - 1] || {
      period: '',
      income: 0,
      expenses: 0,
      netIncome: 0,
      assets: 0,
      liabilities: 0,
      netWorth: 0
    }
  }

  const getPreviousPeriodData = () => {
    return reportData[reportData.length - 2] || getCurrentPeriodData()
  }

  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }

  const exportReport = () => {
    // Export functionality
    console.log('Exporting report...')
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="h-32 bg-gray-300 rounded"></div>
            <div className="h-32 bg-gray-300 rounded"></div>
            <div className="h-32 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  const currentData = getCurrentPeriodData()
  const previousData = getPreviousPeriodData()

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Financial Reports
          </h1>
          <p className="text-gray-600 mt-2">Analyze your financial performance and trends</p>
        </div>
        <div className="flex space-x-3">
          <Select
            value={reportPeriod}
            onChange={(e) => setReportPeriod(e.target.value)}
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </Select>
          <Button onClick={exportReport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Net Income</p>
                <p className="text-2xl font-bold text-green-700">
                  {formatCurrency(currentData.netIncome, 'LKR')}
                </p>
                <div className="flex items-center mt-2">
                  {calculateGrowth(currentData.netIncome, previousData.netIncome) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm ${
                    calculateGrowth(currentData.netIncome, previousData.netIncome) >= 0 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    {Math.abs(calculateGrowth(currentData.netIncome, previousData.netIncome)).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-green-200 rounded-xl">
                <BarChart3 className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Net Worth</p>
                <p className="text-2xl font-bold text-blue-700">
                  {formatCurrency(currentData.netWorth, 'LKR')}
                </p>
                <div className="flex items-center mt-2">
                  {calculateGrowth(currentData.netWorth, previousData.netWorth) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-blue-600 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm ${
                    calculateGrowth(currentData.netWorth, previousData.netWorth) >= 0 
                      ? 'text-blue-600' 
                      : 'text-red-600'
                  }`}>
                    {Math.abs(calculateGrowth(currentData.netWorth, previousData.netWorth)).toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="p-3 bg-blue-200 rounded-xl">
                <TrendingUp className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Savings Rate</p>
                <p className="text-2xl font-bold text-purple-700">
                  {currentData.income > 0 ? ((currentData.netIncome / currentData.income) * 100).toFixed(1) : 0}%
                </p>
                <div className="flex items-center mt-2">
                  <Calendar className="h-4 w-4 text-purple-600 mr-1" />
                  <span className="text-sm text-purple-600">This period</span>
                </div>
              </div>
              <div className="p-3 bg-purple-200 rounded-xl">
                <Calendar className="h-6 w-6 text-purple-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Income vs Expenses Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Income vs Expenses Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.map((data, index) => (
              <motion.div
                key={data.period}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 border rounded-xl"
              >
                <div className="flex-1">
                  <h3 className="font-semibold">{data.period}</h3>
                  <div className="flex space-x-6 mt-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm">Income: {formatCurrency(data.income, 'LKR')}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-sm">Expenses: {formatCurrency(data.expenses, 'LKR')}</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-lg">
                    {formatCurrency(data.netIncome, 'LKR')}
                  </p>
                  <p className="text-sm text-gray-500">Net Income</p>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Income Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {incomeBreakdown.map((item, index) => (
                <motion.div
                  key={item.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full mr-3"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="font-medium">{item.category}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(item.amount, 'LKR')}</p>
                    <p className="text-sm text-gray-500">{item.percentage}%</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Expense Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expenseBreakdown.map((item, index) => (
                <motion.div
                  key={item.category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <div 
                      className="w-4 h-4 rounded-full mr-3"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="font-medium">{item.category}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(item.amount, 'LKR')}</p>
                    <p className="text-sm text-gray-500">{item.percentage}%</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
