-- Update liability types constraint to support more types
-- Run this in Supabase SQL Editor

-- Drop the existing constraint
ALTER TABLE liabilities DROP CONSTRAINT IF EXISTS liabilities_liability_type_check;

-- Add the new constraint with more liability types
ALTER TABLE liabilities ADD CONSTRAINT liabilities_liability_type_check 
CHECK (liability_type IN (
  'loan_taken', 
  'credit_card', 
  'mortgage', 
  'insurance', 
  'utilities', 
  'taxes', 
  'subscription', 
  'rent', 
  'medical', 
  'education', 
  'other'
));

-- Verify the constraint was added
SELECT conname, consrc 
FROM pg_constraint 
WHERE conname = 'liabilities_liability_type_check';
