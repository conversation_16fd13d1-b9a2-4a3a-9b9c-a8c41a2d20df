(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196],{52:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>c,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>d,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(e,t)}let a=e,c=t,s=new Map,f=s,d=0,h=!1;function p(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(h)throw Error(n(3));return c}function v(e){if("function"!=typeof e)throw Error(n(4));if(h)throw Error(n(5));let t=!0;p();let r=d++;return f.set(r,e),function(){if(t){if(h)throw Error(n(6));t=!1,p(),f.delete(r),s=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(h)throw Error(n(9));try{h=!0,c=a(c,e)}finally{h=!1}return(s=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:v(t)}},[i](){return this}}}}}function c(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let u=a[t],c=i[u],s=e[u],f=c(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(e).length)?l:e}}function s(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=s(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2589:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},3786:(e,t,r)=>{"use strict";r.d(t,{r:()=>tC});var n=r(12115),i=r(46641);r(39611);var a=Symbol.for("react.forward_ref"),o=Symbol.for("react.memo");function l(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var u={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s="undefined"!=typeof navigator&&"ReactNative"===navigator.product,f=c||s?n.useLayoutEffect:n.useEffect;function d(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var h={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},p={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},y={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},v={[a]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[o]:y};function g(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case a:case null:case o:case null:return e;default:return t}}case null:return t}}}(e)===o?y:v[e.$$typeof]||h}var m=Object.defineProperty,b=Object.getOwnPropertyNames,x=Object.getOwnPropertySymbols,w=Object.getOwnPropertyDescriptor,O=Object.getPrototypeOf,M=Object.prototype,j=Symbol.for("react-redux-context"),P="undefined"!=typeof globalThis?globalThis:{},S=function(){if(!n.createContext)return{};let e=P[j]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),A=function(e){let{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{let e=function(e,t){let r,n=u,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=u)}let s={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),l=n.useMemo(()=>a.getState(),[a]);return f(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,l]),n.createElement((r||S).Provider,{value:o},t)},_=r(52),E=r(5710),T=r(34890),k=r(34487),C=(0,E.Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:D,setLayout:N,setChartSize:I,setScale:z}=C.actions,L=C.reducer,$=r(68924),R=r(97238),U=r(20215),F=r(58573),K=r(94732),H=r(47062),B=(0,$.Mz)([(e,t)=>t,R.fz,H.D0,U.Re,U.gL,U.R4,K.r1,F.HZ],K.aX),Z=r(96523),q=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},W=(0,E.VP)("mouseClick"),G=(0,E.Nc)();G.startListening({actionCreator:W,effect:(e,t)=>{var r=e.payload,n=B(t.getState(),q(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,T.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var Y=(0,E.VP)("mouseMove"),V=(0,E.Nc)();function X(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}V.startListening({actionCreator:Y,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,Z.au)(n,n.tooltip.settings.shared),a=B(n,q(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch((0,T.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch((0,T.xS)()))}});var J=r(74532);function Q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ee(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var et=(0,E.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,J.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,J.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,J.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=ee(ee({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:er,removeXAxis:en,addYAxis:ei,removeYAxis:ea,addZAxis:eo,removeZAxis:el,updateYAxisWidth:eu}=et.actions,ec=et.reducer,es=r(22248),ef=(0,E.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,J.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,J.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,J.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:ed,removeDot:eh,addArea:ep,removeArea:ey,addLine:ev,removeLine:eg}=ef.actions,em=ef.reducer,eb={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},ex=(0,E.Z0)({name:"brush",initialState:eb,reducers:{setBrushSettings:(e,t)=>null==t.payload?eb:t.payload}}),{setBrushSettings:ew}=ex.actions,eO=ex.reducer,eM=r(32634),ej={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},eP=(0,E.Z0)({name:"rootProps",initialState:ej,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:ej.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),eS=eP.reducer,{updateOptions:eA}=eP.actions,e_=(0,E.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,J.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,J.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:eE,removeRadiusAxis:eT,addAngleAxis:ek,removeAngleAxis:eC}=e_.actions,eD=e_.reducer,eN=(0,E.Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:eI}=eN.actions,ez=eN.reducer,eL=r(14299),e$=r(60841),eR=(0,E.VP)("keyDown"),eU=(0,E.VP)("focus"),eF=(0,E.Nc)();eF.startListening({actionCreator:eR,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number((0,e$.P)(n,(0,U.n4)(r))),o=(0,U.R4)(r);if("Enter"===i){var l=(0,K.pg)(r,"axis","hover",String(n.index));t.dispatch((0,T.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var u=a+("ArrowRight"===i?1:-1)*("left-to-right"===(0,eL._y)(r)?1:-1);if(null!=o&&!(u>=o.length)&&!(u<0)){var c=(0,K.pg)(r,"axis","hover",String(u));t.dispatch((0,T.o4)({active:!0,activeIndex:u.toString(),activeDataKey:void 0,activeCoordinate:c}))}}}}}),eF.startListening({actionCreator:eU,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=(0,K.pg)(r,"axis","hover",String("0"));t.dispatch((0,T.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var eK=(0,E.VP)("externalEvent"),eH=(0,E.Nc)();eH.startListening({actionCreator:eK,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,U.eE)(r),activeDataKey:(0,U.Xb)(r),activeIndex:(0,U.A2)(r),activeLabel:(0,U.BZ)(r),activeTooltipIndex:(0,U.A2)(r),isTooltipActive:(0,U.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}});var eB=r(84421),eZ=r(46670),eq=r(75714),eW=(0,$.Mz)([eq.J],e=>e.tooltipItemPayloads),eG=(0,$.Mz)([eW,eZ.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),eY=(0,E.VP)("touchMove"),eV=(0,E.Nc)();eV.startListening({actionCreator:eY,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=(0,Z.au)(n,n.tooltip.settings.shared);if("axis"===i){var a=B(n,q({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch((0,T.Nt)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],u=document.elementFromPoint(l.clientX,l.clientY);if(!u||!u.getAttribute)return;var c=u.getAttribute(eB.F0),s=null!=(o=u.getAttribute(eB.um))?o:void 0,f=eG(t.getState(),c,s);t.dispatch((0,T.RD)({activeDataKey:s,activeIndex:c,activeCoordinate:f}))}}});var eX=(0,_.HY)({brush:eO,cartesianAxis:ec,chartData:k.LV,graphicalItems:es.iZ,layout:L,legend:eM.CU,options:i.lJ,polarAxis:eD,polarOptions:ez,referenceElements:em,rootProps:eS,tooltip:T.En}),eJ=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,E.U1)({reducer:eX,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([G.middleware,V.middleware,eF.middleware,eH.middleware,eV.middleware]),devTools:{serialize:{replacer:X},name:"recharts-".concat(t)}})},eQ=r(71807),e0=r(15064);function e1(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,eQ.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=eJ(t,i));var l=e0.E;return n.createElement(A,{context:l,store:o.current},r)}var e2=r(81971),e4=e=>{var{chartData:t}=e,r=(0,e2.j)(),i=(0,eQ.r)();return(0,n.useEffect)(()=>i?()=>{}:(r((0,k.hq)(t)),()=>{r((0,k.hq)(void 0))}),[t,r,i]),null};function e5(e){var{layout:t,width:r,height:i,margin:a}=e,o=(0,e2.j)(),l=(0,eQ.r)();return(0,n.useEffect)(()=>{l||(o(N(t)),o(I({width:r,height:i})),o(D(a)))},[o,l,t,r,i,a]),null}function e3(e){var t=(0,e2.j)();return(0,n.useEffect)(()=>{t(eA(e))},[t,e]),null}function e6(e){var t=(0,e2.j)();return(0,n.useEffect)(()=>{t(eI(e))},[t,e]),null}var e8=r(70788),e7=r(96752),e9=r(52596),te=["children","width","height","viewBox","className","style","title","desc"];function tt(){return(tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var tr=(0,n.forwardRef)((e,t)=>{var{children:r,width:i,height:a,viewBox:o,className:l,style:u,title:c,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,te),d=o||{width:i,height:a,x:0,y:0},h=(0,e9.$)("recharts-surface",l);return n.createElement("svg",tt({},(0,e8.J9)(f,!0,"svg"),{className:h,width:i,height:a,style:u,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),n.createElement("title",null,c),n.createElement("desc",null,s),r)}),tn=r(20972),ti=r(78892),ta=["children"];function to(){return(to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var tl={width:"100%",height:"100%"},tu=(0,n.forwardRef)((e,t)=>{var r,i,a=(0,R.yi)(),o=(0,R.rY)(),l=(0,e7.$)();if(!(0,ti.F)(a)||!(0,ti.F)(o))return null;var{children:u,otherAttributes:c,title:s,desc:f}=e;return r="number"==typeof c.tabIndex?c.tabIndex:l?0:void 0,i="string"==typeof c.role?c.role:l?"application":void 0,n.createElement(tr,to({},c,{title:s,desc:f,role:i,tabIndex:r,width:a,height:o,style:tl,ref:t}),u)}),tc=e=>{var{children:t}=e,r=(0,e2.G)(tn.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(tr,{width:i,height:a,x:l,y:o},t)},ts=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ta);return(0,eQ.r)()?n.createElement(tc,null,r):n.createElement(tu,to({ref:t},i),r)}),tf=r(46850),td=r(2589),th=r(25115),tp=(0,n.createContext)(null);function ty(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var tv=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:c,onMouseEnter:s,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:m}=e,b=(0,e2.j)(),[x,w]=(0,n.useState)(null),[O,M]=(0,n.useState)(null);(0,tf.l3)();var j=function(){var e=(0,e2.j)(),[t,r]=(0,n.useState)(null),i=(0,e2.G)(td.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,ti.H)(r)&&r!==i&&e(z(r))}},[t,e,i]),r}(),P=(0,n.useCallback)(e=>{j(e),"function"==typeof t&&t(e),w(e),M(e)},[j,t,w,M]),S=(0,n.useCallback)(e=>{b(W(e)),b(eK({handler:o,reactEvent:e}))},[b,o]),A=(0,n.useCallback)(e=>{b(Y(e)),b(eK({handler:s,reactEvent:e}))},[b,s]),_=(0,n.useCallback)(e=>{b((0,T.xS)()),b(eK({handler:f,reactEvent:e}))},[b,f]),E=(0,n.useCallback)(e=>{b(Y(e)),b(eK({handler:d,reactEvent:e}))},[b,d]),k=(0,n.useCallback)(()=>{b(eU())},[b]),C=(0,n.useCallback)(e=>{b(eR(e.key))},[b]),D=(0,n.useCallback)(e=>{b(eK({handler:l,reactEvent:e}))},[b,l]),N=(0,n.useCallback)(e=>{b(eK({handler:u,reactEvent:e}))},[b,u]),I=(0,n.useCallback)(e=>{b(eK({handler:c,reactEvent:e}))},[b,c]),L=(0,n.useCallback)(e=>{b(eK({handler:h,reactEvent:e}))},[b,h]),$=(0,n.useCallback)(e=>{b(eK({handler:v,reactEvent:e}))},[b,v]),R=(0,n.useCallback)(e=>{b(eY(e)),b(eK({handler:y,reactEvent:e}))},[b,y]),U=(0,n.useCallback)(e=>{b(eK({handler:p,reactEvent:e}))},[b,p]);return n.createElement(th.$.Provider,{value:x},n.createElement(tp.Provider,{value:O},n.createElement("div",{className:(0,e9.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ty(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:m,height:a},g),onClick:S,onContextMenu:D,onDoubleClick:N,onFocus:k,onKeyDown:C,onMouseDown:I,onMouseEnter:A,onMouseLeave:_,onMouseMove:E,onMouseUp:L,onTouchEnd:U,onTouchMove:R,onTouchStart:$,ref:P},r)))}),tg=r(16377),tm=(0,$.Mz)([F.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),tb=(0,$.Mz)([tm,td.Lp,td.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),tx=()=>(0,e2.G)(tb),tw=(0,n.createContext)(void 0),tO=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,tg.NF)("recharts"),"-clip")),i=tx();if(null==i)return null;var{x:a,y:o,width:l,height:u}=i;return n.createElement(tw.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:u,width:l}))),t)},tM=["children","className","width","height","style","compact","title","desc"],tj=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:a,height:o,style:l,compact:u,title:c,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,tM),d=(0,e8.J9)(f,!1);return u?n.createElement(ts,{otherAttributes:d,title:c,desc:s},r):n.createElement(tv,{className:i,style:l,width:a,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(ts,{otherAttributes:d,title:c,desc:s,ref:t},n.createElement(tO,null,r)))}),tP=r(93389),tS=["width","height","layout"];function tA(){return(tA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var t_={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},tE=(0,n.forwardRef)(function(e,t){var r,i=(0,tP.e)(e.categoricalChartProps,t_),{width:a,height:o,layout:l}=i,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,tS);if(!(0,ti.F)(a)||!(0,ti.F)(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e;return n.createElement(e1,{preloadedState:{options:{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:c},n.createElement(e4,{chartData:i.data}),n.createElement(e5,{width:a,height:o,layout:l,margin:i.margin}),n.createElement(e3,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(e6,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(tj,tA({width:a,height:o},u,{ref:t})))}),tT=["item"],tk={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},tC=(0,n.forwardRef)((e,t)=>{var r=(0,tP.e)(e,tk);return n.createElement(tE,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:tT,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},5710:(e,t,r)=>{"use strict";r.d(t,{U1:()=>m,VP:()=>c,Nc:()=>ey,Z0:()=>k});var n=r(52);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(74532),l=(r(49509),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var u=e=>e&&"function"==typeof e.match;function c(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ew(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function s(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function d(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function h(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var p=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new f;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},y=e=>t=>{setTimeout(t,e)},v=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,u(c)),n.dispatch(e)}finally{i=!0}}})},g=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(e){let t,r,i=p(),{reducer:a,middleware:o,devTools:u=!0,duplicateMiddlewareCheck:c=!0,preloadedState:s,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ew(1));r="function"==typeof o?o(i):i();let d=n.Zz;u&&(d=l({trace:!1,..."object"==typeof u&&u}));let h=g((0,n.Tw)(...r)),y=d(..."function"==typeof f?f(h):h());return(0,n.y$)(t,s,y)}function b(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ew(28));if(n in r)throw Error(ew(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var x=(e,t)=>u(e)?e.match(t):e(t);function w(...e){return t=>e.some(e=>x(e,t))}var O=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},M=["name","message","stack","code"],j=class{constructor(e,t){this.payload=e,this.meta=t}_type},P=class{constructor(e,t){this.payload=e,this.meta=t}_type},S=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of M)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},A="External signal was aborted";function _(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var E=Symbol.for("rtk-slice-createasyncthunk"),T=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(T||{}),k=function({creators:e}={}){let t=e?.asyncThunk?.[E];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ew(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),u={},s={},f={},p=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ew(12));if(r in s)throw Error(ew(13));return s[r]=t,y},addMatcher:(e,t)=>(p.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(f[e]=t,y),exposeCaseReducer:(e,t)=>(u[e]=t,y)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?b(e.extraReducers):[e.extraReducers],i={...t,...s};return function(e,t){let r,[n,i,a]=b(t);if("function"==typeof e)r=()=>d(e());else{let t=d(e);r=()=>t}function l(e=r(),t){let u=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[a]),u.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of p)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ew(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:u,settled:c,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||C,pending:l||C,rejected:u||C,settled:c||C})}(o,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ew(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?c(e,o):c(e))}(o,i,y)});let g=e=>e,m=new Map,x=new WeakMap;function w(e,t){return r||(r=v()),r(e,t)}function O(){return r||(r=v()),r.getInitialState()}function M(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=h(x,n,O)),i}function i(t=g){let n=h(m,r,()=>new WeakMap);return h(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>h(x,t,O),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let j={name:n,reducer:w,actions:f,caseReducers:u,getInitialState:O,...M(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:w},r),{...j,...M(n,!0)}}};return j}}();function C(){}function D(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(s)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function N(e,t){return t(e)}function I(e){return Array.isArray(e)||(e=Object.values(e)),e}var z="listener",L="completed",$="cancelled",R=`task-${$}`,U=`task-${L}`,F=`${z}-${$}`,K=`${z}-${L}`,H=class{constructor(e){this.code=e,this.message=`task ${$} (reason: ${e})`}name="TaskAbortError";message},B=(e,t)=>{if("function"!=typeof e)throw TypeError(ew(32))},Z=()=>{},q=(e,t=Z)=>(e.catch(t),e),W=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),G=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},Y=e=>{if(e.aborted){let{reason:t}=e;throw new H(t)}};function V(e,t){let r=Z;return new Promise((n,i)=>{let a=()=>i(new H(e.reason));if(e.aborted)return void a();r=W(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=Z})}var X=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof H?"cancelled":"rejected",error:e}}finally{t?.()}},J=e=>t=>q(V(e,t).then(t=>(Y(e),t))),Q=e=>{let t=J(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et={},er="listenerMiddleware",en=(e,t)=>{let r=t=>W(e,()=>G(t,e.reason));return(n,i)=>{B(n,"taskExecutor");let a=new AbortController;r(a);let o=X(async()=>{Y(e),Y(a.signal);let t=await n({pause:J(a.signal),delay:Q(a.signal),signal:a.signal});return Y(a.signal),t},()=>G(a,U));return i?.autoJoin&&t.push(o.catch(Z)),{result:J(e)(o),cancel(){G(a,R)}}}},ei=(e,t)=>{let r=async(r,n)=>{Y(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await V(t,Promise.race(a));return Y(t),e}finally{i()}};return(e,t)=>q(r(e,t))},ea=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=c(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ew(21));return B(a,"options.listener"),{predicate:i,type:t,effect:a}},eo=ee(e=>{let{type:t,predicate:r,effect:n}=ea(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ew(22))}}},{withTypes:()=>eo}),el=(e,t)=>{let{type:r,effect:n,predicate:i}=ea(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},eu=e=>{e.pending.forEach(e=>{G(e,F)})},ec=e=>()=>{e.forEach(eu),e.clear()},es=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},ef=ee(c(`${er}/add`),{withTypes:()=>ef}),ed=c(`${er}/removeAll`),eh=ee(c(`${er}/remove`),{withTypes:()=>eh}),ep=(...e)=>{console.error(`${er}/error`,...e)},ey=(e={})=>{let t=new Map,{extra:r,onError:i=ep}=e;B(i,"onError");let a=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&eu(e)}),o=e=>a(el(t,e)??eo(e));ee(o,{withTypes:()=>o});let l=e=>{let r=el(t,e);return r&&(r.unsubscribe(),e.cancelActive&&eu(r)),!!r};ee(l,{withTypes:()=>l});let u=async(e,n,a,l)=>{let u=new AbortController,c=ei(o,u.signal),s=[];try{e.pending.add(u),await Promise.resolve(e.effect(n,ee({},a,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:Q(u.signal),pause:J(u.signal),extra:r,signal:u.signal,fork:en(u.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(G(e,F),r.delete(e))})},cancel:()=>{G(u,F),e.pending.delete(u)},throwIfCancelled:()=>{Y(u.signal)}})))}catch(e){e instanceof H||es(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),G(u,K),e.pending.delete(u)}},c=ec(t);return{middleware:e=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(ef.match(a))return o(a.payload);if(ed.match(a))return void c();if(eh.match(a))return l(a.payload);let f=e.getState(),d=()=>{if(f===et)throw Error(ew(23));return f};try{if(s=r(a),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(a,r,f)}catch(e){t=!1,es(i,e,{raisedBy:"predicate"})}t&&u(n,a,e,d)}}}finally{f=et}return s},startListening:o,stopListening:l,clearListeners:c}},ev=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eg=Symbol.for("rtk-state-proxy-original"),em=e=>!!e&&!!e[eg],eb=new WeakMap,ex={};function ew(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},6115:(e,t,r)=>{"use strict";var n=r(12115),i=r(49033),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=o(e,(f=c(function(){function e(e){if(!u){if(u=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=h},[h]),s(h),h}},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23676),i=r(72465),a=r(10656),o=r(81571);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},7611:(e,t,r)=>{"use strict";r.d(t,{F:()=>tD,L:()=>tA});var n=r(12115),i=r(95672),a=r.n(i),o=r(52596),l=r(68924),u=r(60356),c=r(58573),s=r(39827),f=r(14299),d=r(97238),h=r(66038),p=r(12287),y=r(18478),v=e=>e.graphicalItems.polarItems,g=(0,l.Mz)([h.N,p.E],f.eo),m=(0,l.Mz)([v,f.DP,g],f.ec),b=(0,l.Mz)([m],f.rj),x=(0,l.Mz)([b,u.z3],f.Nk),w=(0,l.Mz)([x,f.DP,m],f.fb),O=(0,l.Mz)([x,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),M=()=>void 0,j=(0,l.Mz)([f.DP,f.AV,M,O,M],f.wL),P=(0,l.Mz)([f.DP,d.fz,x,w,y.eC,h.N,j],f.tP),S=(0,l.Mz)([P,f.DP,f.xM],f.xp);function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,P,S,h.N],f.g1);var E=(e,t)=>t,T=[],k=(e,t,r)=>(null==r?void 0:r.length)===0?T:r,C=(0,l.Mz)([u.z3,E,k],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>_(_({},t.presentationProps),e.props))),null!=n)return n}),D=(0,l.Mz)([C,E,k],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([v,E],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.Mz)([C,N,k,c.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return tA({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(81971),L=r(22248);function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){var t=(0,z.j)();return(0,n.useEffect)(()=>(t((0,L.As)(e)),()=>{t((0,L.TK)(e))}),[t,e]),null}var U=r(70788),F=["children","className"];function K(){return(K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var H=n.forwardRef((e,t)=>{var{children:r,className:i}=e,a=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,F),l=(0,o.$)("recharts-layer",i);return n.createElement("g",K({className:l},(0,U.J9)(a,!0),{ref:t}),r)}),B=r(70688),Z=r(16377),q=r(41643);function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Y={widthCache:{},cacheCount:0},V={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},X="recharts_measurement_span",J=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||q.m.isSsr)return{width:0,height:0};var n=(Object.keys(t=G({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(Y.widthCache[i])return Y.widthCache[i];try{var a=document.getElementById(X);a||((a=document.createElement("span")).setAttribute("id",X),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=G(G({},V),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),u={width:l.width,height:l.height};return Y.widthCache[i]=u,++Y.cacheCount>2e3&&(Y.cacheCount=0,Y.widthCache={}),u}catch(e){return{width:0,height:0}}},Q=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,ee=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,et=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,er=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,en={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},ei=Object.keys(en);class ea{static parse(e){var t,[,r,n]=null!=(t=er.exec(e))?t:[];return new ea(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new ea(NaN,""):new ea(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new ea(NaN,""):new ea(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new ea(NaN,""):new ea(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new ea(NaN,""):new ea(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,Z.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,Z.M8)(e)&&(this.unit=""),""===t||et.test(t)||(this.num=NaN,this.unit=""),ei.includes(t)&&(this.num=e*en[t],this.unit="px")}}function eo(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=Q.exec(t))?r:[],o=ea.parse(null!=n?n:""),l=ea.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";t=t.replace(Q,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var c,[,s,f,d]=null!=(c=ee.exec(t))?c:[],h=ea.parse(null!=s?s:""),p=ea.parse(null!=d?d:""),y="+"===f?h.add(p):h.subtract(p);if(y.isNaN())return"NaN";t=t.replace(ee,y.toString())}return t}var el=/\(([^()]*)\)/;function eu(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=el.exec(r));){var[,n]=t;r=r.replace(el,eo(n))}return r}(t),t=eo(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var ec=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],es=["dx","dy","angle","className","breakAll"];function ef(){return(ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ed(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var eh=/[ \f\n\r\t\v\u2028\u2029]+/,ep=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,Z.uy)(t)||(i=r?t.toString().split(""):t.toString().split(eh));var a=i.map(e=>({word:e,width:J(e,n).width})),o=r?0:J("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},ey=(e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:u,breakAll:c}=e,s=(0,Z.Et)(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i||!(d.length>o||h(d).width>Number(n)))return d;for(var p=e=>{var t=f(ep({breakAll:c,style:u,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||h(t).width>Number(n),t]},y=0,v=l.length-1,g=0;y<=v&&g<=l.length-1;){var m=Math.floor((y+v)/2),[b,x]=p(m-1),[w]=p(m);if(b||w||(y=m+1),b&&w&&(v=m-1),!b&&w){a=x;break}g++}return a||d},ev=e=>[{words:(0,Z.uy)(e)?[]:e.toString().split(eh)}],eg=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!q.m.isSsr){var l=ep({breakAll:a,children:n,style:i});if(!l)return ev(n);var{wordsWithComputedWidth:u,spaceWidth:c}=l;return ey({breakAll:a,children:n,maxLines:o,style:i},u,c,t,r)}return ev(n)},em="#808080",eb=(0,n.forwardRef)((e,t)=>{var r,{x:i=0,y:a=0,lineHeight:l="1em",capHeight:u="0.71em",scaleToFit:c=!1,textAnchor:s="start",verticalAnchor:f="end",fill:d=em}=e,h=ed(e,ec),p=(0,n.useMemo)(()=>eg({breakAll:h.breakAll,children:h.children,maxLines:h.maxLines,scaleToFit:c,style:h.style,width:h.width}),[h.breakAll,h.children,h.maxLines,c,h.style,h.width]),{dx:y,dy:v,angle:g,className:m,breakAll:b}=h,x=ed(h,es);if(!(0,Z.vh)(i)||!(0,Z.vh)(a))return null;var w=i+((0,Z.Et)(y)?y:0),O=a+((0,Z.Et)(v)?v:0);switch(f){case"start":r=eu("calc(".concat(u,")"));break;case"middle":r=eu("calc(".concat((p.length-1)/2," * -").concat(l," + (").concat(u," / 2))"));break;default:r=eu("calc(".concat(p.length-1," * -").concat(l,")"))}var M=[];if(c){var j=p[0].width,{width:P}=h;M.push("scale(".concat((0,Z.Et)(P)?P/j:1,")"))}return g&&M.push("rotate(".concat(g,", ").concat(w,", ").concat(O,")")),M.length&&(x.transform=M.join(" ")),n.createElement("text",ef({},(0,U.J9)(x,!0),{ref:t,x:w,y:O,className:(0,o.$)("recharts-text",m),textAnchor:s,fill:d.includes("url")?em:d}),p.map((e,t)=>{var i=e.words.join(b?"":" ");return n.createElement("tspan",{x:w,dy:0===t?r:l,key:"".concat(i,"-").concat(t)},i)}))});eb.displayName="Text";var ex=r(54811),ew=r(25641),eO=r(43597),eM=r(80931),ej=r.n(eM),eP=r(44538),eS=r(93389),eA=r(74460);function e_(){return(e_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var eE=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},eT={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ek=e=>{var t=(0,eS.e)(e,eT),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:l,y:u,upperWidth:c,lowerWidth:s,height:f,className:d}=t,{animationEasing:h,animationDuration:p,animationBegin:y,isUpdateAnimationActive:v}=t;if(l!==+l||u!==+u||c!==+c||s!==+s||f!==+f||0===c&&0===s||0===f)return null;var g=(0,o.$)("recharts-trapezoid",d);return v?n.createElement(eA.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:l,y:u},to:{upperWidth:c,lowerWidth:s,height:f,x:l,y:u},duration:p,animationEasing:h,isActive:v},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:c}=e;return n.createElement(eA.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:p,easing:h},n.createElement("path",e_({},(0,U.J9)(t,!0),{className:g,d:eE(u,c,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",e_({},(0,U.J9)(t,!0),{className:g,d:eE(l,u,c,s,f)})))},eC=r(77283);let eD=Math.cos,eN=Math.sin,eI=Math.sqrt,ez=Math.PI,eL=2*ez,e$={draw(e,t){let r=eI(t/ez);e.moveTo(r,0),e.arc(0,0,r,0,eL)}},eR=eI(1/3),eU=2*eR,eF=eN(ez/10)/eN(7*ez/10),eK=eN(eL/10)*eF,eH=-eD(eL/10)*eF,eB=eI(3),eZ=eI(3)/2,eq=1/eI(12),eW=(eq/2+1)*3;var eG=r(85654),eY=r(31847);eI(3),eI(3);var eV=["type","size","sizeType"];function eX(){return(eX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eQ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eJ(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var e0={symbolCircle:e$,symbolCross:{draw(e,t){let r=eI(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=eI(t/eU),n=r*eR;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=eI(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=eI(.8908130915292852*t),n=eK*r,i=eH*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=eL*t/5,o=eD(a),l=eN(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-eI(t/(3*eB));e.moveTo(0,2*r),e.lineTo(-eB*r,-r),e.lineTo(eB*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=eI(t/eW),n=r/2,i=r*eq,a=r*eq+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-eZ*i,eZ*n+-.5*i),e.lineTo(-.5*n-eZ*a,eZ*n+-.5*a),e.lineTo(-.5*o-eZ*a,eZ*o+-.5*a),e.lineTo(-.5*n+eZ*i,-.5*i-eZ*n),e.lineTo(-.5*n+eZ*a,-.5*a-eZ*n),e.lineTo(-.5*o+eZ*a,-.5*a-eZ*o),e.closePath()}}},e1=Math.PI/180,e2=e=>e0["symbol".concat((0,Z.Zb)(e))]||e$,e4=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*e1;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},e5=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=eQ(eQ({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,eV)),{},{type:t,size:r,sizeType:i}),{className:l,cx:u,cy:c}=a,s=(0,U.J9)(a,!0);return u===+u&&c===+c&&r===+r?n.createElement("path",eX({},s,{className:(0,o.$)("recharts-symbols",l),transform:"translate(".concat(u,", ").concat(c,")"),d:(()=>{var e=e2(t);return(function(e,t){let r=null,n=(0,eY.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,eG.A)(e||e$),t="function"==typeof t?t:(0,eG.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,eG.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,eG.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(e4(r,i,t))()})()})):null};e5.registerSymbol=(e,t)=>{e0["symbol".concat((0,Z.Zb)(e))]=t};var e3=["option","shapeType","propTransformer","activeClassName","isActive"];function e6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e6(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function e7(e,t){return e8(e8({},t),e)}function e9(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(eP.M,r);case"trapezoid":return n.createElement(ek,r);case"sector":return n.createElement(eC.h,r);case"symbols":if("symbols"===t)return n.createElement(e5,r);break;default:return null}}function te(e){var t,{option:r,shapeType:i,propTransformer:a=e7,activeClassName:o="recharts-active-shape",isActive:l}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,e3);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,e8(e8({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(ej()(r)&&"boolean"!=typeof r){var c=a(r,u);t=n.createElement(e9,{shapeType:i,elementProps:c})}else t=n.createElement(e9,{shapeType:i,elementProps:u});return l?n.createElement(H,{className:o},t):t}var tt=r(34890),tr=(e,t)=>{var r=(0,z.j)();return(n,i)=>a=>{null==e||e(n,i,a),r((0,tt.RD)({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},tn=e=>{var t=(0,z.j)();return(r,n)=>i=>{null==e||e(r,n,i),t((0,tt.oP)())}},ti=(e,t)=>{var r=(0,z.j)();return(n,i)=>a=>{null==e||e(n,i,a),r((0,tt.ML)({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},ta=r(71807);function to(e){var{fn:t,args:r}=e,i=(0,z.j)(),a=(0,ta.r)();return(0,n.useEffect)(()=>{if(!a){var e=t(r);return i((0,tt.Ix)(e)),()=>{i((0,tt.XB)(e))}}},[t,r,i,a]),null}var tl=r(20215),tu=r(32634),tc=()=>{};function ts(e){var{legendPayload:t}=e,r=(0,z.j)(),i=(0,z.G)(d.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?tc:(r((0,tu.Lx)(t)),()=>{r((0,tu.u3)(t))}),[r,i,t]),null}var tf=r(84421),td=["onMouseEnter","onClick","onMouseLeave"];function th(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?th(Object(r),!0).forEach(function(t){ty(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):th(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ty(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tv(){return(tv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function tg(e){var t=(0,n.useMemo)(()=>(0,U.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,U.aS)(e.children,ex.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,z.G)(e=>D(e,i,r));return n.createElement(ts,{legendPayload:a})}function tm(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.uM)(l,t),hide:u,type:c,color:o,unit:""}}}var tb=(e,t)=>e>t?"start":e<t?"end":"middle",tx=(e,t,r)=>"function"==typeof t?t(e):(0,Z.F4)(t,r,.8*r),tw=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=(0,ew.lY)(a,o),u=i+(0,Z.F4)(e.cx,a,a/2),c=n+(0,Z.F4)(e.cy,o,o/2),s=(0,Z.F4)(e.innerRadius,l,0);return{cx:u,cy:c,innerRadius:s,outerRadius:tx(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},tO=(e,t)=>(0,Z.sA)(t-e)*Math.min(Math.abs(t-e),360),tM=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(B.I,tv({},t,{type:"linear",className:r}))},tj=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(eb,tv({},t,{alignmentBaseline:"middle",className:a}),i)};function tP(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:o,dataKey:l}=r;if(!i||!a||!t)return null;var u=(0,U.J9)(r,!1),c=(0,U.J9)(a,!1),f=(0,U.J9)(o,!1),d="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,h=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,i=(0,ew.IZ)(e.cx,e.cy,e.outerRadius+d,r),h=tp(tp(tp(tp({},u),e),{},{stroke:"none"},c),{},{index:t,textAnchor:tb(i.x,e.cx)},i),p=tp(tp(tp(tp({},u),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,ew.IZ)(e.cx,e.cy,e.outerRadius,r),i],key:"line"});return n.createElement(H,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&tM(o,p),tj(a,h,(0,s.kr)(e,l)))});return n.createElement(H,{className:"recharts-pie-labels"},h)}function tS(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,z.G)(tl.A2),{onMouseEnter:u,onClick:c,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,td),d=tr(u,a.dataKey),h=tn(s),p=ti(c,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(o)===l,c=u?r:l?i:null,s=tp(tp({},e),{},{stroke:e.stroke,tabIndex:-1,[tf.F0]:o,[tf.um]:a.dataKey});return n.createElement(H,tv({tabIndex:-1,className:"recharts-pie-sector"},(0,eO.XC)(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:h(e,o),onClick:p(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(te,tv({option:c,isActive:u,shapeType:"sector"},s)))}),n.createElement(tP,{sectors:t,props:a,showLabels:o}))}function tA(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:f,dataKey:d,nameKey:h,tooltipType:p}=i,y=Math.abs(i.minAngle),v=tO(c,f),g=Math.abs(v),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,s.kr)(e,d,0)).length,x=g-b*y-(g>=360?b:b-1)*m,w=a.reduce((e,t)=>{var r=(0,s.kr)(t,d,0);return e+((0,Z.Et)(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=(0,s.kr)(e,d,0),f=(0,s.kr)(e,h,t),g=tw(i,l,e),b=((0,Z.Et)(a)?a:0)/w,O=tp(tp({},e),o&&o[t]&&o[t].props),M=(r=t?n.endAngle+(0,Z.sA)(v)*m*(0!==a):c)+(0,Z.sA)(v)*((0!==a?y:0)+b*x),j=(r+M)/2,P=(g.innerRadius+g.outerRadius)/2,S=[{name:f,value:a,payload:O,dataKey:d,type:p}],A=(0,ew.IZ)(g.cx,g.cy,P,j);return n=tp(tp(tp(tp({},i.presentationProps),{},{percent:b,cornerRadius:u,name:f,tooltipPayload:S,midAngle:j,middleRadius:P,tooltipPosition:A},O),g),{},{value:(0,s.kr)(e,d),startAngle:r,endAngle:M,payload:O,paddingAngle:(0,Z.sA)(v)*m})})),r}function t_(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c,activeShape:s,inactiveShape:f,onAnimationStart:d,onAnimationEnd:h}=t,p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,Z.NF)(t)),i=(0,n.useRef)(e);return i.current!==e&&(r.current=(0,Z.NF)(t),i.current=e),r.current}(t,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!1)},[h]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return n.createElement(eA.i,{begin:l,duration:u,isActive:o,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:p},e=>{var{t:o}=e,l=[],u=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,Z.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=tp(tp({},e),{},{startAngle:u+n,endAngle:u+i(o)+n});l.push(c),u=c.endAngle}else{var{endAngle:s,startAngle:f}=e,d=(0,Z.Dj)(0,s-f)(o),h=tp(tp({},e),{},{startAngle:u+n,endAngle:u+d+n});l.push(h),u=h.endAngle}}),r.current=l,n.createElement(H,null,n.createElement(tS,{sectors:l,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!v}))})}function tE(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(t_,{props:e,previousSectorsRef:o}):n.createElement(tS,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function tT(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(H,{tabIndex:i,className:a},n.createElement(tE,e))}var tk={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!q.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function tC(e){var t=(0,eS.e)(e,tk),r=(0,n.useMemo)(()=>(0,U.aS)(e.children,ex.f),[e.children]),i=(0,U.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,z.G)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(to,{fn:tm,args:tp(tp({},t),{},{sectors:o})}),n.createElement(tT,tv({},t,{sectors:o})))}class tD extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(R,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(tg,this.props),n.createElement(tC,this.props),this.props.children)}constructor(){super(...arguments),ty(this,"id",(0,Z.NF)("recharts-pie-"))}}ty(tD,"displayName","Pie"),ty(tD,"defaultProps",tk)},8287:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},8870:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",c=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,d=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,p=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?S(t,d):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),a>(u=(o=Math.ceil(d/7))>u?o+1:u+1)&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((u=c.length)-(a=s.length)<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=i,l?S(t,d):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(c+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=M(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=M(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return S(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(j(this,i),j(e,i),i),l=!0,S(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?A(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):S(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):A(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(c+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(u+"NaN")}for(e=w(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(S(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,S(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this.constructor,d=this.d,h=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(c=d.length)<(s=h.length)&&(a=d,d=h,h=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*d[i-n-1]+t,a[i--]=u%1e7|0,t=u/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?S(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),S(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=_(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=_(n=S(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?_(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=_((n=S(new i(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return S(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,c,s=this,d=s.constructor,h=+(e=new d(e));if(!e.s)return new d(a);if(!(s=new d(s)).s){if(e.s<1)throw Error(u+"Infinity");return s}if(s.eq(a))return s;if(n=d.precision,e.eq(a))return S(s,n);if(c=(t=e.e)>=(r=e.d.length-1),o=s.s,c){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new d(a),t=Math.ceil(n/7+4),l=!1;r%2&&E((i=i.times(s)).d,t),0!==(r=f(r/2));)E((s=s.times(s)).d,t);return l=!0,e.s<0?new d(a).div(i):S(i,n)}}else if(o<0)throw Error(u+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(j(s,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=w(i),n=_(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=w(i=S(new a(i),e,t)),n=_(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),S(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return _(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,s,f,d,h,p,y,v,g,m,b,x,O,M,j,P,A,_=n.constructor,E=n.s==i.s?1:-1,T=n.d,k=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(u+"Division by zero");for(s=0,c=n.e-i.e,P=k.length,M=T.length,y=(p=new _(E)).d=[];k[s]==(T[s]||0);)++s;if(k[s]>(T[s]||0)&&--c,(b=null==a?a=_.precision:o?a+(w(n)-w(i))+1:a)<0)return new _(0);if(b=b/7+2|0,s=0,1==P)for(f=0,k=k[0],b++;(s<M||f)&&b--;s++)x=1e7*f+(T[s]||0),y[s]=x/k|0,f=x%k|0;else{for((f=1e7/(k[0]+1)|0)>1&&(k=e(k,f),T=e(T,f),P=k.length,M=T.length),O=P,g=(v=T.slice(0,P)).length;g<P;)v[g++]=0;(A=k.slice()).unshift(0),j=k[0],k[1]>=1e7/2&&++j;do f=0,(l=t(k,v,P,g))<0?(m=v[0],P!=g&&(m=1e7*m+(v[1]||0)),(f=m/j|0)>1?(f>=1e7&&(f=1e7-1),h=(d=e(k,f)).length,g=v.length,1==(l=t(d,v,h,g))&&(f--,r(d,P<h?A:k,h))):(0==f&&(l=f=1),d=k.slice()),(h=d.length)<g&&d.unshift(0),r(v,d,g),-1==l&&(g=v.length,(l=t(k,v,P,g))<1&&(f++,r(v,P<g?A:k,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=T[O]||0:(v=[T[O]],g=1);while((O++<M||void 0!==v[0])&&b--)}return y[0]||y.shift(),p.e=c,S(p,o?a+w(p)+1:a)}}();function x(e,t){var r,n,i,o,u,c=0,f=0,h=e.constructor,p=h.precision;if(w(e)>16)throw Error(s+w(e));if(!e.s)return new h(a);for(null==t?(l=!1,u=p):u=t,o=new h(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(u+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new h(a),h.precision=u;;){if(n=S(n.times(e),u),r=r.times(++c),m((o=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=S(i.times(i),u);return h.precision=p,null==t?(l=!0,S(i,p)):i}i=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return S(new e(e.LN10),t)}function M(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,i,o,c,s,f,d,h,p=1,y=e,v=y.d,g=y.constructor,x=g.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==t?(l=!1,d=x):d=t,y.eq(10))return null==t&&(l=!0),O(g,d);if(g.precision=d+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(g,d+2,x).times(o+""),y=j(new g(n+"."+r.slice(1)),d-10).plus(f),g.precision=x,null==t?(l=!0,S(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),p++;for(o=w(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=c=y=b(y.minus(a),y.plus(a),d),h=S(y.times(y),d),i=3;;){if(c=S(c.times(h),d),m((f=s.plus(b(c,new g(i),d))).d).slice(0,d)===m(s.d).slice(0,d))return s=s.times(2),0!==o&&(s=s.plus(O(g,d+2,x).times(o+""))),s=b(s,new g(p),d),g.precision=x,null==t?(l=!0,S(s,x)):s;s=f,i+=2}}function P(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>p||e.e<-p))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function S(e,t,r){var n,i,a,o,u,c,h,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,h=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(u=h/(a=d(10,o-i-1))%10|0,c=t<0||void 0!==v[y+1]||h%a,c=r<4?(u||c)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?i>0?h/d(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return c?(a=w(e),v.length=1,t=t-a-1,v[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=d(10,7-n),v[y]=i>0?(h/d(10,o-i)%d(10,i)|0)*a:0),c)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>p||e.e<-p))throw Error(s+w(e));return e}function A(e,t){var r,n,i,a,o,u,c,s,f,d,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),l?S(t,p):t;if(c=e.d,d=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((f=o<0)?(r=c,o=-o,u=d.length):(r=d,n=s,u=c.length),o>(i=Math.max(Math.ceil(p/7),u)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(u=d.length))&&(u=i),i=0;i<u;i++)if(c[i]!=d[i]){f=c[i]<d[i];break}o=0}for(f&&(r=c,c=d,d=r,t.s=-t.s),u=c.length,i=d.length-u;i>0;--i)c[u++]=0;for(i=d.length;i>o;){if(c[--i]<d[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=d[i]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,l?S(t,p):t):new h(0)}function _(e,t,r){var n,i=w(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+M(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+M(-i-1)+a,r&&(n=r-o)>0&&(a+=M(n))):i>=o?(a+=M(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+M(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=M(n))),e.s<0?"-"+a:a}function E(e,t){if(e.length>t)return e.length=t,!0}function T(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(c+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(c+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return P(this,e.toString())}if("string"!=typeof e)throw Error(c+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,h.test(e))P(this,e);else throw Error(c+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=T,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},9819:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},10656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68179),i=r(99279);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},10921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(27040),i=r(14545),a=r(46200),o=r(64072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,c=r,s=l;if(0===c.length)return s;let e=u;for(let t=0;t<c.length;t++){if(null==e||n.isUnsafeProperty(c[t]))return s;e=e[c[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},11928:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(34890);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},12287:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},12429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},14299:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ii,fb:()=>n3,q:()=>iw,tP:()=>iT,g1:()=>iL,iv:()=>i3,Nk:()=>n4,pM:()=>it,Oz:()=>ib,tF:()=>i4,rj:()=>n1,ec:()=>nX,bb:()=>iM,xp:()=>iI,wL:()=>iA,sr:()=>iC,Qn:()=>iN,MK:()=>n9,IO:()=>nQ,P9:()=>ih,S5:()=>ic,eo:()=>nY,yi:()=>is,D5:()=>iZ,Hd:()=>nW,DP:()=>nq,_y:()=>i7,AV:()=>iS,um:()=>nG,xM:()=>iD,gT:()=>iy,Kr:()=>id,$X:()=>ig});var n,i,a,o,l,u,c,s={};r.r(s),r.d(s,{scaleBand:()=>w,scaleDiverging:()=>function e(){var t=eZ(r6()(eA));return t.copy=function(){return r4(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r6()).domain([.1,1,10]);return t.copy=function(){return r4(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r8,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function e(){var t=e2(r6());return t.copy=function(){return r4(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eP),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eP):[0,1],eZ(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eD();return t.copy=function(){return ek(t,e())},p.apply(t,arguments),eZ(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>ek(t,e()).base(t.base()),p.apply(t,arguments),t},scaleOrdinal:()=>x,scalePoint:()=>O,scalePow:()=>e8,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[z(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(T),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[z(a,e,0,i)]:t}function u(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,u()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},p.apply(eZ(l),arguments)},scaleRadial:()=>function e(){var t,r=eD(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e9(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,eP)).map(e9)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},p.apply(a,arguments),eZ(a)},scaleSequential:()=>function e(){var t=eZ(r2()(eA));return t.copy=function(){return r4(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r4(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r5,scaleSequentialQuantile:()=>function e(){var t=[],r=eA;function n(e){if(null!=e&&!isNaN(e*=1))return r((z(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(T),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=T){if(e===T)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),d=Math.min(i,Math.floor(r+(o-l)*c/o+s));e(t,r,f,d,a)}let o=t[r],l=n,u=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);l<u;){for(tn(t,l,u),++l,--u;0>a(t[l],o);)++l;for(;a(t[u],o)>0;)--u}0===a(t[n],o)?tn(t,n,u):tn(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r3,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r4(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e7,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return ek(t,e()).constant(t.constant())},p.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[z(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},p.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eB});var f=r(68924),d=r(83949),h=r.n(d);function p(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class v extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function x(){var e=new v,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new v,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return x(t,r).unknown(n)},p.apply(i,arguments),i}function w(){var e,t,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),d+=(h-d-e*(r-u))*s,t=e*(1-u),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return w(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},p.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(w.apply(null,arguments).paddingInner(1))}let M=Math.sqrt(50),j=Math.sqrt(10),P=Math.sqrt(2);function S(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=M?10:u>=j?5:u>=P?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?S(e,t,2*r):[n,i,a]}function A(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?S(t,e,r):S(e,t,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)u[e]=-((a-e)/o);else for(let e=0;e<l;++e)u[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)u[e]=-((i+e)/o);else for(let e=0;e<l;++e)u[e]=(i+e)*o;return u}function _(e,t,r){return S(e*=1,t*=1,r*=1)[2]}function E(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?_(t,e,r):_(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function T(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function k(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=T,r=(t,r)=>T(e(t),r),n=(t,r)=>e(t)-r):(t=e===T||e===k?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function D(){return 0}function N(e){return null===e?NaN:+e}let I=C(T),z=I.right;function L(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function $(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function R(){}I.left,C(N).center;var U="\\s*([+-]?\\d+)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",H=/^#([0-9a-f]{3,8})$/,B=RegExp(`^rgb\\(${U},${U},${U}\\)$`),Z=RegExp(`^rgb\\(${K},${K},${K}\\)$`),q=RegExp(`^rgba\\(${U},${U},${U},${F}\\)$`),W=RegExp(`^rgba\\(${K},${K},${K},${F}\\)$`),G=RegExp(`^hsl\\(${F},${K},${K}\\)$`),Y=RegExp(`^hsla\\(${F},${K},${K},${F}\\)$`),V={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function X(){return this.rgb().formatHex()}function J(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=H.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=B.exec(e))?new en(t[1],t[2],t[3],1):(t=Z.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=q.exec(e))?et(t[1],t[2],t[3],t[4]):(t=W.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=G.exec(e))?ec(t[1],t[2]/100,t[3]/100,1):(t=Y.exec(e))?ec(t[1],t[2]/100,t[3]/100,t[4]):V.hasOwnProperty(e)?ee(V[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof R||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function eu(e){return((e=el(e))<16?"0":"")+e.toString(16)}function ec(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function es(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof R||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,u=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=u<.5?a+i:2-a-i,o*=60):l=u>0&&u<1?0:o,new ef(o,l,u,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function eh(e){return Math.max(0,Math.min(1,e||0))}function ep(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}L(R,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:X,formatHex:X,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:J,toString:J}),L(en,er,$(R,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${eu(this.r)}${eu(this.g)}${eu(this.b)}${eu((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),L(ef,function(e,t,r,n){return 1==arguments.length?es(e):new ef(e,t,r,null==n?1:n)},$(R,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(ep(e>=240?e-240:e+120,i,n),ep(e,i,n),ep(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),eh(this.s),eh(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*eh(this.s)}%, ${100*eh(this.l)}%${1===e?")":`, ${e})`}`}}));let ev=e=>()=>e;function eg(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):ev(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?eg:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ev(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=eg(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ey((r-n/t)*t,o,i,a,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,a,o,l)}});function ex(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ew=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ew.source,"g");function eM(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ev(t):("number"===i?ex:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,a,o,l=ew.lastIndex=eO.lastIndex=0,u=-1,c=[],s=[];for(e+="",t+="";(i=ew.exec(e))&&(a=eO.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),c[u]?c[u]+=o:c[++u]=o),(i=i[0])===(a=a[0])?c[u]?c[u]+=a:c[++u]=a:(c[++u]=null,s.push({i:u,x:ex(i,a)})),l=eO.lastIndex;return l<t.length&&(o=t.slice(l),c[u]?c[u]+=o:c[++u]=o),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=eM(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=eM(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ex:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function ej(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eP(e){return+e}var eS=[0,1];function eA(e){return e}function e_(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eE(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=e_(i,n),a=r(o,a)):(n=e_(n,i),a=r(a,o)),function(e){return a(n(e))}}function eT(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=e_(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=z(e,t,1,n)-1;return a[r](i[r](t))}}function ek(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,a,o=eS,l=eS,u=eM,c=eA;function s(){var e,t,r,u=Math.min(o.length,l.length);return c!==eA&&(e=o[0],t=o[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?eT:eE,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,u)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),ex)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,eP),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=ej,s()},f.clamp=function(e){return arguments.length?(c=!!e||eA,s()):c!==eA},f.interpolate=function(e){return arguments.length?(u=e,s()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eD(){return eC()(eA,eA)}var eN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=eN.exec(e)))throw Error("invalid format: "+e);return new ez({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ez(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eL(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function e$(e){return(e=eL(Math.abs(e)))?e[1]:NaN}function eR(e,t){var r=eL(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eI.prototype=ez.prototype,ez.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eU={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eR(100*e,t),r:eR,s:function(e,t){var r=eL(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eL(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eF(e){return e}var eK=Array.prototype.map,eH=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eB(e,t,r,n){var i,l,u,c=E(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(e$(s)/3)))-e$(Math.abs(c))))||(n.precision=u),o(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,e$(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=c)))-e$(i))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-e$(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return a(n)}function eZ(e){var t=e.domain;return e.ticks=function(e){var r=t();return A(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eB(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=_(u,c,r))===n)return a[o]=u,a[l]=c,t(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function eq(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eW(e){return Math.log(e)}function eG(e){return Math.exp(e)}function eY(e){return-Math.log(-e)}function eV(e){return-Math.exp(-e)}function eX(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eJ(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eW,eG),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?eX:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eJ(t),r=eJ(r),e(eY,eV)):e(eW,eG),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=t(u),d=t(c),h=null==e?10:+e,p=[];if(!(o%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),u>0){for(;f<=d;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}}else for(;f<=d;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>c)break;p.push(a)}2*p.length<h&&(p=A(u,c,h))}else p=A(f,d,Math.min(d-f,h)).map(r);return s?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=eI(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(eq(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eZ(r)}function e4(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e5(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e3(e){return e<0?-e*e:e*e}function e6(e){var t=e(eA,eA),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eA,eA):.5===r?e(e5,e3):e(e4(r),e4(1/r)):r},eZ(t)}function e8(){var e=e6(eC());return e.copy=function(){return ek(e,e8()).exponent(e.exponent())},p.apply(e,arguments),e}function e7(){return e8.apply(null,arguments).exponent(.5)}function e9(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eF:(t=eK.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),a.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?eF:(i=eK.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function h(e){var t=(e=eI(e)).fill,r=e.align,i=e.sign,h=e.symbol,p=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):eU[b]||(void 0===g&&(g=12),m=!0,b="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var x="$"===h?o:"#"===h&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===h?l:/[%p]/.test(b)?s:"",O=eU[b],M=/[defgprs%]/.test(b);function j(e){var o,l,s,h=x,j=w;if("c"===b)j=O(e)+j,e="";else{var P=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==i&&(P=!1),h=(P?"("===i?i:f:"-"===i||"("===i?"":i)+h,j=("s"===b?eH[8+n/3]:"")+j+(P&&"("===i?")":""),M){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){j=(46===s?u+e.slice(o+1):e.slice(o))+j,e=e.slice(0,o);break}}}v&&!p&&(e=a(e,1/0));var S=h.length+e.length+j.length,A=S<y?Array(y-S+1).join(t):"";switch(v&&p&&(e=a(A+e,A.length?y-j.length:1/0),A=""),r){case"<":e=h+e+j+A;break;case"=":e=h+A+e+j;break;case"^":e=A.slice(0,S=A.length>>1)+h+e+j+A.slice(S);break;default:e=A+h+e+j}return c(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),j.toString=function(){return e+""},j}return{format:h,formatPrefix:function(e,t){var r=h(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(e$(t)/3))),i=Math.pow(10,-n),a=eH[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tu=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tu.range;let tc=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tc.range;let ts=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let th=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);th.range;let tp=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tp.range;let ty=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tv(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tg=tv(0),tm=tv(1),tb=tv(2),tx=tv(3),tw=tv(4),tO=tv(5),tM=tv(6);function tj(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tg.range,tm.range,tb.range,tx.range,tw.range,tO.range,tM.range;let tP=tj(0),tS=tj(1),tA=tj(2),t_=tj(3),tE=tj(4),tT=tj(5),tk=tj(6);tP.range,tS.range,tA.range,t_.range,tE.range,tT.range,tk.range;let tC=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tD=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tD.range;let tN=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tN.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tN.range;let tI=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tz(e,t,r,n,i,a){let o=[[tu,1,1e3],[tu,5,5e3],[tu,15,15e3],[tu,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(E(t/31536e6,r/31536e6,n));if(0===a)return tl.every(Math.max(E(t,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tL,t$]=tz(tI,tD,tP,ty,td,ts),[tR,tU]=tz(tN,tC,tg,th,tf,tc);function tF(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tK(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tH(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tB={"-":"",_:" ",0:"0"},tZ=/^\s*\d+/,tq=/^%/,tW=/[\\^$*+?|[\]().{}]/g;function tG(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tY(e){return e.replace(tW,"\\$&")}function tV(e){return RegExp("^(?:"+e.map(tY).join("|")+")","i")}function tX(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tJ(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t4(e,t,r){var n=tZ.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t3(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t6(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t8(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=tZ.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tZ.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tZ.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tZ.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=tq.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tZ.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tZ.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ru(e,t){return tG(e.getDate(),t,2)}function rc(e,t){return tG(e.getHours(),t,2)}function rs(e,t){return tG(e.getHours()%12||12,t,2)}function rf(e,t){return tG(1+th.count(tN(e),e),t,3)}function rd(e,t){return tG(e.getMilliseconds(),t,3)}function rh(e,t){return rd(e,t)+"000"}function rp(e,t){return tG(e.getMonth()+1,t,2)}function ry(e,t){return tG(e.getMinutes(),t,2)}function rv(e,t){return tG(e.getSeconds(),t,2)}function rg(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tG(tg.count(tN(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rx(e,t){return e=rb(e),tG(tw.count(tN(e),e)+(4===tN(e).getDay()),t,2)}function rw(e){return e.getDay()}function rO(e,t){return tG(tm.count(tN(e)-1,e),t,2)}function rM(e,t){return tG(e.getFullYear()%100,t,2)}function rj(e,t){return tG((e=rb(e)).getFullYear()%100,t,2)}function rP(e,t){return tG(e.getFullYear()%1e4,t,4)}function rS(e,t){var r=e.getDay();return tG((e=r>=4||0===r?tw(e):tw.ceil(e)).getFullYear()%1e4,t,4)}function rA(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tG(t/60|0,"0",2)+tG(t%60,"0",2)}function r_(e,t){return tG(e.getUTCDate(),t,2)}function rE(e,t){return tG(e.getUTCHours(),t,2)}function rT(e,t){return tG(e.getUTCHours()%12||12,t,2)}function rk(e,t){return tG(1+tp.count(tI(e),e),t,3)}function rC(e,t){return tG(e.getUTCMilliseconds(),t,3)}function rD(e,t){return rC(e,t)+"000"}function rN(e,t){return tG(e.getUTCMonth()+1,t,2)}function rI(e,t){return tG(e.getUTCMinutes(),t,2)}function rz(e,t){return tG(e.getUTCSeconds(),t,2)}function rL(e){var t=e.getUTCDay();return 0===t?7:t}function r$(e,t){return tG(tP.count(tI(e)-1,e),t,2)}function rR(e){var t=e.getUTCDay();return t>=4||0===t?tE(e):tE.ceil(e)}function rU(e,t){return e=rR(e),tG(tE.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function rF(e){return e.getUTCDay()}function rK(e,t){return tG(tS.count(tI(e)-1,e),t,2)}function rH(e,t){return tG(e.getUTCFullYear()%100,t,2)}function rB(e,t){return tG((e=rR(e)).getUTCFullYear()%100,t,2)}function rZ(e,t){return tG(e.getUTCFullYear()%1e4,t,4)}function rq(e,t){var r=e.getUTCDay();return tG((e=r>=4||0===r?tE(e):tE.ceil(e)).getUTCFullYear()%1e4,t,4)}function rW(){return"+0000"}function rG(){return"%"}function rY(e){return+e}function rV(e){return Math.floor(e/1e3)}function rX(e){return new Date(e)}function rJ(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,a,o,l,u,c){var s=eD(),f=s.invert,d=s.domain,h=c(".%L"),p=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(u(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,rJ)):d().map(rX)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:c(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eq(r,e)):s},s.copy=function(){return ek(s,rQ(e,t,r,n,i,a,o,l,u,c))},s}function r0(){return p.apply(rQ(tR,tU,tN,tC,tg,th,tf,tc,tu,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return p.apply(rQ(tL,t$,tI,tD,tP,tp,td,ts,tu,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,l=eA,u=!1;function c(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(eM),c.rangeRound=s(ej),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function r4(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r5(){var e=e6(r2());return e.copy=function(){return r4(e,r5()).exponent(e.exponent())},y.apply(e,arguments)}function r3(){return r5.apply(null,arguments).exponent(.5)}function r6(){var e,t,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=eA,d=!1;function h(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=eM);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,u,c]=o,e=a(l*=1),t=a(u*=1),r=a(c*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h):[l,u,c]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(eM),h.rangeRound=p(ej),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(u),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h}}function r8(){var e=e6(r6());return e.copy=function(){return r4(e,r8()).exponent(e.exponent())},y.apply(e,arguments)}function r7(){return r8.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,u=e.shortMonths,c=tV(i),s=tX(i),f=tV(a),d=tX(a),h=tV(o),p=tX(o),y=tV(l),v=tX(l),g=tV(u),m=tX(u),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:ru,e:ru,f:rh,g:rj,G:rS,H:rc,I:rs,j:rf,L:rd,m:rp,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rY,s:rV,S:rv,u:rg,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rM,Y:rP,Z:rA,"%":rG},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:r_,e:r_,f:rD,g:rB,G:rq,H:rE,I:rT,j:rk,L:rC,m:rN,M:rI,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rY,s:rV,S:rz,u:rL,U:r$,V:rU,w:rF,W:rK,x:null,X:null,y:rH,Y:rZ,Z:rW,"%":rG},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return j(e,t,r,n)},d:t7,e:t7,f:ri,g:t5,G:t4,H:re,I:re,j:t9,L:rn,m:t8,M:rt,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t6,Q:ro,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tJ,W:t2,x:function(e,t,n){return j(e,r,t,n)},X:function(e,t,r){return j(e,n,t,r)},y:t5,Y:t4,Z:t3,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(u,l)),null!=(i=tB[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(e.slice(u,l)),o.join("")}}function M(e,t){return function(r){var n,i,a=tH(1900,void 0,1);if(j(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tK(tH(a.y,0,1))).getUTCDay())>4||0===i?tS.ceil(n):tS(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tF(tH(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=th.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tK(tH(a.y,0,1)).getUTCDay():tF(tH(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tK(a)):tF(a)}}function j(e,t,r,n){for(var i,a,o=0,l=t.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tB?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=M(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=M(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,c=l.utcFormat,l.utcParse;var r9=r(97238),ne=r(39827),nt=r(60356),nr=r(16377),nn=r(78892);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(8870),nl=r.n(no),nu=e=>e,nc={},ns=e=>e===nc,nf=e=>function t(){return 0==arguments.length||1==arguments.length&&ns(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nd=(e,t)=>1===e?t:nf(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nc).length;return a>=e?t(...n):nd(e-a,nf(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>ns(e)?r.shift():e),...r)}))}),nh=e=>nd(e.length,e),np=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},ny=nh((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),nv=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nu;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},ng=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nm=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nb(e){var t;return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nx(e,t,r){for(var n=new(nl())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nh((e,t,r)=>{var n=+e;return n+r*(t-n)}),nh((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nh((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nw=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nO=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nb(e.toNumber()),i=new(nl())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nM=(e,t,r)=>{var n=new(nl())(1),i=new(nl())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(nl())(10).pow(nb(e)-1),i=new(nl())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(nl())(Math.floor(e)))}else 0===e?i=new(nl())(Math.floor((t-1)/2)):r||(i=new(nl())(Math.floor(e)));var o=Math.floor((t-1)/2);return nv(ny(e=>i.add(new(nl())(e-o).mul(n)).toNumber()),np)(0,t)},nj=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nO(new(nl())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(o))).sub(e).div(o).toNumber()),u=Math.ceil(new(nl())(t).sub(i).div(o).toNumber()),c=l+u+1;return c>r?nj(e,t,r,n,a+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(u).mul(o))})},nP=nm(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nw([t,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...np(0,n-1).map(()=>1/0)]:[...np(0,n-1).map(()=>-1/0),l];return t>r?ng(u):u}if(o===l)return nM(o,n,i);var{step:c,tickMin:s,tickMax:f}=nj(o,l,a,i,0),d=nx(s,f.add(new(nl())(.1).mul(c)),c);return t>r?ng(d):d}),nS=nm(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nw([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),u=nO(new(nl())(o).sub(a).div(l-1),i,0),c=[...nx(new(nl())(a),new(nl())(o),u),o];return!1===i&&(c=c.map(e=>Math.round(e))),r>n?ng(c):c}),nA=r(2589),n_=r(96908),nE=r(58573),nT=r(20972),nk=r(18478),nC=r(47062),nD=r(66038),nN=r(12287),nI=r(18190),nz=r(84421);function nL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nL(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nR=[0,"auto"],nU={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},nF=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nU:r},nK={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nR,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nz.tQ},nH=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nK:r},nB={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nZ=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nB:r},nq=(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nH(e,r);case"zAxis":return nZ(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nW=(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nH(e,r);case"angleAxis":return(0,nC.Be)(e,r);case"radiusAxis":return(0,nC.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nG=e=>e.graphicalItems.countOfBars>0;function nY(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nV=(0,f.Mz)([nD.N,nN.E],nY),nX=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nJ=(0,f.Mz)([e=>e.graphicalItems.cartesianItems,nq,nV],nX),nQ=e=>e.filter(e=>void 0===e.stackId),n0=(0,f.Mz)([nJ],nQ),n1=e=>e.map(e=>e.data).filter(Boolean).flat(1),n2=(0,f.Mz)([nJ],n1),n4=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n5=(0,f.Mz)([n2,nt.HS],n4),n3=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n6=(0,f.Mz)([n5,nq,nJ],n3);function n8(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n7(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var n9=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,a,r),graphicalItems:i}]})),ie=(0,f.Mz)([n5,nJ,nk.eC],n9),it=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ir=(0,f.Mz)([ie,nt.LF,nD.N],it),ii=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>n8(n,e)),l=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n7(r.flatMap(r=>{var n,i,a=(0,ne.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),ia=(0,f.Mz)(n5,nq,n0,nD.N,ii);function io(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var il=e=>{var t=n7(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},iu=(e,t,r)=>{var n=e.map(io).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?h()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},ic=e=>{var t;if(null==e||!("domain"in e))return nR;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n7(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:nR},is=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},id=e=>e.referenceElements.dots,ih=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),ip=(0,f.Mz)([id,nD.N,nN.E],ih),iy=e=>e.referenceElements.areas,iv=(0,f.Mz)([iy,nD.N,nN.E],ih),ig=e=>e.referenceElements.lines,im=(0,f.Mz)([ig,nD.N,nN.E],ih),ib=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ix=(0,f.Mz)(ip,nD.N,ib),iw=(e,t)=>{var r=n7(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iO=(0,f.Mz)([iv,nD.N],iw),iM=(e,t)=>{var r=n7(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ij=(0,f.Mz)(im,nD.N,iM),iP=(0,f.Mz)(ix,ij,iO,(e,t,r)=>is(e,r,t)),iS=(0,f.Mz)([nq],ic),iA=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.IH.test(o)){var u=ne.IH.exec(o);if(null==u||null==t)i=void 0;else{var c=+u[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var s=ne.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(ni(d))return null==t?d:na(d,t,r)}}}(t,is(r,i,il(n)),e.allowDataOverflow)},i_=(0,f.Mz)([nq,iS,ir,ia,iP],iA),iE=[0,1],iT=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,c=(0,ne._L)(t,a);return c&&null==l?h()(0,r.length):"category"===u?iu(n,e,c):"expand"===i?iE:o}},ik=(0,f.Mz)([nq,r9.fz,n5,n6,nk.eC,nD.N,i_],iT),iC=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in s?l:"point"}}},iD=(0,f.Mz)([nq,r9.fz,nG,nk.iO,nD.N],iC);function iN(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.Zb)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.YB)(a),a}}}var iI=(e,t,r)=>{var n=ic(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nP(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nS(e,t.tickCount,t.allowDecimals)}},iz=(0,f.Mz)([ik,nW,iD],iI),iL=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,i$=(0,f.Mz)([nq,ik,iz,nD.N],iL),iR=(0,f.Mz)(n6,nq,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n7(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iU=(0,f.Mz)(iR,r9.fz,nk.gY,nE.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),iF=(0,f.Mz)(nF,(e,t)=>{var r=nF(e,t);return null==r||"string"!=typeof r.padding?0:iU(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),iK=(0,f.Mz)(nH,(e,t)=>{var r=nH(e,t);return null==r||"string"!=typeof r.padding?0:iU(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iH=(0,f.Mz)([nE.HZ,iF,nT.U,nT.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),iB=(0,f.Mz)([nE.HZ,r9.fz,iK,nT.U,nT.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iZ=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iH(e,r,n);case"yAxis":return iB(e,r,n);case"zAxis":return null==(i=nZ(e,r))?void 0:i.range;case"angleAxis":return(0,nC.Cv)(e);case"radiusAxis":return(0,nC.Dc)(e,r);default:return}},iq=(0,f.Mz)([nq,iZ],nI.I),iW=(0,f.Mz)([nq,iD,i$,iq],iN);function iG(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nJ,nD.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n8(t,e)));var iY=(e,t)=>t,iV=(e,t,r)=>r,iX=(0,f.Mz)(n_.h,iY,iV,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iG)),iJ=(0,f.Mz)(n_.W,iY,iV,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iG)),iQ=(e,t)=>({width:e.width,height:t.height}),i0=(e,t)=>({width:"number"==typeof t.width?t.width:nz.tQ,height:e.height}),i1=((0,f.Mz)(nE.HZ,nF,iQ),(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}}),i2=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},i4=((0,f.Mz)(nA.A$,nE.HZ,iX,iY,iV,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=iQ(t,r);null==a&&(a=i1(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),(0,f.Mz)(nA.Lp,nE.HZ,iJ,iY,iV,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=i0(t,r);null==a&&(a=i2(t,n,e));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),(0,f.Mz)(nE.HZ,nH,(e,t)=>({width:"number"==typeof t.width?t.width:nz.tQ,height:e.height})),(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,ne._L)(e,n),u=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(u))return u}}),i5=(0,f.Mz)([r9.fz,n6,nq,nD.N],i4),i3=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},i6=(0,f.Mz)([r9.fz,n6,nW,nD.N],i3);(0,f.Mz)([r9.fz,(e,t,r)=>{switch(t){case"xAxis":return nF(e,r);case"yAxis":return nH(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},iD,iW,i5,i6,iZ,iz,nD.N],(e,t,r,n,i,a,o,l,u)=>{if(null==t)return null;var c=(0,ne._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),(0,f.Mz)([r9.fz,nW,iD,iW,iz,iZ,i5,i6,nD.N],(e,t,r,n,i,a,o,l,u)=>{if(null!=t&&null!=n){var c=(0,ne._L)(e,u),{type:s,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===u&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,nr.M8)(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),(0,f.Mz)([r9.fz,nW,iW,iZ,i5,i6,nD.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,o),{tickCount:u}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),(0,f.Mz)(nq,iW,(e,t)=>{if(null!=e&&null!=t)return n$(n$({},e),{},{scale:t})});var i8=(0,f.Mz)([nq,iD,ik,iq],iN);(0,f.Mz)((e,t,r)=>nZ(e,r),i8,(e,t)=>{if(null!=e&&null!=t)return n$(n$({},e),{},{scale:t})});var i7=(0,f.Mz)([r9.fz,n_.h,n_.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},14474:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(52596),i=r(12115),a=r(20400),o=r.n(a),l=r(16377),u=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:c="100%",height:f="100%",minWidth:d=0,minHeight:h,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[M,j]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),P=(0,i.useCallback)((e,t)=>{j(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;P(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return P(r,n),t.observe(w.current),()=>{t.disconnect()}},[P,v]);var S=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=M;if(e<0||t<0)return null;u((0,l._3)(c)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),u(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(c)?e:c,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),u(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,c,f,d,h,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,p,h,d,M,c]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:c,height:f,minWidth:d,minHeight:h,maxHeight:p}),ref:w},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},S))})},14545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},14804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(12429);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},15064:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(12115).createContext)(null)},15160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},16377:(e,t,r)=>{"use strict";r.d(t,{CG:()=>h,Dj:()=>p,Et:()=>u,F4:()=>d,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>c});var n=r(95672),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!o(e),c=e=>u(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},h=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},18190:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},18478:(e,t,r)=>{"use strict";r.d(t,{eC:()=>i,gY:()=>n,hX:()=>l,iO:()=>a,lZ:()=>o,pH:()=>u});var n=e=>e.rootProps.barCategoryGap,i=e=>e.rootProps.stackOffset,a=e=>e.options.chartName,o=e=>e.rootProps.syncId,l=e=>e.rootProps.syncMethod,u=e=>e.options.eventEmitter},19452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},20215:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ea,eE:()=>ec,Xb:()=>eo,A2:()=>ei,yn:()=>es,Dn:()=>j,gL:()=>Y,fl:()=>V,R4:()=>Q,Re:()=>O,n4:()=>T});var n=r(68924),i=r(14299),a=r(97238),o=r(39827),l=r(60356),u=r(18478),c=r(16377),s=r(18190),f=r(96523),d=r(60530),h=r(11928),p=r(60841),y=r(64968),v=r(2589),g=r(58573),m=r(85146),b=r(46670),x=r(75714),w=r(94013),O=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},M=e=>e.tooltip.settings.axisId,j=e=>{var t=O(e),r=M(e);return(0,i.Hd)(e,t,r)},P=(0,n.Mz)([j,a.fz,i.um,u.iO,O],i.sr),S=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),A=(0,n.Mz)([O,M],i.eo),_=(0,n.Mz)([S,j,A],i.ec),E=(0,n.Mz)([_],i.rj),T=(0,n.Mz)([E,l.LF],i.Nk),k=(0,n.Mz)([T,j,_],i.fb),C=(0,n.Mz)([j],i.S5),D=(0,n.Mz)([T,_,u.eC],i.MK),N=(0,n.Mz)([D,l.LF,O],i.pM),I=(0,n.Mz)([_],i.IO),z=(0,n.Mz)([T,j,I,O],i.kz),L=(0,n.Mz)([i.Kr,O,M],i.P9),$=(0,n.Mz)([L,O],i.Oz),R=(0,n.Mz)([i.gT,O,M],i.P9),U=(0,n.Mz)([R,O],i.q),F=(0,n.Mz)([i.$X,O,M],i.P9),K=(0,n.Mz)([F,O],i.bb),H=(0,n.Mz)([$,K,U],i.yi),B=(0,n.Mz)([j,C,N,z,H],i.wL),Z=(0,n.Mz)([j,a.fz,T,k,u.eC,O,B],i.tP),q=(0,n.Mz)([Z,j,P],i.xp),W=(0,n.Mz)([j,Z,q,O],i.g1),G=e=>{var t=O(e),r=M(e);return(0,i.D5)(e,t,r,!1)},Y=(0,n.Mz)([j,G],s.I),V=(0,n.Mz)([j,P,W,Y],i.Qn),X=(0,n.Mz)([a.fz,k,j,O],i.tF),J=(0,n.Mz)([a.fz,k,j,O],i.iv),Q=(0,n.Mz)([a.fz,j,P,V,G,X,J,O],(e,t,r,n,i,a,l,u)=>{if(t){var{type:s}=t,f=(0,o._L)(e,u);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/d:0;return(h="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,c.sA)(i[0]-i[1])*h:h,f&&l)?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:a?a[e]:e,index:t,offset:h}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([x.J,ee,et,er],h.i),ei=(0,n.Mz)([en,T],p.P),ea=(0,n.Mz)([Q,ei],d.E),eo=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([x.J,ee,et,er],m.q),eu=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,Q,er,el,b.x],y.o),ec=(0,n.Mz)([en,eu],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),es=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,j,ea,b.x,ee],w.N);(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},20241:(e,t,r)=>{e.exports=r(22434).sortBy},20400:(e,t,r)=>{e.exports=r(82962).throttle},20972:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>u});var n=r(68924),i=r(58573),a=r(2589),o=r(16377),l=e=>e.brush,u=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},22188:(e,t,r)=>{e.exports=r(85252).isEqual},22248:(e,t,r)=>{"use strict";r.d(t,{As:()=>f,TK:()=>d,iZ:()=>h});var n=r(5710),i=r(74532),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:u,replaceCartesianGraphicalItem:c,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:d}=a.actions,h=a.reducer},22434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(47064),i=r(55998),a=r(64373);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},22436:(e,t,r)=>{"use strict";var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},22520:(e,t,r)=>{"use strict";var n=r(49641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1147),a=r(98221),o=r(15160),l=r(42721),u=r(83616);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,f,d,h){let p=h(t,r,c,s,f,d);if(void 0!==p)return p;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,f){if(Object.is(r,c))return!0;let d=o.getTag(r),h=o.getTag(c);if(d===l.argumentsTag&&(d=l.objectTag),h===l.argumentsTag&&(h=l.objectTag),d!==h)return!1;switch(d){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let e=r.valueOf(),t=c.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let p=(s=s??new Map).get(r),y=s.get(c);if(null!=p&&null!=y)return p===c;s.set(r,c),s.set(c,r);try{switch(d){case l.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,f))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(t(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!e(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,d,h)}(e,t,void 0,void 0,void 0,void 0,r)}},23676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},24021:(e,t,r)=>{"use strict";r.d(t,{m:()=>eo});var n=r(12115),i=r(47650),a=r(20241),o=r.n(a),l=r(52596),u=r(16377);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,u.vh)(e[0])&&(0,u.vh)(e[1])?e.join(" ~ "):e}var h=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:h,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,u.uy)(g),M=O?g:"",j=(0,l.$)("recharts-default-tooltip",y),P=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(M=m(g,s)),n.createElement("div",c({className:j,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:P,style:w},n.isValidElement(M)?M:"".concat(M)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||h||d,{value:o,name:l}=e,c=o,p=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[c,p]=y;else{if(null==y)return null;c=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,u.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&(0,u.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var h=c[n];return null==h?0:o[n]?f<h?Math.max(d,h):Math.max(f,h):null==s?0:d+l>h+s?Math.max(f,h):Math.max(d,h)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:s,offset:f,position:d,reverseDirection:h,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:M}=this.props,{cssClasses:j,cssProperties:P}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:f,useTranslate3d:d,viewBox:h}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.width,viewBox:h,viewBoxDimension:h.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.height,viewBox:h,viewBoxDimension:h.height}),useTranslate3d:d}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(p,{["".concat(p,"-right")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r>=t.x,["".concat(p,"-left")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r<t.x,["".concat(p,"-bottom")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n>=t.y,["".concat(p,"-top")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:d,reverseDirection:h,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),S=M?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},P),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),A=m(m({},S),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:j,style:A,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var w=r(41643),O=r(60512),M=r.n(O),j=r(97238),P=r(96752),S=r(70688),A=r(70788),_=["x","y","top","left","width","height","className"];function E(){return(E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var k=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),C=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:c=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:c},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,_));return(0,u.Et)(t)&&(0,u.Et)(r)&&(0,u.Et)(o)&&(0,u.Et)(c)&&(0,u.Et)(i)&&(0,u.Et)(a)?n.createElement("path",E({},(0,A.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:k(t,r,o,c,i,a)})):null},D=r(44538),N=r(25641);function I(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,N.IZ)(t,r,n,i),(0,N.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var z=r(77283),L=r(81971),$=r(39827),R=r(20215);function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var K=()=>(0,L.G)(R.Dn),H=()=>{var e=K(),t=(0,L.G)(R.R4),r=(0,L.G)(R.fl);return(0,$.Hj)(F(F({},e),{},{scale:r}),t)},B=r(94732);function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e){var t,r,i,{coordinate:a,payload:o,index:u,offset:c,tooltipAxisBandSize:s,layout:f,cursor:d,tooltipEventType:h,chartName:p}=e;if(!d||!a||"ScatterChart"!==p&&"axis"!==h)return null;if("ScatterChart"===p)r=a,i=C;else if("BarChart"===p)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:c.left+.5,y:"horizontal"===f?c.top+.5:a.y-t,width:"horizontal"===f?s:c.width-1,height:"horizontal"===f?c.height-1:s},i=D.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=I(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=z.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return I(t);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=t,d=(0,N.IZ)(l,u,c,f),h=(0,N.IZ)(l,u,s,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,c)},i=S.I;var x="object"==typeof d&&"className"in d?d.className:void 0,w=W(W(W(W({stroke:"#ccc",pointerEvents:"none"},c),r),(0,A.J9)(d,!1)),{},{payload:o,payloadIndex:u,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,w):(0,n.createElement)(i,w)}function Y(e){var t=H(),r=(0,j.W7)(),i=(0,j.WX)(),a=(0,B.fW)();return n.createElement(G,Z({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var V=r(25115),X=r(34890),J=r(46850),Q=r(96523),ee=r(93389);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function en(e){return e.dataKey}var ei=[],ea={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function eo(e){var t,r,a=(0,ee.e)(e,ea),{active:o,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:f,isAnimationActive:d,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:S,defaultIndex:A,portal:_,axisId:E}=a,T=(0,L.j)(),k="number"==typeof A?String(A):A;(0,n.useEffect)(()=>{T((0,X.UF)({shared:O,trigger:S,axisId:E,active:o,defaultIndex:k}))},[T,O,S,E,o,k]);var C=(0,j.sk)(),D=(0,P.$)(),N=(0,Q.Td)(O),{activeIndex:I,isActive:z}=(0,L.G)(e=>(0,B.yn)(e,N,S,k)),$=(0,L.G)(e=>(0,B.u9)(e,N,S,k)),R=(0,L.G)(e=>(0,B.BZ)(e,N,S,k)),U=(0,L.G)(e=>(0,B.dS)(e,N,S,k)),F=(0,V.X)(),K=null!=o?o:z,[H,Z]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([$,K]),q="axis"===N?R:void 0;(0,J.m7)(N,S,U,q,I,K);var W=null!=_?_:F;if(null==W)return null;var G=null!=$?$:ei;K||(G=ei),f&&G.length&&(t=$.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),G=!0===y?M()(t,en):"function"==typeof y?M()(t,y):t);var et=G.length>0,eo=n.createElement(x,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:d,active:K,coordinate:U,hasPayload:et,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:C,wrapperStyle:b,lastBoundingBox:H,innerRef:Z,hasPortalFromProps:!!_},(r=er(er({},a),{},{payload:G,label:q,active:K,coordinate:U,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(h,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,W),K&&n.createElement(Y,{cursor:w,tooltipEventType:N,coordinate:U,payload:$,index:I}))}},24517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(46200),a=r(37298),o=r(10921),l=r(93205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},25115:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(12115),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},25641:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>l,Kg:()=>a,lY:()=>u,yy:()=>h}),r(12115);var a=Math.PI/180,o=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),u=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},c=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,l=c({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var u=Math.acos((r-i)/l);return n>a&&(u=2*Math.PI-u),{radius:l,angle:o(u),angleInRadian:u}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},d=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},h=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=s({x:n,y:a},t),{innerRadius:u,outerRadius:c}=t;if(o<u||o>c||0===o)return null;var{startAngle:h,endAngle:p}=f(t),y=l;if(h<=p){for(;y>p;)y-=360;for(;y<h;)y+=360;r=y>=h&&y<=p}else{for(;y>h;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=h}return r?i(i({},t),{},{radius:o,angle:d(y,t)}):null}},27040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},29738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117),i=r(42721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let u=t?.(r,a,o,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},30294:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case f:case a:case l:case o:case h:return e;default:switch(e=e&&e.$$typeof){case c:case d:case y:case p:case u:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return v(e)===a}},31847:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,u=r-e,c=i-t,s=o-e,f=l-t,d=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*u-c*s)>1e-6&&a){let h=r-o,p=i-l,y=u*u+c*c,v=Math.sqrt(y),g=Math.sqrt(d),m=a*Math.tan((n-Math.acos((y+d-(h*h+p*p))/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*h>s*p)},${this._x1=e+x*u},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,u){if(e*=1,t*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let c=r*Math.cos(o),s=r*Math.sin(o),f=e+c,d=t+s,h=1^u,p=u?o-l:l-o;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${h},${e-c},${t-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=d}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${h},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},32634:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>u,u3:()=>c});var n=r(5710),i=r(74532),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:c}=a.actions,s=a.reducer},34487:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(5710).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},34890:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>h,Nt:()=>p,RD:()=>s,UF:()=>c,XB:()=>u,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>d});var n=r(5710),i=r(74532),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:h,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},36633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},37298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},39827:(e,t,r)=>{"use strict";r.d(t,{qx:()=>_,IH:()=>A,s0:()=>b,gH:()=>m,SW:()=>N,YB:()=>w,bk:()=>D,Hj:()=>E,Mk:()=>S,yy:()=>M,GF:()=>T,uM:()=>k,kr:()=>g,r4:()=>C,_L:()=>x});var n=r(20241),i=r.n(n),a=r(95672),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(9819),c=r(85654);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var h=r(16377),p=r(25641);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,h.uy)(e)||(0,h.uy)(t)?r:(0,h.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,d=void 0;if((0,h.sA)(s-c)!==(0,h.sA)(f-s)){var p=[];if((0,h.sA)(f-s)===(0,h.sA)(i[1]-i[0])){d=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+c)/2),p[1]=Math.max(y,(y+c)/2)}else{d=c;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=p[0]&&e<=p[1]){({index:o}=r[u]);break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[u]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,h.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,h.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,w=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},O={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,h.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,u=0;o<r;++o)u+=e[o][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var u=0,c=0,s=0;u<i;++u){for(var f=e[t[u]],d=f[o][1]||0,h=(d-(f[o-1][1]||0))/2,p=0;p<u;++p){var y=e[t[p]];h+=(y[o][1]||0)-(y[o-1][1]||0)}c+=d,s+=h*d}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,h.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},M=(e,t,r)=>{var n=O[r];return(function(){var e=(0,c.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),d),c=l.length,s=-1;for(let e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,u.A)(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,c.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,c.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,c.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)},j=e=>{var t=e.flat(2).filter(h.Et);return[Math.min(...t),Math.max(...t)]},P=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],S=(e,t,r)=>{if(null!=e)return P(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=j(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},A=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,E=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,u=a.length;l<u;l++){var c=a[l],s=a[l-1];o=Math.min((c.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function T(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function k(e,t){return e?String(e):"string"==typeof t?t:void 0}function C(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var D=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},N=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},41643:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},42694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},42721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},43597:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>s,_U:()=>u,j2:()=>l});var n=r(12115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},c=(e,t,r)=>n=>(e(t,r,n),null),s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=c(a,t,r))}),n}},44117:(e,t,r)=>{"use strict";var n=r(49641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(98221),a=r(15160),o=r(42721),l=r(36633),u=r(80885);function c(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(null!=d)return d;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,c(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(c(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},44538:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(12115),i=r(52596),a=r(70788),o=r(93389),l=r(74460);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),a+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),a+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+u*d,",").concat(t,"\n            L ").concat(e+r-u*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-u*d,",").concat(t+n,"\n            L ").concat(e+u*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:h,y:p,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:M}=t;if(h!==+h||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var j=(0,i.$)("recharts-rectangle",m);return M?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:h,y:p},to:{width:y,height:v,x:h,y:p},duration:x,animationEasing:b,isActive:M},e=>{var{width:i,height:o,x:s,y:d}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",u({},(0,a.J9)(t,!0),{className:j,d:c(s,d,i,o,g),ref:r})))}):n.createElement("path",u({},(0,a.J9)(t,!0),{className:j,d:c(h,p,y,v,g)}))}},45643:(e,t,r)=>{"use strict";e.exports=r(6115)},46200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},46641:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>a});var n=r(5710),i=r(16377);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:u}=o.actions},46670:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},46850:(e,t,r)=>{"use strict";r.d(t,{l3:()=>g,m7:()=>m});var n=r(12115),i=r(81971),a=r(18478),o=new(r(82661)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",c=r(46641),s=r(34890),f=r(94732),d=r(20215);function h(e){return e.tooltip.syncInteraction}var p=r(97238),y=r(34487),v=()=>{};function g(){var e,t,r,f,h,g,m,b,x,w,O,M=(0,i.j)();(0,n.useEffect)(()=>{M((0,c.dl)())},[M]),e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),h=(0,i.G)(d.R4),g=(0,p.WX)(),m=(0,p.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f)return void r(i);if(null!=h){if("function"==typeof f){var o,l=f(h,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=h[l]}else"value"===f&&(o=h.find(e=>String(e.value)===i.payload.label));var{coordinate:u}=i.payload;if(null==o||!1===i.payload.active||null==u||null==m)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:c,y:d}=u,p=Math.min(c,m.x+m.width),y=Math.min(d,m.y+m.height),v={x:"horizontal"===g?o.coordinate:p,y:"horizontal"===g?y:o.coordinate};r((0,s.E1)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,t,e,f,h,g,m]),x=(0,i.G)(a.lZ),w=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==x)return v;var e=(e,t,r)=>{w!==r&&x===e&&O((0,y.M)(t))};return o.on(u,e),()=>{o.off(u,e)}},[O,w,x])}function m(e,t,r,u,c,d){var p=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(h),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var e=(0,s.E1)({active:d,coordinate:r,dataKey:p,index:c,label:"number"==typeof u?String(u):u});o.emit(l,v,e,y)}},[b,r,p,c,u,y,v,g,d])}},47062:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>j,Gl:()=>g,Dc:()=>M});var n=r(68924),i=r(2589),a=r(58573),o=r(25641),l=r(16377),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},c={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(18190),f=r(97238),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},h={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:c.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:d,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:h,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var M=(0,n.Mz)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,M],s.I);var j=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:u,startAngle:c,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(u,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},47064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55181),i=r(51551),a=r(64072);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>u(t,e))})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},49033:(e,t,r)=>{"use strict";e.exports=r(22436)},49901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64373),i=r(64664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},50177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},50330:(e,t,r)=>{"use strict";e.exports=r(30294)},51551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},54811:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},55181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},55998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},58573:(e,t,r)=>{"use strict";r.d(t,{HZ:()=>y,Ds:()=>v});var n=r(68924),i=r(95672),a=r.n(i),o=r(20241),l=r.n(o),u=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,u],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var c=r(39827),s=r(2589),f=r(96908),d=r(84421);function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,u,e=>e.legend.size],(e,t,r,n,i,o,l,u)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:d.tQ;return p(p({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:p(p({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),h=p(p({},f),s),y=h.bottom;h.bottom+=n;var v=e-(h=(0,c.s0)(h,l,u)).left-h.right,g=t-h.top-h.bottom;return p(p({brushBottom:y},h),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height}));(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},60356:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(68924),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},60512:(e,t,r)=>{e.exports=r(7547).uniqBy},60530:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(16377),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},60841:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(78892),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},62194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921);t.property=function(e){return function(t){return n.get(t,e)}}},64072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},64373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98412),i=r(68179),a=r(82384),o=r(83616);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},64664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},64968:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},66038:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},68179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(19452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},68500:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},68924:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(e,t=l){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function l(e,t){return e===t}function u(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var c=(e,t)=>!1;function s(){return function(e,t=l){return new o(null,t)}(0,c)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),u(t)};Symbol();var d=0,h=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=d++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in h)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new v(e):new p(e)}(n)),r.tag&&u(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),u(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=d++},g={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(e,t={}){let r,n=b(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}}let u=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return u.s=1,u.v=t,t}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:c=[],argsMemoize:s=x,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},h=n(c),p=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=u(function(){return i++,l.apply(null,arguments)},...h);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>O})},70688:(e,t,r)=>{"use strict";r.d(t,{I:()=>H});var n=r(12115);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class c{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function h(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=h(this,e,t)),r);break;default:y(this,this._t0,r=h(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(9819),M=r(85654),j=r(31847);function P(e){return e[0]}function S(e){return e[1]}function A(e,t){var r=(0,M.A)(!0),n=null,i=d,a=null,o=(0,j.i)(l);function l(l){var u,c,s,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(c,u,l),+t(c,u,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?P:(0,M.A)(e),t="function"==typeof t?t:void 0===t?S:(0,M.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,M.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function _(e,t,r){var n=null,i=(0,M.A)(!0),a=null,o=d,l=null,u=(0,j.i)(c);function c(c){var s,f,d,h,p,y=(c=(0,O.A)(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=u())),s=0;s<=y;++s){if(!(s<y&&i(h=c[s],s,c))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=s-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(h,s,c),m[s]=+t(h,s,c),l.point(n?+n(h,s,c):g[s],r?+r(h,s,c):m[s]))}if(p)return l=null,p+""||null}function s(){return A().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?P:(0,M.A)(+e),t="function"==typeof t?t:void 0===t?(0,M.A)(0):(0,M.A)(+t),r="function"==typeof r?r:void 0===r?S:(0,M.A)(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,M.A)(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,M.A)(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,M.A)(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,M.A)(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,M.A)(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}var E=r(52596),T=r(43597),k=r(70788),C=r(16377),D=r(78892);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new c(e,!0)},curveBumpY:function(e){return new c(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:d,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},$=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),R=e=>e.x,U=e=>e.y,F=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||d},K=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=F(r,a),u=o?n.filter($):n;if(Array.isArray(i)){var c=o?i.filter(e=>$(e)):i,s=u.map((e,t)=>z(z({},e),{},{base:c[t]}));return(t="vertical"===a?_().y(U).x1(R).x0(e=>e.base.x):_().x(R).y1(U).y0(e=>e.base.y)).defined($).curve(l),t(s)}return(t="vertical"===a&&(0,C.Et)(i)?_().y(U).x1(R).x0(i):(0,C.Et)(i)?_().x(R).y1(U).y0(i):A().x(R).y(U)).defined($).curve(l),t(u)},H=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?K(e):i;return n.createElement("path",N({},(0,k.J9)(e,!1),(0,T._U)(e),{className:(0,E.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},70788:(e,t,r)=>{"use strict";r.d(t,{J9:()=>y,aS:()=>h});var n=r(95672),i=r.n(n),a=r(12115),o=r(50330),l=r(16377),u=r(43597),c=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,d=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,s=e,t};function h(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>c(e)):[c(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var p=(e,t,r,n)=>{var i,a=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||u.QQ.includes(t))||r&&u.j2.includes(t)},y=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;p(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},71807:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(12115),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},72465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},72744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(82384),a=r(36633),o=r(83616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return c(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let c=e[u],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,u){let c=r(t,n,i,a,o,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},t.isSetMatch=c},74460:(e,t,r)=>{"use strict";r.d(t,{i:()=>D});var n=r(12115),i=r(22188),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>l(o(e,t),r),c=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),s=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var s=u(e,t),f=u(r,n),d=c(e,t),h=e=>e>1?1:e<0?0:e,p=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=d(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=h(r-i/a)}return f(r)};return p.isStepper=!1,p},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},d=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null};function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),v=(e,t,r)=>e.map(e=>"".concat(y(e)," ").concat(t,"ms ").concat(r)).join(","),g=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),m=(e,t)=>Object.keys(t).reduce((r,n)=>p(p({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},M=(e,t,r)=>{var n=m((t,r)=>{if(O(r)){var[n,i]=e(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},t);return r<1?m((e,t)=>O(t)?x(x({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):M(e,n,r-1)};let j=(e,t,r,n,i,a)=>{var o=g(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>x(x({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),u=()=>m((e,t)=>t.from,l),c=()=>!Object.values(l).filter(O).length,s=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=M(r,l,d),i(x(x(x({},e),t),u())),o=n,c()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,u=null,c=i.reduce((r,n)=>x(x({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,d=m((e,t)=>w(...t,r(f)),c);if(a(x(x(x({},e),t),d)),f<1)u=o.setTimeout(s);else{var h=m((e,t)=>w(...t,r(1)),c);a(x(x(x({},e),t),h))}};return()=>(u=o.setTimeout(s),()=>{u()})}(e,t,r,n,o,i,a)};class P{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var S=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function A(){return(A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){T(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function T(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class k extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:u}=this.state;if(r){if(!t){this.state&&u&&(n&&u[n]!==o||!n&&u!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?l:e.to;this.state&&u&&(n&&u[n]!==s||!n&&u!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(E(E({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,u=j(t,r,d(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=u()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof u||"spring"===a)return void this.runJSAnimation(e);var c=n?{[n]:i}:i,s=v(Object.keys(c),r,a);this.manager.start([o,t,E(E({},c),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,S),v=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,E(E({},y),{},{style:E(E({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),T(this,"mounted",!1),T(this,"manager",null),T(this,"stopJSAnimation",null),T(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}T(k,"displayName","Animate"),T(k,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function D(e){var t,r,i,a,o,l,u,c,s=(0,n.useContext)(C);return n.createElement(k,A({},e,{animationManager:null!=(u=null!=(c=e.animationManager)?c:s)?u:(t=new P,i=()=>null,a=!1,o=null,l=e=>{if(!a){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&i(e),"function"==typeof e&&e()}},{stop:()=>{a=!0},start:e=>{a=!1,o&&(o(),o=null),l(e)},subscribe:e=>(i=e,()=>{i=()=>null}),getTimeoutController:()=>t})}))}},74532:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>c,a6:()=>s,h4:()=>Z,jM:()=>B,ss:()=>K});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function c(e){return!!e&&!!e[o]}function s(e){return!!e&&(d(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=u(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function h(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function x(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=u(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(u(e),t)}}function w(e,t=!1){return M(e)||c(e)||!s(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>w(t,!0))),e}function O(){l(2)}function M(e){return Object.isFrozen(e)}var j={};function P(e){let t=j[e];return t||l(0,e),t}function S(e,t){t&&(P("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function A(e){_(e),e.drafts_.forEach(T),e.drafts_=null}function _(e){e===n&&(n=e.parent_)}function E(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function T(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function k(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(A(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&P("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),A(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(M(t))return t;let n=t[o];if(!n)return h(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),h(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&P("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(c(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!c(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!M(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var I={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=$(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===L(e.base_,t)?(U(e),e.copy_[t]=F(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=$(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=L(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;U(e),R(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,U(e),R(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>u(e.base_),setPrototypeOf(){l(12)}},z={};function L(e,t){let r=e[o];return(r?b(r):e)[t]}function $(e,t){if(!(t in e))return;let r=u(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=u(r)}}function R(e){!e.modified_&&(e.modified_=!0,e.parent_&&R(e.parent_))}function U(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function F(e,t){let r=g(e)?P("MapSet").proxyMap_(e,t):m(e)?P("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:u}=Proxy.revocable(a,o);return i.draft_=u,i.revoke_=l,u}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function K(e){return c(e)||l(10,e),function e(t){let r;if(!s(t)||M(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return h(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}h(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var H=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=E(this),a=F(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?A(i):_(i)}return S(i,r),k(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let t=[],i=[];P("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),c(e)&&(e=K(e));let t=E(this),r=F(e,void 0);return r[o].isManual_=!0,_(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return S(n,t),k(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=P("Patches").applyPatches_;return c(e)?n(e,t):this.produce(e,e=>n(e,t))}},B=H.produce;function Z(e){return e}H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H)},75714:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},77283:(e,t,r)=>{"use strict";r.d(t,{h:()=>y});var n=r(12115),i=r(52596),a=r(70788),o=r(25641),l=r(16377),u=r(93389);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:u,cornerIsExternal:c}=e,s=u*(l?1:-1)+n,f=Math.asin(u/s)/o.Kg,d=c?i:i+a*f,h=(0,o.IZ)(t,r,s,d);return{center:h,circleTangency:(0,o.IZ)(t,r,n,d),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),c?i-a*f:i),theta:f}},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=e,u=s(a,l),c=a+u,f=(0,o.IZ)(t,r,i,a),d=(0,o.IZ)(t,r,i,c),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(a>c),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var p=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,c);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(a<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(t,",").concat(r," Z");return h},h=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:u,startAngle:c,endAngle:s}=e,h=(0,l.sA)(s-c),{circleTangency:p,lineTangency:y,theta:v}=f({cx:t,cy:r,radius:i,angle:c,sign:h,cornerRadius:a,cornerIsExternal:u}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:s,sign:-h,cornerRadius:a,cornerIsExternal:u}),x=u?Math.abs(c-s):Math.abs(c-s)-v-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):d({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(h<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:M,theta:j}=f({cx:t,cy:r,radius:n,angle:c,sign:h,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),{circleTangency:P,lineTangency:S,theta:A}=f({cx:t,cy:r,radius:n,angle:s,sign:-h,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),_=u?Math.abs(c-s):Math.abs(c-s)-j-A;if(_<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(S.x,",").concat(S.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(h>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(h<0),",").concat(M.x,",").concat(M.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w},p={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=e=>{var t,r=(0,u.e)(e,p),{cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<f||b===x)return null;var O=(0,i.$)("recharts-sector",w),M=y-f,j=(0,l.F4)(v,M,0,!0);return t=j>0&&360>Math.abs(b-x)?h({cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(j,M/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):d({cx:o,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",c({},(0,a.J9)(r,!0),{className:O,d:t}))}},78673:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,u=null,{leading:c=!1,trailing:s=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(l=e,u=setTimeout(b,t),c&&null!==i)?p(e):n,v=e=>(u=null,s&&null!==i)?p(e):n,g=e=>{if(null===o)return!0;let r=e-o,n=d&&e-l>=h;return r>=t||r<0||n},m=e=>{let r=t-(null===o?0:e-o),n=h-(e-l);return d?Math.min(r,n):r},b=()=>{let e=Date.now();if(g(e))return v(e);u=setTimeout(b,m(e))},x=function(...e){let r=Date.now(),l=g(r);if(i=e,a=this,o=r,l){if(null===u)return y(r);if(d)return clearTimeout(u),u=setTimeout(b,t),p(r)}return null===u&&(u=setTimeout(b,t)),n};return x.cancel=()=>{null!==u&&clearTimeout(u),l=0,o=i=a=u=null},x.flush=()=>null===u?n:v(Date.now()),x}},78892:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},80885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},80931:(e,t,r)=>{e.exports=r(86006).isPlainObject},81571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72465),i=r(62194),a=r(14804),o=r(24517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},81971:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(45643),i=r(12115),a=r(15064),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},u=()=>{},c=()=>u,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:c,t?t.store.getState:u,t?t.store.getState:u,t?e:u,s)}},82384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},82661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var d,h=s.length;for(c=0;c<h;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,i);break;default:if(!u)for(d=1,u=Array(f-1);d<f;d++)u[d-1]=arguments[d];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},82962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(78673);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},83616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},83949:(e,t,r)=>{e.exports=r(49901).range},84421:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},85146:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},85252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(22520),i=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},85654:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},86006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},93205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14545),i=r(98412),a=r(50177),o=r(64072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},93389:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},94013:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(16377),i=r(39827);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,a,l,u,c)=>{if(null!=t&&null!=u){var{chartData:s,computedData:f,dataStartIndex:d,dataEndIndex:h}=r;return e.reduce((e,r)=>{var p,y,v,g,m,{dataDefinedOnItem:b,settings:x}=r,w=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((p=b,y=s,null!=p?p:y),d,h),O=null!=(v=null==x?void 0:x.dataKey)?v:null==a?void 0:a.dataKey,M=null==x?void 0:x.nameKey;return Array.isArray(g=null!=a&&a.dataKey&&Array.isArray(w)&&!Array.isArray(w[0])&&"axis"===c?(0,n.eP)(w,a.dataKey,l):u(w,t,f,M))?g.forEach(t=>{var r=o(o({},x),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:x,dataKey:O,payload:g,value:(0,i.kr)(g,O),name:null!=(m=(0,i.kr)(g,M))?m:null==x?void 0:x.name})),e},[])}}},94732:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>z,dS:()=>C,dp:()=>E,fW:()=>O,pg:()=>k,r1:()=>S,u9:()=>N,yn:()=>I});var n=r(68924),i=r(20241),a=r.n(i),o=r(81971),l=r(39827),u=r(60356),c=r(20215),s=r(18478),f=r(97238),d=r(58573),h=r(2589),p=r(60530),y=r(11928),v=r(60841),g=r(64968),m=r(85146),b=r(46670),x=r(75714),w=r(94013),O=()=>(0,o.G)(s.iO),M=(e,t)=>t,j=(e,t,r)=>r,P=(e,t,r,n)=>n,S=(0,n.Mz)(c.R4,e=>a()(e,e=>e.coordinate)),A=(0,n.Mz)([x.J,M,j,P],y.i),_=(0,n.Mz)([A,c.n4],v.P),E=(e,t,r)=>{if(null!=t){var n=(0,x.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},T=(0,n.Mz)([x.J,M,j,P],m.q),k=(0,n.Mz)([h.Lp,h.A$,f.fz,d.HZ,c.R4,P,T,b.x],g.o),C=(0,n.Mz)([A,k],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(c.R4,_,p.E),N=(0,n.Mz)([T,_,u.LF,c.Dn,D,b.x,M],w.N),I=(0,n.Mz)([A],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,u)=>{if(e&&t&&n&&i&&a){var c=(0,l.r4)(e.chartX,e.chartY,t,r,u);if(c){var s=(0,l.SW)(c,t),f=(0,l.gH)(s,o,a,n,i),d=(0,l.bk)(t,a,f,c);return{activeIndex:String(f),activeCoordinate:d}}}}},95672:(e,t,r)=>{e.exports=r(10921).get},96523:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>u,au:()=>l,xH:()=>i});var n=r(81971),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function u(e){return(0,n.G)(t=>l(t,e))}},96752:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(81971),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},96908:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(68924),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},97238:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>p,fz:()=>h,rY:()=>d,sk:()=>u,yi:()=>f}),r(12115);var n=r(81971),i=r(58573),a=r(2589),o=r(71807),l=r(20972),u=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),u=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&u?{width:a.width-u.left-u.right,height:a.height-u.top-u.bottom,x:u.left,y:u.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:c},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),h=e=>e.layout.layoutType,p=()=>(0,n.G)(h)},98132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},98221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},98412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},99279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}}}]);