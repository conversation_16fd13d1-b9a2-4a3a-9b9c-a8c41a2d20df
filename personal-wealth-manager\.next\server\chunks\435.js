"use strict";exports.id=435,exports.ids=[435],exports.modules={252:(e,t,r)=>{r.d(t,{L:()=>s});var n=r(43210),i=r(48143);function s(){let[e]=(0,n.useState)(i.e);return e}},3567:(e,t,r)=>{var n=r(43210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=n.useSyncExternalStore,o=n.useRef,a=n.useEffect,l=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,d){var c=o(null);if(null===c.current){var h={hasValue:!1,value:null};c.current=h}else h=c.current;var p=s(e,(c=l(function(){function e(e){if(!a){if(a=!0,s=e,e=n(e),void 0!==d&&h.hasValue){var t=h.value;if(d(t,e))return o=t}return o=e}if(t=o,i(s,e))return t;var r=n(e);return void 0!==d&&d(t,r)?(s=e,t):(s=e,o=r)}var s,o,a=!1,l=void 0===r?null:r;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,r,n,d]))[0],c[1]);return a(function(){h.hasValue=!0,h.value=p},[p]),u(p),p}},5748:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},6895:(e,t,r)=>{e.exports=r(3567)},7044:(e,t,r)=>{r.d(t,{B:()=>n});let n="undefined"!=typeof window},10327:(e,t,r)=>{r.d(t,{Y:()=>s});var n=r(43210),i=r(52315);function s(e){let t=(0,n.useRef)(e);return(0,i.s)(()=>{t.current=e},[e]),t}},11860:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12157:(e,t,r)=>{r.d(t,{L:()=>n});let n=(0,r(43210).createContext)({})},12941:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13337:(e,t,r)=>{r.d(t,{x:()=>n});function n(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},13516:(e,t,r)=>{r.d(t,{e:()=>D,_:()=>C});var n,i,s=r(43210),o=r(252),a=r(52263),l=r(84818),u=r(52315),d=r(10327),c=r(15319),h=r(44967),p=r(48143);"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(n=null==process?void 0:process.env)?void 0:n.NODE_ENV)==="test"&&void 0===(null==(i=null==Element?void 0:Element.prototype)?void 0:i.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var m=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(m||{}),f=r(39857),g=r(13337),v=r(44685),y=r(69334);function b(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:E)!==s.Fragment||1===s.Children.count(e.children)}let x=(0,s.createContext)(null);x.displayName="TransitionContext";var w=(e=>(e.Visible="visible",e.Hidden="hidden",e))(w||{});let k=(0,s.createContext)(null);function P(e){return"children"in e?P(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function T(e,t){let r=(0,d.Y)(e),n=(0,s.useRef)([]),i=(0,l.a)(),u=(0,o.L)(),c=(0,a._)((e,t=y.mK.Hidden)=>{let s=n.current.findIndex(({el:t})=>t===e);-1!==s&&((0,v.Y)(t,{[y.mK.Unmount](){n.current.splice(s,1)},[y.mK.Hidden](){n.current[s].state="hidden"}}),u.microTask(()=>{var e;!P(n)&&i.current&&(null==(e=r.current)||e.call(r))}))}),h=(0,a._)(e=>{let t=n.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):n.current.push({el:e,state:"visible"}),()=>c(e,y.mK.Unmount)}),p=(0,s.useRef)([]),m=(0,s.useRef)(Promise.resolve()),f=(0,s.useRef)({enter:[],leave:[]}),g=(0,a._)((e,r,n)=>{p.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(([t])=>t!==e)),null==t||t.chains.current[r].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(f.current[r].map(([e,t])=>t)).then(()=>e())})]),"enter"===r?m.current=m.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),b=(0,a._)((e,t,r)=>{Promise.all(f.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>r(t))});return(0,s.useMemo)(()=>({children:n,register:h,unregister:c,onStart:g,onStop:b,wait:m,chains:f}),[h,c,n,g,b,f,m])}k.displayName="NestingContext";let E=s.Fragment,S=y.Ac.RenderStrategy,A=(0,y.FX)(function(e,t){let{show:r,appear:n=!1,unmount:i=!0,...o}=e,l=(0,s.useRef)(null),d=b(e),p=(0,h.P)(...d?[l,t]:null===t?[]:[t]);(0,c.g)();let m=(0,f.O_)();if(void 0===r&&null!==m&&(r=(m&f.Uw.Open)===f.Uw.Open),void 0===r)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[g,v]=(0,s.useState)(r?"visible":"hidden"),w=T(()=>{r||v("hidden")}),[E,A]=(0,s.useState)(!0),C=(0,s.useRef)([r]);(0,u.s)(()=>{!1!==E&&C.current[C.current.length-1]!==r&&(C.current.push(r),A(!1))},[C,r]);let D=(0,s.useMemo)(()=>({show:r,appear:n,initial:E}),[r,n,E]);(0,u.s)(()=>{r?v("visible"):P(w)||null===l.current||v("hidden")},[r,w]);let F={unmount:i},R=(0,a._)(()=>{var t;E&&A(!1),null==(t=e.beforeEnter)||t.call(e)}),V=(0,a._)(()=>{var t;E&&A(!1),null==(t=e.beforeLeave)||t.call(e)}),j=(0,y.Ci)();return s.createElement(k.Provider,{value:w},s.createElement(x.Provider,{value:D},j({ourProps:{...F,as:s.Fragment,children:s.createElement(M,{ref:p,...F,...o,beforeEnter:R,beforeLeave:V})},theirProps:{},defaultTag:s.Fragment,features:S,visible:"visible"===g,name:"Transition"})))}),M=(0,y.FX)(function(e,t){var r,n;let{transition:i=!0,beforeEnter:l,afterEnter:d,beforeLeave:m,afterLeave:w,enter:A,enterFrom:M,enterTo:C,entered:D,leave:F,leaveFrom:R,leaveTo:V,...j}=e,[L,O]=(0,s.useState)(null),I=(0,s.useRef)(null),N=b(e),B=(0,h.P)(...N?[I,t,O]:null===t?[]:[t]),U=null==(r=j.unmount)||r?y.mK.Unmount:y.mK.Hidden,{show:$,appear:z,initial:W}=function(){let e=(0,s.useContext)(x);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[_,Y]=(0,s.useState)($?"visible":"hidden"),H=function(){let e=(0,s.useContext)(k);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:X,unregister:q}=H;(0,u.s)(()=>X(I),[X,I]),(0,u.s)(()=>{if(U===y.mK.Hidden&&I.current)return $&&"visible"!==_?void Y("visible"):(0,v.Y)(_,{hidden:()=>q(I),visible:()=>X(I)})},[_,I,X,q,$,U]);let K=(0,c.g)();(0,u.s)(()=>{if(N&&K&&"visible"===_&&null===I.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[I,_,K,N]);let G=z&&$&&W,Z=(0,s.useRef)(!1),Q=T(()=>{Z.current||(Y("hidden"),q(I))},H),[,J]=function(e,t,r,n){let[i,a]=(0,s.useState)(r),{hasFlag:l,addFlag:d,removeFlag:c}=function(e=0){let[t,r]=(0,s.useState)(e),n=(0,s.useCallback)(e=>r(e),[t]),i=(0,s.useCallback)(e=>r(t=>t|e),[t]),o=(0,s.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:n,addFlag:i,hasFlag:o,removeFlag:(0,s.useCallback)(e=>r(t=>t&~e),[r]),toggleFlag:(0,s.useCallback)(e=>r(t=>t^e),[r])}}(e&&i?3:0),h=(0,s.useRef)(!1),m=(0,s.useRef)(!1),f=(0,o.L)();return(0,u.s)(()=>{var i;if(e){if(r&&a(!0),!t){r&&d(3);return}return null==(i=null==n?void 0:n.start)||i.call(n,r),function(e,{prepare:t,run:r,done:n,inFlight:i}){let s=(0,p.e)();return function(e,{inFlight:t,prepare:r}){if(null!=t&&t.current)return r();let n=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=n}(e,{prepare:t,inFlight:i}),s.nextFrame(()=>{r(),s.requestAnimationFrame(()=>{s.add(function(e,t){var r,n;let i=(0,p.e)();if(!e)return i.dispose;let s=!1;i.add(()=>{s=!0});let o=null!=(n=null==(r=e.getAnimations)?void 0:r.call(e).filter(e=>e instanceof CSSTransition))?n:[];return 0===o.length?t():Promise.allSettled(o.map(e=>e.finished)).then(()=>{s||t()}),i.dispose}(e,n))})}),s.dispose}(t,{inFlight:h,prepare(){m.current?m.current=!1:m.current=h.current,h.current=!0,m.current||(r?(d(3),c(4)):(d(4),c(2)))},run(){m.current?r?(c(3),d(4)):(c(4),d(3)):r?c(1):d(1)},done(){var e;m.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(h.current=!1,c(7),r||a(!1),null==(e=null==n?void 0:n.end)||e.call(n,r))}})}},[e,r,t,f]),e?[i,{closed:l(1),enter:l(2),leave:l(4),transition:l(2)||l(4)}]:[r,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!i||!N||!K||W&&!z),L,$,{start:(0,a._)(e=>{Z.current=!0,Q.onStart(I,e?"enter":"leave",e=>{"enter"===e?null==l||l():"leave"===e&&(null==m||m())})}),end:(0,a._)(e=>{let t=e?"enter":"leave";Z.current=!1,Q.onStop(I,t,e=>{"enter"===e?null==d||d():"leave"===e&&(null==w||w())}),"leave"!==t||P(Q)||(Y("hidden"),q(I))})}),ee=(0,y.oE)({ref:B,className:(null==(n=(0,g.x)(j.className,G&&A,G&&M,J.enter&&A,J.enter&&J.closed&&M,J.enter&&!J.closed&&C,J.leave&&F,J.leave&&!J.closed&&R,J.leave&&J.closed&&V,!J.transition&&$&&D))?void 0:n.trim())||void 0,...function(e){let t={};for(let r in e)!0===e[r]&&(t[`data-${r}`]="");return t}(J)}),et=0;"visible"===_&&(et|=f.Uw.Open),"hidden"===_&&(et|=f.Uw.Closed),$&&"hidden"===_&&(et|=f.Uw.Opening),$||"visible"!==_||(et|=f.Uw.Closing);let er=(0,y.Ci)();return s.createElement(k.Provider,{value:Q},s.createElement(f.El,{value:et},er({ourProps:ee,theirProps:j,defaultTag:E,features:S,visible:"visible"===_,name:"Transition.Child"})))}),C=(0,y.FX)(function(e,t){let r=null!==(0,s.useContext)(x),n=null!==(0,f.O_)();return s.createElement(s.Fragment,null,!r&&n?s.createElement(A,{ref:t,...e}):s.createElement(M,{ref:t,...e}))}),D=Object.assign(A,{Child:C,Root:A})},15124:(e,t,r)=>{r.d(t,{E:()=>i});var n=r(43210);let i=r(7044).B?n.useLayoutEffect:n.useEffect},15319:(e,t,r)=>{r.d(t,{g:()=>o});var n,i=r(43210),s=r(99923);function o(){let e,t=(e="undefined"==typeof document,"useSyncExternalStore"in(n||(n=r.t(i,2)))&&(0,(n||(n=r.t(i,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[o,a]=i.useState(s._.isHandoffComplete);return o&&!1===s._.isHandoffComplete&&a(!1),i.useEffect(()=>{!0!==o&&a(!0)},[o]),i.useEffect(()=>s._.handoff(),[]),!t&&o}},18171:(e,t,r)=>{r.d(t,{s:()=>i});var n=r(74479);function i(e){return(0,n.G)(e)&&"offsetHeight"in e}},18908:(e,t,r)=>{r.d(t,{lG:()=>eG});var n,i,s,o=r(43210),a=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(a||{}),l=r(10327);class u extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var d=r(48143),c=Object.defineProperty,h=(e,t,r)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,p=(e,t,r)=>(h(e,"symbol"!=typeof t?t+"":t,r),r),m=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)},f=(e,t,r)=>(m(e,t,"read from private field"),r?r.call(e):t.get(e)),g=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},v=(e,t,r,n)=>(m(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);class y{constructor(e){g(this,n,{}),g(this,i,new u(()=>new Set)),g(this,s,new Set),p(this,"disposables",(0,d.e)()),v(this,n,e)}dispose(){this.disposables.dispose()}get state(){return f(this,n)}subscribe(e,t){let r={selector:e,callback:t,current:e(f(this,n))};return f(this,s).add(r),this.disposables.add(()=>{f(this,s).delete(r)})}on(e,t){return f(this,i).get(e).add(t),this.disposables.add(()=>{f(this,i).get(e).delete(t)})}send(e){let t=this.reduce(f(this,n),e);if(t!==f(this,n)){for(let e of(v(this,n,t),f(this,s))){let t=e.selector(f(this,n));b(e.current,t)||(e.current=t,e.callback(t))}for(let t of f(this,i).get(e.type))t(f(this,n),e)}}}function b(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&x(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&x(e.entries(),t.entries()):!!(w(e)&&w(t))&&x(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function x(e,t){for(;;){let r=e.next(),n=t.next();if(r.done&&n.done)return!0;if(r.done||n.done||!Object.is(r.value,n.value))return!1}}function w(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}n=new WeakMap,i=new WeakMap,s=new WeakMap;var k=r(44685),P=Object.defineProperty,T=(e,t,r)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,E=(e,t,r)=>(T(e,"symbol"!=typeof t?t+"":t,r),r),S=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(S||{});let A={0(e,t){let r=t.id,n=e.stack,i=e.stack.indexOf(r);if(-1!==i){let t=e.stack.slice();return t.splice(i,1),t.push(r),n=t,{...e,stack:n}}return{...e,stack:[...e.stack,r]}},1(e,t){let r=t.id,n=e.stack.indexOf(r);if(-1===n)return e;let i=e.stack.slice();return i.splice(n,1),{...e,stack:i}}};class M extends y{constructor(){super(...arguments),E(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),E(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new M({stack:[]})}reduce(e,t){return(0,k.Y)(t.type,A,e,t)}}let C=new u(()=>M.new());var D=r(6895),F=r(52263);function R(e,t,r=b){return(0,D.useSyncExternalStoreWithSelector)((0,F._)(t=>e.subscribe(V,t)),(0,F._)(()=>e.state),(0,F._)(()=>e.state),(0,F._)(t),r)}function V(e){return e}var j=r(52315);function L(e,t){let r=(0,o.useId)(),n=C.get(t),[i,s]=R(n,(0,o.useCallback)(e=>[n.selectors.isTop(e,r),n.selectors.inStack(e,r)],[n,r]));return(0,j.s)(()=>{if(e)return n.actions.push(r),()=>n.actions.pop(r)},[n,e,r]),!!e&&(!s||i)}var O=r(99923);function I(e){var t,r;return O._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(r=null==(t=e.current)?void 0:t.ownerDocument)?r:document:null:document}let N=new Map,B=new Map;function U(e){var t;let r=null!=(t=B.get(e))?t:0;return B.set(e,r+1),0!==r||(N.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let r=null!=(t=B.get(e))?t:1;if(1===r?B.delete(e):B.set(e,r-1),1!==r)return;let n=N.get(e);n&&(null===n["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",n["aria-hidden"]),e.inert=n.inert,N.delete(e))})(e)}function $(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function z(e){return $(e)&&"tagName"in e}function W(e){return z(e)&&"accessKey"in e}function _(e){return z(e)&&"tabIndex"in e}let Y=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),H=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var X=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(X||{}),q=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(q||{}),K=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(K||{}),G=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(G||{}),Z=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Z||{});function Q(e){null==e||e.focus({preventScroll:!0})}function J(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:i=[]}={}){var s,o,a;let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?r?function(e,t=e=>e){return e.slice().sort((e,r)=>{let n=t(e),i=t(r);if(null===n||null===i)return 0;let s=n.compareDocumentPosition(i);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(H)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Y)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);i.length>0&&u.length>1&&(u=u.filter(e=>!i.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),n=null!=n?n:l.activeElement;let d=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(n))-1;if(4&t)return Math.max(0,u.indexOf(n))+1;if(8&t)return u.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),h=32&t?{preventScroll:!0}:{},p=0,m=u.length,f;do{if(p>=m||p+m<=0)return 0;let e=c+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(f=u[e])||f.focus(h),p+=d}while(f!==l.activeElement);return 6&t&&null!=(a=null==(o=null==(s=f)?void 0:s.matches)?void 0:o.call(s,"textarea,input"))&&a&&f.select(),2}function ee(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function et(){return ee()||/Android/gi.test(window.navigator.userAgent)}function er(...e){return(0,o.useMemo)(()=>I(...e),[...e])}var en=r(69334),ei=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ei||{});let es=(0,en.FX)(function(e,t){var r;let{features:n=1,...i}=e,s={ref:t,"aria-hidden":(2&n)==2||(null!=(r=i["aria-hidden"])?r:void 0),hidden:(4&n)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&n)==4&&(2&n)!=2&&{display:"none"}}};return(0,en.Ci)()({ourProps:s,theirProps:i,slot:{},defaultTag:"span",name:"Hidden"})}),eo=(0,o.createContext)(null);function ea({children:e,node:t}){let[r,n]=(0,o.useState)(null),i=el(null!=t?t:r);return o.createElement(eo.Provider,{value:i},e,null===i&&o.createElement(es,{features:ei.Hidden,ref:e=>{var t,r;if(e){for(let i of null!=(r=null==(t=I(e))?void 0:t.querySelectorAll("html > *, body > *"))?r:[])if(i!==document.body&&i!==document.head&&z(i)&&null!=i&&i.contains(e)){n(i);break}}}}))}function el(e=null){var t;return null!=(t=(0,o.useContext)(eo))?t:e}let eu=function(e,t){let r=e(),n=new Set;return{getSnapshot:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...i){let s=t[e].call(r,...i);s&&(r=s,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var r;let n=null!=(r=this.get(e))?r:{doc:e,count:0,d:(0,d.e)(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n,i={doc:e,d:t,meta:function(e){let t={};for(let r of e)Object.assign(t,r(t));return t}(r)},s=[ee()?{before({doc:e,d:t,meta:r}){function n(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var r;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let r=(0,d.e)();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let i=null!=(r=window.scrollY)?r:window.pageYOffset,s=null;t.addEventListener(e,"click",t=>{if(_(t.target))try{let r=t.target.closest("a");if(!r)return;let{hash:i}=new URL(r.href),o=e.querySelector(i);_(o)&&!n(o)&&(s=o)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var r;if(_(e.target)&&z(r=e.target)&&"style"in r)if(n(e.target)){let r=e.target;for(;r.parentElement&&n(r.parentElement);)r=r.parentElement;t.style(r,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(_(e.target)){var t;if(!(W(t=e.target)&&"INPUT"===t.nodeName))if(n(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;i!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,i),s&&s.isConnected&&(s.scrollIntoView({block:"nearest"}),s=null)})})}}:{},{before({doc:e}){var t;let r=e.documentElement;n=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-r.clientWidth)},after({doc:e,d:t}){let r=e.documentElement,i=Math.max(0,r.clientWidth-r.offsetWidth),s=Math.max(0,n-i);t.style(r,"paddingRight",`${s}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];s.forEach(({before:e})=>null==e?void 0:e(i)),s.forEach(({after:e})=>null==e?void 0:e(i))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});eu.subscribe(()=>{let e=eu.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let e="hidden"===t.get(r.doc),n=0!==r.count;(n&&!e||!n&&e)&&eu.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),0===r.count&&eu.dispatch("TEARDOWN",r)}});var ed=r(15319),ec=r(44967);let eh=(0,o.createContext)(()=>{});function ep({value:e,children:t}){return o.createElement(eh.Provider,{value:e},t)}var em=r(39857);let ef=(0,o.createContext)(!1);function eg(e){return o.createElement(ef.Provider,{value:e.force},e.children)}let ev=(0,o.createContext)(void 0),ey=(0,o.createContext)(null);ey.displayName="DescriptionContext";let eb=Object.assign((0,en.FX)(function(e,t){let r=(0,o.useId)(),n=(0,o.useContext)(ev),{id:i=`headlessui-description-${r}`,...s}=e,a=function e(){let t=(0,o.useContext)(ey);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),l=(0,ec.P)(t);(0,j.s)(()=>a.register(i),[i,a.register]);let u=n||!1,d=(0,o.useMemo)(()=>({...a.slot,disabled:u}),[a.slot,u]),c={ref:l,...a.props,id:i};return(0,en.Ci)()({ourProps:c,theirProps:s,slot:d,defaultTag:"p",name:a.name||"Description"})}),{});var ex=r(252),ew=r(84818),ek=r(39704);function eP(e){(0,F._)(e),(0,o.useRef)(!1)}var eT=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(eT||{});function eE(e,t){(0,o.useRef)([]),(0,F._)(e)}let eS=[];function eA(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let r of e.current)z(r.current)&&t.add(r.current);return t}var eM=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eM||{});let eC=Object.assign((0,en.FX)(function(e,t){var r;let n,i=(0,o.useRef)(null),s=(0,ec.P)(i,t),{initialFocus:a,initialFocusFallback:u,containers:d,features:c=15,...h}=e;(0,ed.g)()||(c=0);let p=er(i);!function(e,{ownerDocument:t}){let r=!!(8&e),n=function(e=!0){let t=(0,o.useRef)(eS.slice());return eE(([e],[r])=>{!0===r&&!1===e&&(0,ek._)(()=>{t.current.splice(0)}),!1===r&&!0===e&&(t.current=eS.slice())},[e,eS,t]),(0,F._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eE(()=>{r||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&Q(n())},[r]),eP(()=>{r&&Q(n())})}(c,{ownerDocument:p});let m=function(e,{ownerDocument:t,container:r,initialFocus:n,initialFocusFallback:i}){let s=(0,o.useRef)(null),a=L(!!(1&e),"focus-trap#initial-focus"),l=(0,ew.a)();return eE(()=>{if(0===e)return;if(!a){null!=i&&i.current&&Q(i.current);return}let o=r.current;o&&(0,ek._)(()=>{if(!l.current)return;let r=null==t?void 0:t.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===r){s.current=r;return}}else if(o.contains(r)){s.current=r;return}if(null!=n&&n.current)Q(n.current);else{if(16&e){if(J(o,X.First|X.AutoFocus)!==q.Error)return}else if(J(o,X.First)!==q.Error)return;if(null!=i&&i.current&&(Q(i.current),(null==t?void 0:t.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}s.current=null==t?void 0:t.activeElement})},[i,a,e]),s}(c,{ownerDocument:p,container:i,initialFocus:a,initialFocusFallback:u});!function(e,{ownerDocument:t,container:r,containers:n,previousActiveElement:i}){var s;let o=(0,ew.a)(),a=!!(4&e);null==t||t.defaultView,(0,l.Y)(e=>{if(!a||!o.current)return;let t=eA(n);W(r.current)&&t.add(r.current);let s=i.current;if(!s)return;let l=e.target;W(l)?eD(t,l)?(i.current=l,Q(l)):(e.preventDefault(),e.stopPropagation(),Q(s)):Q(i.current)})}(c,{ownerDocument:p,container:i,containers:d,previousActiveElement:m});let f=(n=(0,o.useRef)(0),r=e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},(0,l.Y)(r),n),g=(0,F._)(e=>{if(!W(i.current))return;let t=i.current;(0,k.Y)(f.current,{[eT.Forwards]:()=>{J(t,X.First,{skipElements:[e.relatedTarget,u]})},[eT.Backwards]:()=>{J(t,X.Last,{skipElements:[e.relatedTarget,u]})}})}),v=L(!!(2&c),"focus-trap#tab-lock"),y=(0,ex.L)(),b=(0,o.useRef)(!1),x=(0,en.Ci)();return o.createElement(o.Fragment,null,v&&o.createElement(es,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:g,features:ei.Focusable}),x({ourProps:{ref:s,onKeyDown(e){"Tab"==e.key&&(b.current=!0,y.requestAnimationFrame(()=>{b.current=!1}))},onBlur(e){if(!(4&c))return;let t=eA(d);W(i.current)&&t.add(i.current);let r=e.relatedTarget;_(r)&&"true"!==r.dataset.headlessuiFocusGuard&&(eD(t,r)||(b.current?J(i.current,(0,k.Y)(f.current,{[eT.Forwards]:()=>X.Next,[eT.Backwards]:()=>X.Previous})|X.WrapAround,{relativeTo:e.target}):_(e.target)&&Q(e.target)))}},theirProps:h,defaultTag:"div",name:"FocusTrap"}),v&&o.createElement(es,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:g,features:ei.Focusable}))}),{features:eM});function eD(e,t){for(let r of e)if(r.contains(t))return!0;return!1}var eF=r(51215);let eR=o.Fragment,eV=(0,en.FX)(function(e,t){let{ownerDocument:r=null,...n}=e,i=(0,o.useRef)(null),s=(0,ec.P)((0,ec.a)(e=>{i.current=e}),t),a=er(i),l=null!=r?r:a,u=function(e){let t=(0,o.useContext)(ef),r=(0,o.useContext)(eL),[n,i]=(0,o.useState)(()=>{var n;if(!t&&null!==r)return null!=(n=r.current)?n:null;if(O._.isServer)return null;let i=null==e?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(null===e)return null;let s=e.createElement("div");return s.setAttribute("id","headlessui-portal-root"),e.body.appendChild(s)});return n}(l),[d]=(0,o.useState)(()=>{var e;return O._.isServer?null:null!=(e=null==l?void 0:l.createElement("div"))?e:null}),c=(0,o.useContext)(eO),h=(0,ed.g)();(0,j.s)(()=>{!u||!d||u.contains(d)||(d.setAttribute("data-headlessui-portal",""),u.appendChild(d))},[u,d]),(0,j.s)(()=>{if(d&&c)return c.register(d)},[c,d]),eP(()=>{var e;u&&d&&($(d)&&u.contains(d)&&u.removeChild(d),u.childNodes.length<=0&&(null==(e=u.parentElement)||e.removeChild(u)))});let p=(0,en.Ci)();return h&&u&&d?(0,eF.createPortal)(p({ourProps:{ref:s},theirProps:n,slot:{},defaultTag:eR,name:"Portal"}),d):null}),ej=o.Fragment,eL=(0,o.createContext)(null),eO=(0,o.createContext)(null),eI=(0,en.FX)(function(e,t){let r=(0,ec.P)(t),{enabled:n=!0,ownerDocument:i,...s}=e,a=(0,en.Ci)();return n?o.createElement(eV,{...s,ownerDocument:i,ref:r}):a({ourProps:{ref:r},theirProps:s,slot:{},defaultTag:eR,name:"Portal"})}),eN=(0,en.FX)(function(e,t){let{target:r,...n}=e,i={ref:(0,ec.P)(t)},s=(0,en.Ci)();return o.createElement(eL.Provider,{value:r},s({ourProps:i,theirProps:n,defaultTag:ej,name:"Popover.Group"}))}),eB=Object.assign(eI,{Group:eN});var eU=r(13516),e$=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(e$||{}),ez=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(ez||{});let eW={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},e_=(0,o.createContext)(null);function eY(e){let t=(0,o.useContext)(e_);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eY),t}return t}function eH(e,t){return(0,k.Y)(t.type,eW,e,t)}e_.displayName="DialogContext";let eX=(0,en.FX)(function(e,t){let r,n,i,s,u,c,h,p,m,f=(0,o.useId)(),{id:g=`headlessui-dialog-${f}`,open:v,onClose:y,initialFocus:b,role:x="dialog",autoFocus:w=!0,__demoMode:P=!1,unmount:T=!1,...E}=e,S=(0,o.useRef)(!1);x="dialog"===x||"alertdialog"===x?x:(S.current||(S.current=!0,console.warn(`Invalid role [${x}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let A=(0,em.O_)();void 0===v&&null!==A&&(v=(A&em.Uw.Open)===em.Uw.Open);let M=(0,o.useRef)(null),D=(0,ec.P)(M,t),V=er(M),O=+!v,[N,B]=(0,o.useReducer)(eH,{titleId:null,descriptionId:null,panelRef:(0,o.createRef)()}),$=(0,F._)(()=>y(!1)),H=(0,F._)(e=>B({type:0,id:e})),X=!!(0,ed.g)()&&0===O,[q,K]=(r=(0,o.useContext)(eO),n=(0,o.useRef)([]),i=(0,F._)(e=>(n.current.push(e),r&&r.register(e),()=>s(e))),s=(0,F._)(e=>{let t=n.current.indexOf(e);-1!==t&&n.current.splice(t,1),r&&r.unregister(e)}),u=(0,o.useMemo)(()=>({register:i,unregister:s,portals:n}),[i,s,n]),[n,(0,o.useMemo)(()=>function({children:e}){return o.createElement(eO.Provider,{value:u},e)},[u])]),Z=el(),{resolveContainers:Q}=function({defaultContainers:e=[],portals:t,mainTreeNode:r}={}){let n=er(r),i=(0,F._)(()=>{var i,s;let o=[];for(let t of e)null!==t&&(z(t)?o.push(t):"current"in t&&z(t.current)&&o.push(t.current));if(null!=t&&t.current)for(let e of t.current)o.push(e);for(let e of null!=(i=null==n?void 0:n.querySelectorAll("html > *, body > *"))?i:[])e!==document.body&&e!==document.head&&z(e)&&"headlessui-portal-root"!==e.id&&(r&&(e.contains(r)||e.contains(null==(s=null==r?void 0:r.getRootNode())?void 0:s.host))||o.some(t=>e.contains(t))||o.push(e));return o});return{resolveContainers:i,contains:(0,F._)(e=>i().some(t=>t.contains(e)))}}({mainTreeNode:Z,portals:q,defaultContainers:[{get current(){var J;return null!=(J=N.panelRef.current)?J:M.current}}]}),ee=null!==A&&(A&em.Uw.Closing)===em.Uw.Closing;!function(e,{allowed:t,disallowed:r}={}){let n=L(e,"inert-others");(0,j.s)(()=>{var e,i;if(!n)return;let s=(0,d.e)();for(let t of null!=(e=null==r?void 0:r())?e:[])t&&s.add(U(t));let o=null!=(i=null==t?void 0:t())?i:[];for(let e of o){if(!e)continue;let t=I(e);if(!t)continue;let r=e.parentElement;for(;r&&r!==t.body;){for(let e of r.children)o.some(t=>e.contains(t))||s.add(U(e));r=r.parentElement}}return s.dispose},[n,t,r])}(!P&&!ee&&X,{allowed:(0,F._)(()=>{var e,t;return[null!=(t=null==(e=M.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,F._)(()=>{var e;return[null!=(e=null==Z?void 0:Z.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let ei=C.get(null);(0,j.s)(()=>{if(X)return ei.actions.push(g),()=>ei.actions.pop(g)},[ei,g,X]);let es=R(ei,(0,o.useCallback)(e=>ei.selectors.isTop(e,g),[ei,g]));c=(0,l.Y)(e=>{e.preventDefault(),$()}),h=(0,o.useCallback)(function(e,t){if(e.defaultPrevented)return;let r=t(e);if(null!==r&&r.getRootNode().contains(r)&&r.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(Q))if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return function(e,t=0){var r;return e!==(null==(r=I(e))?void 0:r.body)&&(0,k.Y)(t,{0:()=>e.matches(Y),1(){let t=e;for(;null!==t;){if(t.matches(Y))return!0;t=t.parentElement}return!1}})}(r,G.Loose)||-1===r.tabIndex||e.preventDefault(),c.current(e,r)}},[c,Q]),p=(0,o.useRef)(null),(0,l.Y)(e=>{var t,r;et()||(p.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)}),(0,l.Y)(e=>{if(et()||!p.current)return;let t=p.current;return p.current=null,h(e,()=>t)}),m=(0,o.useRef)({x:0,y:0}),(0,l.Y)(e=>{m.current.x=e.touches[0].clientX,m.current.y=e.touches[0].clientY}),(0,l.Y)(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-m.current.x)>=30||Math.abs(t.y-m.current.y)>=30))return h(e,()=>_(e.target)?e.target:null)}),(0,l.Y)(e=>h(e,()=>{var e;return W(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null})),function(e,t="undefined"!=typeof document?document.defaultView:null,r){let n=L(e,"escape");(0,l.Y)(e=>{n&&(e.defaultPrevented||e.key===a.Escape&&r(e))})}(es,null==V?void 0:V.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),$()}),function(e,t,r=()=>[document.body]){!function(e,t,r=()=>({containers:[]})){let n=(0,o.useSyncExternalStore)(eu.subscribe,eu.getSnapshot,eu.getSnapshot),i=t?n.get(t):void 0;i&&i.count,(0,j.s)(()=>{if(!(!t||!e))return eu.dispatch("PUSH",t,r),()=>eu.dispatch("POP",t,r)},[e,t])}(L(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}})}(!P&&!ee&&X,V,Q),(0,l.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&$()});let[eo,ea]=function(){let[e,t]=(0,o.useState)([]);return[e.length>0?e.join(" "):void 0,(0,o.useMemo)(()=>function(e){let r=(0,F._)(e=>(t(t=>[...t,e]),()=>t(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),n=(0,o.useMemo)(()=>({register:r,slot:e.slot,name:e.name,props:e.props,value:e.value}),[r,e.slot,e.name,e.props,e.value]);return o.createElement(ey.Provider,{value:n},e.children)},[t])]}(),eh=(0,o.useMemo)(()=>[{dialogState:O,close:$,setTitleId:H,unmount:T},N],[O,N,$,H,T]),ef=(0,o.useMemo)(()=>({open:0===O}),[O]),ev={ref:D,id:g,role:x,tabIndex:-1,"aria-modal":P?void 0:0===O||void 0,"aria-labelledby":N.titleId,"aria-describedby":eo,unmount:T},eb=!function(){var e;let[t]=(0,o.useState)(()=>null),[r,n]=(0,o.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,j.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){n(e.matches)}},[t]),r}(),ex=eM.None;X&&!P&&(ex|=eM.RestoreFocus,ex|=eM.TabLock,w&&(ex|=eM.AutoFocus),eb&&(ex|=eM.InitialFocus));let ew=(0,en.Ci)();return o.createElement(em.$x,null,o.createElement(eg,{force:!0},o.createElement(eB,null,o.createElement(e_.Provider,{value:eh},o.createElement(eN,{target:M},o.createElement(eg,{force:!1},o.createElement(ea,{slot:ef},o.createElement(K,null,o.createElement(eC,{initialFocus:b,initialFocusFallback:M,containers:Q,features:ex},o.createElement(ep,{value:$},ew({ourProps:ev,theirProps:E,slot:ef,defaultTag:eq,features:eK,visible:0===O,name:"Dialog"})))))))))))}),eq="div",eK=en.Ac.RenderStrategy|en.Ac.Static,eG=Object.assign((0,en.FX)(function(e,t){let{transition:r=!1,open:n,...i}=e,s=(0,em.O_)(),a=e.hasOwnProperty("open")||null!==s,l=e.hasOwnProperty("onClose");if(!a&&!l)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!l)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!s&&"boolean"!=typeof e.open)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(void 0!==n||r)&&!i.static?o.createElement(ea,null,o.createElement(eU.e,{show:n,transition:r,unmount:i.unmount},o.createElement(eX,{ref:t,...i}))):o.createElement(ea,null,o.createElement(eX,{ref:t,open:n,...i}))}),{Panel:(0,en.FX)(function(e,t){let r=(0,o.useId)(),{id:n=`headlessui-dialog-panel-${r}`,transition:i=!1,...s}=e,[{dialogState:a,unmount:l},u]=eY("Dialog.Panel"),d=(0,ec.P)(t,u.panelRef),c=(0,o.useMemo)(()=>({open:0===a}),[a]),h=(0,F._)(e=>{e.stopPropagation()}),p=i?eU._:o.Fragment,m=(0,en.Ci)();return o.createElement(p,{...i?{unmount:l}:{}},m({ourProps:{ref:d,id:n,onClick:h},theirProps:s,slot:c,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,en.FX)(function(e,t){let{transition:r=!1,...n}=e,[{dialogState:i,unmount:s}]=eY("Dialog.Backdrop"),a=(0,o.useMemo)(()=>({open:0===i}),[i]),l=r?eU._:o.Fragment,u=(0,en.Ci)();return o.createElement(l,{...r?{unmount:s}:{}},u({ourProps:{ref:t,"aria-hidden":!0},theirProps:n,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,en.FX)(function(e,t){let r=(0,o.useId)(),{id:n=`headlessui-dialog-title-${r}`,...i}=e,[{dialogState:s,setTitleId:a}]=eY("Dialog.Title"),l=(0,ec.P)(t),u=(0,o.useMemo)(()=>({open:0===s}),[s]);return(0,en.Ci)()({ourProps:{ref:l,id:n},theirProps:i,slot:u,defaultTag:"h2",name:"Dialog.Title"})})),Description:eb})},21279:(e,t,r)=>{r.d(t,{t:()=>n});let n=(0,r(43210).createContext)(null)},23576:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},25541:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26001:(e,t,r)=>{let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function s(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,n){if("function"==typeof t){let[i,o]=s(n);t=t(void 0!==r?r:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=s(n);t=t(void 0!==r?r:e.custom,i,o)}return t}function a(e,t,r){let n=e.getProps();return o(n,t,void 0!==r?r:n.custom,e)}function l(e,t){return e?.[t]??e?.default??e}r.d(t,{P:()=>sA});let u=e=>e,d={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,o=c.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(d.schedule(t),e()),l++,t(a)}let d={schedule:(e,t=!1,s=!1)=>{let a=s&&i?r:n;return t&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),o.delete(e)},process:e=>{if(a=e,i){s=!0;return}i=!0,[r,n]=[n,r],r.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,r.clear(),i=!1,s&&(s=!1,d.process(e))}};return d}(s,t?r:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=o,y=()=>{let s=d.useManualTiming?i.timestamp:performance.now();r=!1,d.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(s-i.timestamp,40),1)),i.timestamp=s,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),p.process(i),m.process(i),f.process(i),g.process(i),v.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(y))},b=()=>{r=!0,n=!0,i.isProcessing||e(y)};return{schedule:c.reduce((e,t)=>{let n=o[t];return e[t]=(e,t=!1,i=!1)=>(r||b(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<c.length;t++)o[c[t]].cancel(e)},state:i,steps:o}}let{schedule:m,cancel:f,state:g,steps:v}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(y),x=new Set(["width","height","top","left","right","bottom",...y]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function k(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class P{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>k(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){n=void 0}let E={now:()=>(void 0===n&&E.set(g.isProcessing||d.useManualTiming?g.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(T)}},S=e=>!isNaN(parseFloat(e)),A={current:void 0};class M{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=E.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new P);let r=this.events[e].add(t);return"change"===e?()=>{r(),m.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(e,t){return new M(e,t)}let D=e=>Array.isArray(e),F=e=>!!(e&&e.getVelocity);function R(e,t){let r=e.getValue("willChange");if(F(r)&&r.add)return r.add(t);if(!r&&d.WillChange){let r=new d.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let V=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),j="data-"+V("framerAppearId"),L=(e,t)=>r=>t(e(r)),O=(...e)=>e.reduce(L),I=(e,t,r)=>r>t?t:r<e?e:r,N=e=>1e3*e,B=e=>e/1e3,U={layout:0,mainThread:0,waapi:0},$=()=>{},z=()=>{},W=e=>t=>"string"==typeof t&&t.startsWith(e),_=W("--"),Y=W("var(--"),H=e=>!!Y(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,q={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},K={...q,transform:e=>I(0,1,e)},G={...q,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>r=>!!("string"==typeof r&&J.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),et=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,s,o,a]=n.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},er=e=>I(0,255,e),en={...q,transform:e=>Math.round(er(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+en.transform(e)+", "+en.transform(t)+", "+en.transform(r)+", "+Z(K.transform(n))+")"},es={test:ee("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},eo=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ea=eo("deg"),el=eo("%"),eu=eo("px"),ed=eo("vh"),ec=eo("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(r))+", "+Z(K.transform(n))+")"},em={test:e=>ei.test(e)||es.test(e)||ep.test(e),parse:e=>ei.test(e)?ei.parse(e):ep.test(e)?ep.parse(e):es.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ep.transform(e),getAnimatableNone:e=>{let t=em.parse(e);return t.alpha=0,em.transform(t)}},ef=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ev="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],s=0,o=t.replace(ey,e=>(em.test(e)?(n.color.push(s),i.push(ev),r.push(em.parse(e))):e.startsWith("var(")?(n.var.push(s),i.push("var"),r.push(e)):(n.number.push(s),i.push(eg),r.push(parseFloat(e))),++s,"${}")).split("${}");return{values:r,split:o,indexes:n,types:i}}function ex(e){return eb(e).values}function ew(e){let{split:t,types:r}=eb(e),n=t.length;return e=>{let i="";for(let s=0;s<n;s++)if(i+=t[s],void 0!==e[s]){let t=r[s];t===eg?i+=Z(e[s]):t===ev?i+=em.transform(e[s]):i+=e[s]}return i}}let ek=e=>"number"==typeof e?0:em.test(e)?em.getAnimatableNone(e):e,eP={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(ef)?.length||0)>0},parse:ex,createTransformer:ew,getAnimatableNone:function(e){let t=ex(e);return ew(e)(t.map(ek))}};function eT(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function eE(e,t){return r=>r>0?t:e}let eS=(e,t,r)=>e+(t-e)*r,eA=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},eM=[es,ei,ep],eC=e=>eM.find(t=>t.test(e));function eD(e){let t=eC(e);if($(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===ep&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,s=0,o=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;i=eT(a,n,e+1/3),s=eT(a,n,e),o=eT(a,n,e-1/3)}else i=s=o=r;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(r)),r}let eF=(e,t)=>{let r=eD(e),n=eD(t);if(!r||!n)return eE(e,t);let i={...r};return e=>(i.red=eA(r.red,n.red,e),i.green=eA(r.green,n.green,e),i.blue=eA(r.blue,n.blue,e),i.alpha=eS(r.alpha,n.alpha,e),ei.transform(i))},eR=new Set(["none","hidden"]);function eV(e,t){return r=>eS(e,t,r)}function ej(e){return"number"==typeof e?eV:"string"==typeof e?H(e)?eE:em.test(e)?eF:eI:Array.isArray(e)?eL:"object"==typeof e?em.test(e)?eF:eO:eE}function eL(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>ej(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function eO(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=ej(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let eI=(e,t)=>{let r=eP.createTransformer(t),n=eb(e),i=eb(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?eR.has(e)&&!i.values.length||eR.has(t)&&!n.values.length?function(e,t){return eR.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):O(eL(function(e,t){let r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let s=t.types[i],o=e.indexes[s][n[s]],a=e.values[o]??0;r[i]=a,n[s]++}return r}(n,i),i.values),r):($(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eE(e,t))};function eN(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?eS(e,t,r):ej(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>m.update(t,e),stop:()=>f(t),now:()=>g.isProcessing?g.timestamp:E.now()}},eU=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function e$(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function ez(e,t,r){var n,i;let s=Math.max(t-5,0);return n=r-e(s),(i=t-s)?1e3/i*n:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e_(e,t){return e*Math.sqrt(1-t*t)}let eY=["duration","bounce"],eH=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eq(e=eW.visualDuration,t=eW.bounce){let r,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:d,mass:c,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eH)&&eX(e,eY))if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:eW.mass,stiffness:n,damping:i}}else{let r=function({duration:e=eW.duration,bounce:t=eW.bounce,velocity:r=eW.velocity,mass:n=eW.mass}){let i,s;$(e<=N(eW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-t;o=I(eW.minDamping,eW.maxDamping,o),e=I(eW.minDuration,eW.maxDuration,B(e)),o<1?(i=t=>{let n=t*o,i=n*e;return .001-(n-r)/e_(t,o)*Math.exp(-i)},s=t=>{let n=t*o*e,s=Math.pow(o,2)*Math.pow(t,2)*e,a=Math.exp(-n),l=e_(Math.pow(t,2),o);return(n*r+r-s)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let a=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,s,5/e);if(e=N(e),isNaN(a))return{stiffness:eW.stiffness,damping:eW.damping,duration:e};{let t=Math.pow(a,2)*n;return{stiffness:t,damping:2*o*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:eW.mass}).isResolvedFromDuration=!0}return t}({...n,velocity:-B(n.velocity||0)}),f=p||0,g=d/(2*Math.sqrt(u*c)),v=a-o,y=B(Math.sqrt(u/c)),b=5>Math.abs(v);if(i||(i=b?eW.restSpeed.granular:eW.restSpeed.default),s||(s=b?eW.restDelta.granular:eW.restDelta.default),g<1){let e=e_(y,g);r=t=>a-Math.exp(-g*y*t)*((f+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)r=e=>a-Math.exp(-y*e)*(v+(f+y*v)*e);else{let e=y*Math.sqrt(g*g-1);r=t=>{let r=Math.exp(-g*y*t),n=Math.min(e*t,300);return a-r*((f+g*y*v)*Math.sinh(n)+e*v*Math.cosh(n))/e}}let x={calculatedDuration:m&&h||null,next:e=>{let t=r(e);if(m)l.done=e>=h;else{let n=0===e?f:0;g<1&&(n=0===e?N(f):ez(r,e,t));let o=Math.abs(a-t)<=s;l.done=Math.abs(n)<=i&&o}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(e$(x),2e4),t=eU(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function eK({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:d}){let c,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,v=r*t,y=p+v,b=void 0===o?y:o(y);b!==y&&(v=b-p);let x=e=>-v*Math.exp(-e/n),w=e=>b+x(e),k=e=>{let t=x(e),r=w(e);m.done=Math.abs(t)<=u,m.value=m.done?b:r},P=e=>{f(m.value)&&(c=e,h=eq({keyframes:[m.value,g(m.value)],velocity:ez(w,e,m.value),damping:i,stiffness:s,restDelta:u,restSpeed:d}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==c||(t=!0,k(e),P(e)),void 0!==c&&e>=c)?h.next(e-c):(t||k(e),m)}}}eq.applyToOptions=e=>{let t=function(e,t=100,r){let n=r({...e,keyframes:[0,t]}),i=Math.min(e$(n),2e4);return{type:"keyframes",ease:e=>n.next(i*e).value/t,duration:B(i)}}(e,100,eq);return e.ease=t.ease,e.duration=N(t.duration),e.type="keyframes",e};let eG=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function eZ(e,t,r,n){if(e===t&&r===n)return u;let i=t=>(function(e,t,r,n,i){let s,o,a=0;do(s=eG(o=t+(r-t)/2,n,i)-e)>0?r=o:t=o;while(Math.abs(s)>1e-7&&++a<12);return o})(t,0,1,e,r);return e=>0===e||1===e?e:eG(i(e),t,n)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e4=e=>t=>1-e(1-t),e3=eZ(.33,1.53,.69,.99),e5=e4(e3),e8=e2(e5),e6=e=>(e*=2)<1?.5*e5(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e9=e4(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tr={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e9,backIn:e5,backInOut:e8,backOut:e3,anticipate:e6},tn=e=>"string"==typeof e,ti=e=>{if(tt(e)){z(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return eZ(t,r,n,i)}return tn(e)?(z(void 0!==tr[e],`Invalid easing type '${e}'`),tr[e]):e},ts=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n};function to({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){var i;let s=e1(n)?n.map(ti):ti(n),o={done:!1,value:t[0]},a=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let s=e.length;if(z(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let n=[],i=r||d.mix||eN,s=e.length-1;for(let r=0;r<s;r++){let s=i(e[r],e[r+1]);t&&(s=O(Array.isArray(t)?t[r]||u:t,s)),n.push(s)}return n}(t,n,i),l=a.length,c=r=>{if(o&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=ts(e[n],e[n+1],r);return a[n](i)};return r?t=>c(I(e[0],e[s-1],t)):c}((i=r&&r.length===t.length?r:function(e){let t=[0];return!function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=ts(0,t,n);e.push(eS(r,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}let ta=e=>null!==e;function tl(e,{repeat:t,repeatType:r="loop"},n,i=1){let s=e.filter(ta),o=i<0||t&&"loop"!==r&&t%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let tu={decay:eK,inertia:eK,tween:to,keyframes:to,spring:eq};function td(e){"string"==typeof e.type&&(e.type=tu[e.type])}class tc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tp extends tc{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;td(e);let{type:t=to,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:s=0}=e,{keyframes:o}=e,a=t||to;a!==to&&"number"!=typeof o[0]&&(this.mixKeyframes=O(th,eN(o[0],o[1])),o=[0,100]);let l=a({...e,keyframes:o});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=e$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:d,repeatType:c,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let y=this.currentTime,b=r;if(d){let e=Math.min(this.currentTime,n)/o,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,d+1))%2&&("reverse"===c?(r=1-r,h&&(r-=h/o)):"mirror"===c&&(b=s)),y=I(0,1,r)*o}let x=v?{done:!1,value:u[0]}:b.next(y);i&&(x.value=i(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==eK&&(x.value=tl(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=N(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(E.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tm=e=>180*e/Math.PI,tf=e=>tv(tm(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tf,rotateZ:tf,skewX:e=>tm(Math.atan(e[1])),skewY:e=>tm(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tv=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tx={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tb,scale:e=>(ty(e)+tb(e))/2,rotateX:e=>tv(tm(Math.atan2(e[6],e[5]))),rotateY:e=>tv(tm(Math.atan2(-e[2],e[0]))),rotateZ:tf,rotate:tf,skewX:e=>tm(Math.atan(e[4])),skewY:e=>tm(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tk(e,t){let r,n;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=tx,n=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=tg,n=t}if(!n)return tw(t);let s=r[t],o=n[1].split(",").map(tT);return"function"==typeof s?s(o):o[s]}let tP=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return tk(r,t)};function tT(e){return parseFloat(e.trim())}let tE=e=>e===q||e===eu,tS=new Set(["x","y","z"]),tA=y.filter(e=>!tS.has(e)),tM={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tk(t,"x"),y:(e,{transform:t})=>tk(t,"y")};tM.translateX=tM.x,tM.translateY=tM.y;let tC=new Set,tD=!1,tF=!1,tR=!1;function tV(){if(tF){let e=Array.from(tC).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tF=!1,tD=!1,tC.forEach(e=>e.complete(tR)),tC.clear()}function tj(){tC.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tF=!0)})}class tL{constructor(e,t,r,n,i,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tC.add(this),tD||(tD=!0,m.read(tj),m.resolveKeyframes(tV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){let i=n?.get(),s=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===i&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tC.delete(this)}cancel(){"scheduled"===this.state&&(tC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tO=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tN=tI(()=>void 0!==window.ScrollTimeline),tB={},tU=function(e,t){let r=tI(e);return()=>tB[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),t$=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,tz={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:t$([0,.65,.55,1]),circOut:t$([.55,0,1,.45]),backIn:t$([.31,.01,.66,-.59]),backOut:t$([.33,1.53,.69,.99])};function tW(e){return"function"==typeof e&&"applyToOptions"in e}class t_ extends tc{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=s,this.options=e,z("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tW(e)&&tU()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:n=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let d={[t]:r};l&&(d.offset=l);let c=function e(t,r){if(t)return"function"==typeof t?tU()?eU(t,r):"ease-out":tt(t)?t$(t):Array.isArray(t)?t.map(t=>e(t,r)||tz.easeOut):tz[t]}(a,i);Array.isArray(c)&&(d.easing=c),h.value&&U.waapi++;let p={delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=e.animate(d,p);return h.value&&m.finished.finally(()=>{U.waapi--}),m}(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){tO(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=N(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tN())?(this.animation.timeline=e,u):t(this)}}let tY={anticipate:e6,backInOut:e8,circInOut:te};class tH extends t_{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tY&&(e.ease=tY[e.ease])}(e),td(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:n,element:i,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let o=new tp({...s,autoplay:!1}),a=N(this.finishedTime??this.time);t.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eP.test(e)||"0"===e)&&!e.startsWith("url("));var tq,tK,tG=r(18171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends tc{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();let c={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:s,name:a,motionValue:l,element:u,...d},h=u?.KeyframeResolver||tL;this.keyframeResolver=new h(o,(e,t,r)=>this.onKeyframesResolved(e,t,c,!r),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;let{name:i,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=r;this.resolvedAt=E.now(),!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],o=tX(i,t),a=tX(s,t);return $(o===a,`You are trying to animate ${t} from "${i}" to "${s}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||tW(r))&&n)}(e,i,s,o)&&((d.instantAnimations||!a)&&c?.(tl(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},p=!l&&function(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:s,type:o}=e;if(!(0,tG.s)(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return tQ()&&r&&tZ.has(r)&&("transform"!==r||!l)&&!a&&!n&&"mirror"!==i&&0!==s&&"inertia"!==o}(h)?new tH({...h,element:h.motionValue.owner.current}):new tp(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tR=!0,tj(),tV(),tR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t4={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t5=(e,{keyframes:t})=>t.length>2?t4:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t8=(e,t,r,n={},i,s)=>o=>{let a=l(n,e)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=N(u);let h={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-c,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}(a)&&Object.assign(h,t5(e,h)),h.duration&&(h.duration=N(h.duration)),h.repeatDelay&&(h.repeatDelay=N(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(p=!0)),(d.instantAnimations||d.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(t0),s=t&&"loop"!==r&&t%2==1?0:i.length-1;return i[s]}(h.keyframes,a);if(void 0!==e)return void m.update(()=>{h.onUpdate(e),h.onComplete()})}return a.isSync?new tp(h):new tJ(h)};function t6(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...u}=t;n&&(s=n);let d=[],c=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||c&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(c,t))continue;let o={delay:r,...l(s||{},t)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(i)&&i===a&&!o.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let r=e.props[j];if(r){let e=window.MotionHandoffAnimation(r,t,m);null!==e&&(o.startTime=e,h=!0)}}R(e,t),n.start(t8(t,n,i,e.shouldReduceMotion&&x.has(t)?{type:!1}:o,e,h));let p=n.animation;p&&d.push(p)}return o&&Promise.all(d).then(()=>{m.update(()=>{o&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=a(e,t)||{};for(let t in i={...i,...r}){var s;let r=D(s=i[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,C(r))}}(e,o)})}),d}function t7(e,t,r={}){let n=a(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let s=n?()=>Promise.all(t6(e,n,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=i;return function(e,t,r=0,n=0,i=0,s=1,o){let a=[],l=e.variantChildren.size,u=(l-1)*i,d="function"==typeof n,c=d?e=>n(e,l):1===s?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(t9).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(t7(e,t,{...o,delay:r+(d?0:n)+c(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n,s,o,a,r)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([s(),o(r.delay)]);{let[e,t]="beforeChildren"===l?[s,o]:[o,s];return e().then(()=>t())}}function t9(e,t){return e.sortNodePosition(t)}function re(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function rt(e){return"string"==typeof e||Array.isArray(e)}let rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...rr],ri=rn.length,rs=[...rr].reverse(),ro=rr.length;function ra(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rl(){return{animate:ra(!0),whileInView:ra(),whileHover:ra(),whileTap:ra(),whileDrag:ra(),whileFocus:ra(),exit:ra()}}class ru{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rd extends ru{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>t7(e,t,r)));else if("string"==typeof t)n=t7(e,t,r);else{let i="function"==typeof t?a(e,t,r.custom):t;n=Promise.all(t6(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rl(),n=!0,s=t=>(r,n)=>{let i=a(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function o(o){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<ri;e++){let n=rn[e],i=t.props[n];(rt(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},d=[],c=new Set,h={},p=1/0;for(let t=0;t<ro;t++){var m,f;let a=rs[t],g=r[a],v=void 0!==l[a]?l[a]:u[a],y=rt(v),b=a===o?g.isActive:null;!1===b&&(p=t);let x=v===u[a]&&v!==l[a]&&y;if(x&&n&&e.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...h},!g.isActive&&null===b||!v&&!g.prevProp||i(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!re(f,m)),k=w||a===o&&g.isActive&&!x&&y||t>p&&y,P=!1,T=Array.isArray(v)?v:[v],E=T.reduce(s(a),{});!1===b&&(E={});let{prevResolvedValues:S={}}=g,A={...S,...E},M=t=>{k=!0,c.has(t)&&(P=!0,c.delete(t)),g.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in A){let t=E[e],r=S[e];if(h.hasOwnProperty(e))continue;let n=!1;(D(t)&&D(r)?re(t,r):t===r)?void 0!==t&&c.has(e)?M(e):g.protectedKeys[e]=!0:null!=t?M(e):c.add(e)}g.prevProp=v,g.prevResolvedValues=E,g.isActive&&(h={...h,...E}),n&&e.blockInitialAnimation&&(k=!1);let C=!(x&&w)||P;k&&C&&d.push(...T.map(e=>({animation:e,options:{type:a}})))}if(c.size){let t={};if("boolean"!=typeof l.initial){let r=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}c.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null}),d.push({animation:t})}let g=!!d.length;return n&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(d):Promise.resolve()}return{animateChanges:o,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),r[t].isActive=n;let i=o(t);for(let e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rl(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rc=0;class rh extends ru{constructor(){super(...arguments),this.id=rc++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rp={x:!1,y:!1};function rm(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let rf=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rg(e){return{point:{x:e.pageX,y:e.pageY}}}let rv=e=>t=>rf(t)&&e(t,rg(t));function ry(e,t,r,n){return rm(e,t,rv(r),n)}function rb({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rx(e){return e.max-e.min}function rw(e,t,r,n=.5){e.origin=n,e.originPoint=eS(t.min,t.max,e.origin),e.scale=rx(r)/rx(t),e.translate=eS(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rk(e,t,r,n){rw(e.x,t.x,r.x,n?n.originX:void 0),rw(e.y,t.y,r.y,n?n.originY:void 0)}function rP(e,t,r){e.min=r.min+t.min,e.max=e.min+rx(t)}function rT(e,t,r){e.min=t.min-r.min,e.max=e.min+rx(t)}function rE(e,t,r){rT(e.x,t.x,r.x),rT(e.y,t.y,r.y)}let rS=()=>({translate:0,scale:1,origin:0,originPoint:0}),rA=()=>({x:rS(),y:rS()}),rM=()=>({min:0,max:0}),rC=()=>({x:rM(),y:rM()});function rD(e){return[e("x"),e("y")]}function rF(e){return void 0===e||1===e}function rR({scale:e,scaleX:t,scaleY:r}){return!rF(e)||!rF(t)||!rF(r)}function rV(e){return rR(e)||rj(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rj(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function rL(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function rO(e,t=0,r=1,n,i){e.min=rL(e.min,t,r,n,i),e.max=rL(e.max,t,r,n,i)}function rI(e,{x:t,y:r}){rO(e.x,t.translate,t.scale,t.originPoint),rO(e.y,r.translate,r.scale,r.originPoint)}function rN(e,t){e.min=e.min+t,e.max=e.max+t}function rB(e,t,r,n,i=.5){let s=eS(e.min,e.max,i);rO(e,t,r,s,n)}function rU(e,t){rB(e.x,t.x,t.scaleX,t.scale,t.originX),rB(e.y,t.y,t.scaleY,t.scale,t.originY)}function r$(e,t){return rb(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let rz=({current:e})=>e?e.ownerDocument.defaultView:null;function rW(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let r_=(e,t)=>Math.abs(e-t);class rY{constructor(e,t,{transformPagePoint:r,contextWindow:n=window,dragSnapToOrigin:i=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rq(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(r_(e.x,t.x)**2+r_(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=g;this.history.push({...n,timestamp:i});let{onStart:s,onMove:o}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rH(t,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rq("pointercancel"===e.type?this.lastMoveEventInfo:rH(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),n&&n(e,s)},!rf(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.distanceThreshold=s,this.contextWindow=n||window;let o=rH(rg(e),this.transformPagePoint),{point:a}=o,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rq(o,this.history)),this.removeListeners=O(ry(this.contextWindow,"pointermove",this.handlePointerMove),ry(this.contextWindow,"pointerup",this.handlePointerUp),ry(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function rH(e,t){return t?{point:t(e.point)}:e}function rX(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rq({point:e},t){return{point:e,delta:rX(e,rK(t)),offset:rX(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rK(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>N(.1)));)r--;if(!n)return{x:0,y:0};let s=B(i.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(i.x-n.x)/s,y:(i.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,.1)}}function rK(e){return e[e.length-1]}function rG(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rZ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function rQ(e,t,r){return{min:rJ(e,t),max:rJ(e,r)}}function rJ(e,t){return"number"==typeof e?e:e[t]||0}let r0=new WeakMap;class r1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rC(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:r}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rY(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rg(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rp[e])return null;else return rp[e]=!0,()=>{rp[e]=!1};return rp.x||rp.y?null:(rp.x=rp.y=!0,()=>{rp.x=rp.y=!1})}(r),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rD(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=rx(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&m.postRender(()=>i(e,t)),R(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:o}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rD(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:r,contextWindow:rz(this.visualElement)})}stop(e,t){let r=e||this.latestPointerEvent,n=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!n||!r)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&m.postRender(()=>o(r,n))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!r2(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?eS(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?eS(r,e,n.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),i.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&rW(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rG(e.x,r,i),y:rG(e.y,t,n)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rQ(e,"left","right"),y:rQ(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rD(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rW(t))return!1;let n=t.current;z(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let s=function(e,t,r){let n=r$(e,r),{scroll:i}=t;return i&&(rN(n.x,i.offset.x),rN(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),o=(e=i.layout.layoutBox,{x:rZ(e.x,s.x),y:rZ(e.y,s.y)});if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=rb(e))}return o}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rD(o=>{if(!r2(o,t,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return R(this.visualElement,e),r.start(t8(e,r,0,t,this.visualElement,!1))}stopAnimation(){rD(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rD(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rD(t=>{let{drag:r}=this.getProps();if(!r2(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:s}=n.layout.layoutBox[t];i.set(e[t]-eS(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rW(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rD(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rx(e),i=rx(t);return i>n?r=ts(t.min,t.max-n,e.min):n>i&&(r=ts(e.min,e.max-i,t.min)),I(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rD(t=>{if(!r2(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:s}=this.constraints[t];r.set(eS(i,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;r0.set(this.visualElement,this);let e=ry(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rW(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),m.read(t);let i=rm(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rD(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:s=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:s,dragMomentum:o}}}function r2(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class r4 extends ru{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new r1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let r3=e=>(t,r)=>{e&&m.postRender(()=>e(t,r))};class r5 extends ru{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new rY(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rz(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:r3(e),onStart:r3(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&m.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=ry(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r8=r(60687);let{schedule:r6}=p(queueMicrotask,!1);var r7=r(43210),r9=r(86044),ne=r(12157);let nt=(0,r7.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ni={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let r=nn(e,t.target.x),n=nn(e,t.target.y);return`${r}% ${n}%`}},ns={},no=!1;class na extends r7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;for(let e in nu)ns[e]=nu[e],_(e)&&(ns[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),no&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:s}=r;return s&&(s.isPresent=i,no=!0,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?s.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?s.promote():s.relegate()||m.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),r6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nl(e){let[t,r]=(0,r9.xQ)(),n=(0,r7.useContext)(ne.L);return(0,r8.jsx)(na,{...e,layoutGroup:n,switchLayoutGroup:(0,r7.useContext)(nt),isPresent:t,safeToRemove:r})}let nu={borderRadius:{...ni,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ni,borderTopRightRadius:ni,borderBottomLeftRadius:ni,borderBottomRightRadius:ni,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=eP.parse(e);if(n.length>5)return e;let i=eP.createTransformer(e),s=+("number"!=typeof n[0]),o=r.x.scale*t.x,a=r.y.scale*t.y;n[0+s]/=o,n[1+s]/=a;let l=eS(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),i(n)}}};var nd=r(74479);function nc(e){return(0,nd.G)(e)&&"ownerSVGElement"in e}let nh=(e,t)=>e.depth-t.depth;class np{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){k(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nh),this.isDirty=!1,this.children.forEach(e)}}function nm(e){return F(e)?e.get():e}let nf=["TopLeft","TopRight","BottomLeft","BottomRight"],ng=nf.length,nv=e=>"string"==typeof e?parseFloat(e):e,ny=e=>"number"==typeof e||eu.test(e);function nb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nx=nk(0,.5,e9),nw=nk(.5,.95,u);function nk(e,t,r){return n=>n<e?0:n>t?1:r(ts(e,t,n))}function nP(e,t){e.min=t.min,e.max=t.max}function nT(e,t){nP(e.x,t.x),nP(e.y,t.y)}function nE(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nS(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nA(e,t,[r,n,i],s,o){!function(e,t=0,r=1,n=.5,i,s=e,o=e){if(el.test(t)&&(t=parseFloat(t),t=eS(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=eS(s.min,s.max,n);e===s&&(a-=t),e.min=nS(e.min,t,r,a,i),e.max=nS(e.max,t,r,a,i)}(e,t[r],t[n],t[i],t.scale,s,o)}let nM=["x","scaleX","originX"],nC=["y","scaleY","originY"];function nD(e,t,r,n){nA(e.x,t,nM,r?r.x:void 0,n?n.x:void 0),nA(e.y,t,nC,r?r.y:void 0,n?n.y:void 0)}function nF(e){return 0===e.translate&&1===e.scale}function nR(e){return nF(e.x)&&nF(e.y)}function nV(e,t){return e.min===t.min&&e.max===t.max}function nj(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nL(e,t){return nj(e.x,t.x)&&nj(e.y,t.y)}function nO(e){return rx(e.x)/rx(e.y)}function nI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nN{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(k(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nB={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nU=["","X","Y","Z"],n$=0;function nz(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function nW({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=n$++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(nB.nodes=nB.calculatedTargetDeltas=nB.calculatedProjections=0),this.nodes.forEach(nH),this.nodes.forEach(nJ),this.nodes.forEach(n0),this.nodes.forEach(nX),h.addProjectionMetrics&&h.addProjectionMetrics(nB)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new np)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new P),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=nc(t)&&!(nc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=0,i=()=>this.root.updateBlockedByResize=!1;m.read(()=>{n=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==n&&(n=e,this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=E.now(),n=({timestamp:i})=>{let s=i-r;s>=250&&(f(n),e(s-t))};return m.setup(n,!0),()=>f(n)}(i,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(nQ)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||i.getDefaultTransition()||n8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=i.getProps(),u=!this.targetLayout||!nL(this.targetLayout,n),d=!t&&r;if(this.options.layoutRoot||this.resumeFrom||d||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(s,"layout"),onPlay:o,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,d)}else t||nQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[j];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nK);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nG);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nZ),this.nodes.forEach(n_),this.nodes.forEach(nY)):this.nodes.forEach(nG),this.clearAllSnapshots();let e=E.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nq),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rx(this.snapshot.measuredBox.x)||rx(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nR(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||rV(this.latestValues)||s)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),n9((t=n).x),n9(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rC();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(rN(t.x,e.offset.x),rN(t.y,e.offset.y))}return t}removeElementScroll(e){let t=rC();if(nT(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:s}=n;n!==this.root&&i&&s.layoutScroll&&(i.wasRoot&&nT(t,e),rN(t.x,i.offset.x),rN(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let r=rC();nT(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rU(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rV(n.latestValues)&&rU(r,n.latestValues)}return rV(this.latestValues)&&rU(r,this.latestValues),r}removeTransform(e){let t=rC();nT(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rV(r.latestValues))continue;rR(r.latestValues)&&r.updateSnapshot();let n=rC();nT(n,r.measurePageBox()),nD(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rV(this.latestValues)&&nD(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rC(),this.relativeTargetOrigin=rC(),rE(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=rC(),this.targetWithTransforms=rC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,rP(s.x,o.x,a.x),rP(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nT(this.target,this.layout.layoutBox),rI(this.target,this.targetDelta)):nT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rC(),this.relativeTargetOrigin=rC(),rE(this.relativeTargetOrigin,this.target,e.target),nT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&nB.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||rR(this.parent.latestValues)||rj(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===g.timestamp&&(r=!1),r)return;let{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||i))return;nT(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(e,t,r,n=!1){let i,s,o=r.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){s=(i=r[a]).projectionDelta;let{visualElement:o}=i.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rU(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,rI(e,s)),n&&rV(i.latestValues)&&rU(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rC());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nE(this.prevProjectionDelta.x,this.projectionDelta.x),nE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rk(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),h.value&&nB.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rA(),this.projectionDelta=rA(),this.projectionDeltaWithTransform=rA()}setAnimationOrigin(e,t=!1){let r,n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o=rA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=rC(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,c=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(n5));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(n4(o.x,e.x,n),n4(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;rE(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,n3(p.x,m.x,f.x,g),n3(p.y,m.y,f.y,g),r&&(u=this.relativeTarget,h=r,nV(u.x,h.x)&&nV(u.y,h.y))&&(this.isProjectionDirty=!1),r||(r=rC()),nT(r,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,r,n,i,s){i?(e.opacity=eS(0,r.opacity??1,nx(n)),e.opacityExit=eS(t.opacity??1,0,nw(n))):s&&(e.opacity=eS(t.opacity??1,r.opacity??1,n));for(let i=0;i<ng;i++){let s=`border${nf[i]}Radius`,o=nb(t,s),a=nb(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ny(o)===ny(a)?(e[s]=Math.max(eS(nv(o),nv(a),n),0),(el.test(a)||el.test(o))&&(e[s]+="%")):e[s]=a)}(t.rotate||r.rotate)&&(e.rotate=eS(t.rotate||0,r.rotate||0,n))}(s,i,this.latestValues,n,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{nr.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(e,t,r){let n=F(e)?e:C(e);return n.start(t8("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ie(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rC();let t=rx(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rx(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nT(t,r),rU(t,i),rk(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nN),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&nz("z",e,n,this.animationValues);for(let t=0;t<nU.length;t++)nz(`rotate${nU[t]}`,e,n,this.animationValues),nz(`skew${nU[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let r=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=nm(t?.pointerEvents)||"",e.transform=r?r(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nm(t?.pointerEvents)||""),this.hasProjected&&!rV(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(e,t,r){let n="",i=e.x.translate/t.x,s=e.y.translate/t.y,o=r?.z||0;if((i||s||o)&&(n=`translate3d(${i}px, ${s}px, ${o}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:s,skewX:o,skewY:a}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);r&&(s=r(i,s)),e.transform=s;let{x:o,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ns){if(void 0===i[t])continue;let{correct:r,applyTo:o,isCSSVariable:a}=ns[t],l="none"===s?i[t]:r(i[t],n);if(o){let t=o.length;for(let r=0;r<t;r++)e[o[r]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=n===this?nm(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nK),this.root.sharedNodes.clear()}}}function n_(e){e.updateLayout()}function nY(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,s=t.source!==e.layout.source;"size"===i?rD(e=>{let n=s?t.measuredBox[e]:t.layoutBox[e],i=rx(n);n.min=r[e].min,n.max=n.min+i}):ie(i,t.layoutBox,r)&&rD(n=>{let i=s?t.measuredBox[n]:t.layoutBox[n],o=rx(r[n]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+o)});let o=rA();rk(o,r,t.layoutBox);let a=rA();s?rk(a,e.applyTransform(n,!0),t.measuredBox):rk(a,r,t.layoutBox);let l=!nR(o),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:s}=n;if(i&&s){let o=rC();rE(o,t.layoutBox,i.layoutBox);let a=rC();rE(a,r,s.layoutBox),nL(o,a)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nH(e){h.value&&nB.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nX(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nq(e){e.clearSnapshot()}function nK(e){e.clearMeasurements()}function nG(e){e.isLayoutDirty=!1}function nZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nJ(e){e.resolveTargetDelta()}function n0(e){e.calcProjection()}function n1(e){e.resetSkewAndRotation()}function n2(e){e.removeLeadSnapshot()}function n4(e,t,r){e.translate=eS(t.translate,0,r),e.scale=eS(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function n3(e,t,r,n){e.min=eS(t.min,r.min,n),e.max=eS(t.max,r.max,n)}function n5(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n6=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),n7=n6("applewebkit/")&&!n6("chrome/")?Math.round:u;function n9(e){e.min=n7(e.min),e.max=n7(e.max)}function ie(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nO(t)-nO(r)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=nW({attachResizeListener:(e,t)=>rm(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},is=nW({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function io(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(void 0)??t.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function ia(e){return!("touch"===e.pointerType||rp.x||rp.y)}function il(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&m.postRender(()=>i(t,rg(t)))}class iu extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,s]=io(e,r),o=e=>{if(!ia(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let s=e=>{ia(e)&&(n(e),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,i)};return n.forEach(e=>{e.addEventListener("pointerenter",o,i)}),s}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class id extends ru{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(rm(this.node.current,"focus",()=>this.onFocus()),rm(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ic=(e,t)=>!!t&&(e===t||ic(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iv=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=im(()=>{if(ip.has(r))return;ig(r,"down");let e=im(()=>{ig(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>ig(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function iy(e){return rf(e)&&!(rp.x||rp.y)}function ib(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&m.postRender(()=>i(t,rg(t)))}class ix extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,s]=io(e,r),o=e=>{let n=e.currentTarget;if(!iy(e))return;ip.add(n);let s=t(n,e),o=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ip.has(n)&&ip.delete(n),iy(e)&&"function"==typeof s&&s(e,{success:t})},a=e=>{o(e,n===window||n===document||r.useGlobalTarget||ic(n,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{((r.useGlobalTarget?window:e).addEventListener("pointerdown",o,i),(0,tG.s)(e))&&(e.addEventListener("focus",e=>iv(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ik=new WeakMap,iP=e=>{let t=iw.get(e.target);t&&t(e)},iT=e=>{e.forEach(iP)},iE={some:0,all:1};class iS extends ru{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iE[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;ik.has(r)||ik.set(r,{});let n=ik.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iT,{root:e,...t})),n[i]}(t);return iw.set(e,r),n.observe(e),()=>{iw.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),s=t?r:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,r7.createContext)({strict:!1});var iM=r(32582);let iC=(0,r7.createContext)({});function iD(e){return i(e.animate)||rn.some(t=>rt(e[t]))}function iF(e){return!!(iD(e)||e.variants)}function iR(e){return Array.isArray(e)?e.join(" "):e}var iV=r(7044);let ij={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in ij)iL[e]={isEnabled:t=>ij[e].some(e=>!!t[e])};let iO=Symbol.for("motionComponentSymbol");var iI=r(21279),iN=r(15124);function iB(e,{layout:t,layoutId:r}){return b.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!ns[e]||"opacity"===e)}let iU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,i$={...q,transform:Math.round},iz={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:ea,rotateX:ea,rotateY:ea,rotateZ:ea,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ea,skewX:ea,skewY:ea,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:K,originX:eh,originY:eh,originZ:eu,zIndex:i$,fillOpacity:K,strokeOpacity:K,numOctaves:i$},iW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i_=y.length;function iY(e,t,r){let{style:n,vars:i,transformOrigin:s}=e,o=!1,a=!1;for(let e in t){let r=t[e];if(b.has(e)){o=!0;continue}if(_(e)){i[e]=r;continue}{let t=iU(r,iz[e]);e.startsWith("origin")?(a=!0,s[e]=t):n[e]=t}}if(!t.transform&&(o||r?n.transform=function(e,t,r){let n="",i=!0;for(let s=0;s<i_;s++){let o=y[s],a=e[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||r){let e=iU(a,iz[o]);if(!l){i=!1;let t=iW[o]||o;n+=`${t}(${e}) `}r&&(t[o]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:r=0}=s;n.transformOrigin=`${e} ${t} ${r}`}}let iH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,r){for(let n in t)F(t[n])||iB(n,r)||(e[n]=t[n])}let iq={offset:"stroke-dashoffset",array:"stroke-dasharray"},iK={offset:"strokeDashoffset",array:"strokeDasharray"};function iG(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:s=1,pathOffset:o=0,...a},l,u,d){if(iY(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:c,style:h}=e;c.transform&&(h.transform=c.transform,delete c.transform),(h.transform||c.transformOrigin)&&(h.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),h.transform&&(h.transformBox=d?.transformBox??"fill-box",delete c.transformBox),void 0!==t&&(c.x=t),void 0!==r&&(c.y=r),void 0!==n&&(c.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let s=i?iq:iK;e[s.offset]=eu.transform(-n);let o=eu.transform(t),a=eu.transform(r);e[s.array]=`${o} ${a}`}(c,i,s,o,!1)}let iZ=()=>({...iH(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i4(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i3=r(72789);let i5=e=>(t,r)=>{let n=(0,r7.useContext)(iC),s=(0,r7.useContext)(iI.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,s){return{latestValues:function(e,t,r,n){let s={},a=n(e,{});for(let e in a)s[e]=nm(a[e]);let{initial:l,animate:u}=e,d=iD(e),c=iF(e);t&&c&&!d&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!r&&!1===r.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let t=Array.isArray(p)?p:[p];for(let r=0;r<t.length;r++){let n=o(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(s[e]=t)}for(let t in e)s[t]=e[t]}}}return s}(r,n,s,e),renderState:t()}})(e,t,n,s);return r?a():(0,i3.M)(a)};function i8(e,t,r){let{style:n}=e,i={};for(let s in n)(F(n[s])||t.style&&F(t.style[s])||iB(s,e)||r?.getValue(s)?.liveStyle!==void 0)&&(i[s]=n[s]);return i}let i6={useVisualState:i5({scrapeMotionValuesFromProps:i8,createRenderState:iH})};function i7(e,t,r){let n=i8(e,t,r);for(let r in e)(F(e[r])||F(t[r]))&&(n[-1!==y.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let i9={useVisualState:i5({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},se=e=>t=>t.test(e),st=[q,eu,el,ea,ec,ed,{test:e=>"auto"===e,parse:e=>e}],sr=e=>st.find(se(e)),sn=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=e=>/^0[^.\s]+$/u.test(e),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(Q)||[];if(!n)return e;let i=r.replace(n,""),s=+!!so.has(t);return n!==r&&(s*=100),t+"("+s+i+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...eP,getAnimatableNone:e=>{let t=e.match(sl);return t?t.map(sa).join(" "):e}},sd={...iz,color:em,backgroundColor:em,outlineColor:em,fill:em,stroke:em,borderColor:em,borderTopColor:em,borderRightColor:em,borderBottomColor:em,borderLeftColor:em,filter:su,WebkitFilter:su},sc=e=>sd[e];function sh(e,t){let r=sc(e);return r!==su&&(r=eP),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let sp=new Set(["auto","none","0"]);class sm extends tL{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&H(n=n.trim())){let i=function e(t,r,n=1){z(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,s]=function(e){let t=si.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${r??n}`,i]}(t);if(!i)return;let o=window.getComputedStyle(r).getPropertyValue(i);if(o){let e=o.trim();return sn(e)?parseFloat(e):e}return H(s)?e(s,r,n+1):s}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!x.has(r)||2!==e.length)return;let[n,i]=e,s=sr(n),o=sr(i);if(s!==o)if(tE(s)&&tE(o))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else tM[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!sp.has(t)&&eb(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=sh(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tM[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,s=r[i];r[i]=tM[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let sf=[...st,em,eP],sg=e=>sf.find(se(e)),sv={current:null},sy={current:!1},sb=new WeakMap,sx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=E.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=iD(t),this.isVariantNode=iF(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==a[e]&&F(t)&&t.set(a[e],!1)}}mount(e){this.current=e,sb.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sy.current||function(){if(sy.current=!0,iV.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>sv.current=e.matches;e.addEventListener("change",t),t()}else sv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=b.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),s(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rC()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sx.length;t++){let r=sx[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],s=r[n];if(F(i))e.addValue(n,i);else if(F(s))e.addValue(n,C(i,{owner:e}));else if(s!==i)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,C(void 0!==t?t:i,{owner:e}))}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=C(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(sn(r)||ss(r))?r=parseFloat(r):!sg(r)&&eP.test(t)&&(r=sh(e,t)),this.setBaseTarget(e,F(r)?r.get():r)),F(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=o(this.props,r,this.presenceContext?.custom);n&&(t=n[e])}if(r&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||F(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new P),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sk extends sw{constructor(){super(...arguments),this.KeyframeResolver=sm}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;F(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function sP(e,{style:t,vars:r},n,i){let s,o=e.style;for(s in t)o[s]=t[s];for(s in i?.applyProjectionStyles(o,n),r)o.setProperty(s,r[s])}class sT extends sk{constructor(){super(...arguments),this.type="html",this.renderInstance=sP}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tw(t):tP(e,t);{let r=window.getComputedStyle(e),n=(_(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return r$(e,t)}build(e,t,r){iY(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i8(e,t,r)}}let sE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sS extends sk{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rC}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=sc(t);return e&&e.default||0}return t=sE.has(t)?t:V(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return i7(e,t,r)}build(e,t,r){iG(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){for(let r in sP(e,t,void 0,n),t.attrs)e.setAttribute(sE.has(r)?r:V(r),t.attrs[r])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let sA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((tq={animation:{Feature:rd},exit:{Feature:rh},inView:{Feature:iS},tap:{Feature:ix},focus:{Feature:id},hover:{Feature:iu},pan:{Feature:r5},drag:{Feature:r4,ProjectionNode:is,MeasureLayout:nl},layout:{ProjectionNode:is,MeasureLayout:nl}},tK=(e,t)=>i4(e)?new sS(t):new sT(t,{allowProjection:e!==r7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){function s(e,s){var o,a,l;let u,d={...(0,r7.useContext)(iM.Q),...e,layoutId:function({layoutId:e}){let t=(0,r7.useContext)(ne.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:c}=d,h=function(e){let{initial:t,animate:r}=function(e,t){if(iD(e)){let{initial:t,animate:r}=e;return{initial:!1===t||rt(t)?t:void 0,animate:rt(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,r7.useContext)(iC));return(0,r7.useMemo)(()=>({initial:t,animate:r}),[iR(t),iR(r)])}(e),p=n(e,c);if(!c&&iV.B){a=0,l=0,(0,r7.useContext)(iA).strict;let e=function(e){let{drag:t,layout:r}=iL;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);u=e.MeasureLayout,h.visualElement=function(e,t,r,n,i){let{visualElement:s}=(0,r7.useContext)(iC),o=(0,r7.useContext)(iA),a=(0,r7.useContext)(iI.t),l=(0,r7.useContext)(iM.Q).reducedMotion,u=(0,r7.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:s,props:r,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let d=u.current,c=(0,r7.useContext)(nt);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,r,n){let{layoutId:i,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||a&&rW(a),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:d,layoutScroll:l,layoutRoot:u})}(u.current,r,i,c);let h=(0,r7.useRef)(!1);(0,r7.useInsertionEffect)(()=>{d&&h.current&&d.update(r,a)});let p=r[j],m=(0,r7.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,iN.E)(()=>{d&&(h.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),r6.render(d.render),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,r7.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),d}(i,p,d,t,e.ProjectionNode)}return(0,r8.jsxs)(iC.Provider,{value:h,children:[u&&h.visualElement?(0,r8.jsx)(u,{visualElement:h.visualElement,...d}):null,r(i,e,(o=h.visualElement,(0,r7.useCallback)(e=>{e&&p.onMount&&p.onMount(e),o&&(e?o.mount(e):o.unmount()),s&&("function"==typeof s?s(e):rW(s)&&(s.current=e))},[o])),p,c,h.visualElement)]})}e&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let o=(0,r7.forwardRef)(s);return o[iO]=i,o}({...i4(e)?i9:i6,preloadedFeatures:tq,useRender:function(e=!1){return(t,r,n,{latestValues:i},s)=>{let o=(i4(t)?function(e,t,r,n){let i=(0,r7.useMemo)(()=>{let r=iZ();return iG(r,t,iQ(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return iX(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,r7.useMemo)(()=>{let r=iH();return iY(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,s,t),a=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===r&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==r7.Fragment?{...a,...o,ref:n}:{},{children:u}=r,d=(0,r7.useMemo)(()=>F(u)?u.get():u,[u]);return(0,r7.createElement)(t,{...l,children:d})}}(t),createVisualElement:tK,Component:e})}))},28947:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},32582:(e,t,r)=>{r.d(t,{Q:()=>n});let n=(0,r(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},37360:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39704:(e,t,r)=>{r.d(t,{_:()=>n});function n(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},39857:(e,t,r)=>{r.d(t,{$x:()=>l,El:()=>a,O_:()=>o,Uw:()=>s});var n=r(43210);let i=(0,n.createContext)(null);i.displayName="OpenClosedContext";var s=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(s||{});function o(){return(0,n.useContext)(i)}function a({value:e,children:t}){return n.createElement(i.Provider,{value:e},t)}function l({children:e}){return n.createElement(i.Provider,{value:null},e)}},40083:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44685:(e,t,r)=>{r.d(t,{Y:()=>n});function n(e,t,...r){if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let i=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(i,n),i}},44967:(e,t,r)=>{r.d(t,{P:()=>a,a:()=>o});var n=r(43210),i=r(52263);let s=Symbol();function o(e,t=!0){return Object.assign(e,{[s]:t})}function a(...e){let t=(0,n.useRef)(e),r=(0,i._)(e=>{for(let r of t.current)null!=r&&("function"==typeof r?r(e):r.current=e)});return e.every(e=>null==e||(null==e?void 0:e[s]))?void 0:r}},48143:(e,t,r)=>{r.d(t,{e:()=>function e(){let t=[],r={addEventListener:(e,t,n,i)=>(e.addEventListener(t,n,i),r.add(()=>e.removeEventListener(t,n,i))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>r.requestAnimationFrame(()=>r.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,n._)(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(t){let r=e();return t(r),this.add(()=>r.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let r=t.indexOf(e);if(r>=0)for(let e of t.splice(r,1))e()}),dispose(){for(let e of t.splice(0))e()}};return r}});var n=r(39704)},49384:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}},49625:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},52263:(e,t,r)=>{r.d(t,{_:()=>s});var n=r(43210),i=r(10327);let s=function(e){let t=(0,i.Y)(e);return n.useCallback((...e)=>t.current(...e),[t])}},52315:(e,t,r)=>{r.d(t,{s:()=>s});var n=r(43210),i=r(99923);let s=(e,t)=>{i._.isServer?(0,n.useEffect)(e,t):(0,n.useLayoutEffect)(e,t)}},53411:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62688:(e,t,r)=>{r.d(t,{A:()=>c});var n=r(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:s="",children:o,iconNode:d,...c},h)=>(0,n.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:a("lucide",s),...!o&&!l(c)&&{"aria-hidden":"true"},...c},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(o)?o:[o]])),c=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...s},l)=>(0,n.createElement)(d,{ref:l,iconNode:t,className:a(`lucide-${i(o(e))}`,`lucide-${e}`,r),...s}));return r.displayName=o(e),r}},66594:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]])},69334:(e,t,r)=>{r.d(t,{Ac:()=>o,Ci:()=>l,FX:()=>h,mK:()=>a,oE:()=>p});var n=r(43210),i=r(13337),s=r(44685),o=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(o||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function l(){let e,t,r=(e=(0,n.useRef)([]),t=(0,n.useCallback)(t=>{for(let r of e.current)null!=r&&("function"==typeof r?r(t):r.current=t)},[]),(...r)=>{if(!r.every(e=>null==e))return e.current=r,t});return(0,n.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:r,defaultTag:n,features:i,visible:o=!0,name:a,mergeRefs:l}){l=null!=l?l:d;let h=c(t,e);if(o)return u(h,r,n,a,l);let p=null!=i?i:0;if(2&p){let{static:e=!1,...t}=h;if(e)return u(t,r,n,a,l)}if(1&p){let{unmount:e=!0,...t}=h;return(0,s.Y)(+!e,{0:()=>null,1:()=>u({...t,hidden:!0,style:{display:"none"}},r,n,a,l)})}return u(h,r,n,a,l)})({mergeRefs:r,...e}),[r])}function u(e,t={},r,s,o){let{as:a=r,children:l,refName:d="ref",...h}=m(e,["unmount","static"]),f=void 0!==e.ref?{[d]:e.ref}:{},g="function"==typeof l?l(t):l;"className"in h&&h.className&&"function"==typeof h.className&&(h.className=h.className(t)),h["aria-labelledby"]&&h["aria-labelledby"]===h.id&&(h["aria-labelledby"]=void 0);let v={};if(t){let e=!1,r=[];for(let[n,i]of Object.entries(t))"boolean"==typeof i&&(e=!0),!0===i&&r.push(n.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(v["data-headlessui-state"]=r.join(" "),r))v[`data-${e}`]=""}if(a===n.Fragment&&(Object.keys(p(h)).length>0||Object.keys(p(v)).length>0))if(!(0,n.isValidElement)(g)||Array.isArray(g)&&g.length>1){if(Object.keys(p(h)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${s} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(p(h)).concat(Object.keys(p(v))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{var y;let e=g.props,t=null==e?void 0:e.className,r="function"==typeof t?(...e)=>(0,i.x)(t(...e),h.className):(0,i.x)(t,h.className),s=c(g.props,p(m(h,["ref"])));for(let e in v)e in s&&delete v[e];return(0,n.cloneElement)(g,Object.assign({},s,v,f,{ref:o((y=g,n.version.split(".")[0]>="19"?y.props.ref:y.ref),f.ref)},r?{className:r}:{}))}return(0,n.createElement)(a,Object.assign({},m(h,["ref"]),a!==n.Fragment&&f,a!==n.Fragment&&v),g)}function d(...e){return e.every(e=>null==e)?void 0:t=>{for(let r of e)null!=r&&("function"==typeof r?r(t):r.current=t)}}function c(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];if(t.disabled||t["aria-disabled"])for(let e in r)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(r[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in r)Object.assign(t,{[e](t,...n){for(let i of r[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;i(t,...n)}}});return t}function h(e){var t;return Object.assign((0,n.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function p(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function m(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}},72789:(e,t,r)=>{r.d(t,{M:()=>i});var n=r(43210);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},74479:(e,t,r)=>{r.d(t,{G:()=>n});function n(e){return"object"==typeof e&&null!==e}},82348:(e,t,r)=>{r.d(t,{QP:()=>eu});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||o(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),s=n?i(e.slice(1),n):void 0;if(s)return s;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},s=/^\[(.+)\]$/,o=e=>{if(s.test(e)){let t=s.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return d(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,c=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,s)=>{r.set(i,s),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,i=0,s=0;for(let o=0;o<e.length;o++){let a=e[o];if(0===n&&0===i){if(":"===a){r.push(e.slice(s,o)),s=o+1;continue}if("/"===a){t=o;continue}}"["===a?n++:"]"===a?n--:"("===a?i++:")"===a&&i--}let o=0===r.length?e:e.substring(s),a=p(o);return{modifiers:r,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},f=e=>({cache:c(e.cacheSize),parseClassName:h(e),sortModifiers:m(e),...n(e)}),g=/\s+/,v=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:s}=t,o=[],a=e.trim().split(g),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:u,modifiers:d,hasImportantModifier:c,baseClassName:h,maybePostfixModifierPosition:p}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let m=!!p,f=n(m?h.substring(0,p):h);if(!f){if(!m||!(f=n(h))){l=t+(l.length>0?" "+l:l);continue}m=!1}let g=s(d).join(":"),v=c?g+"!":g,y=v+f;if(o.includes(y))continue;o.push(y);let b=i(f,m);for(let e=0;e<b.length;++e){let t=b[e];o.push(v+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,E=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>P.test(e),D=e=>!!e&&!Number.isNaN(Number(e)),F=e=>!!e&&Number.isInteger(Number(e)),R=e=>e.endsWith("%")&&D(e.slice(0,-1)),V=e=>T.test(e),j=()=>!0,L=e=>E.test(e)&&!S.test(e),O=()=>!1,I=e=>A.test(e),N=e=>M.test(e),B=e=>!$(e)&&!X(e),U=e=>ee(e,ei,O),$=e=>w.test(e),z=e=>ee(e,es,L),W=e=>ee(e,eo,D),_=e=>ee(e,er,O),Y=e=>ee(e,en,N),H=e=>ee(e,el,I),X=e=>k.test(e),q=e=>et(e,es),K=e=>et(e,ea),G=e=>et(e,er),Z=e=>et(e,ei),Q=e=>et(e,en),J=e=>et(e,el,!0),ee=(e,t,r)=>{let n=w.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=k.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,es=e=>"length"===e,eo=e=>"number"===e,ea=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,n,i,s=function(a){return n=(r=f(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,s=o,o(a)};function o(e){let t=n(e);if(t)return t;let s=v(e,r);return i(e,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let e=x("color"),t=x("font"),r=x("text"),n=x("font-weight"),i=x("tracking"),s=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),d=x("shadow"),c=x("inset-shadow"),h=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),v=x("ease"),y=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),X,$],P=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],E=()=>[X,$,l],S=()=>[C,"full","auto",...E()],A=()=>[F,"none","subgrid",X,$],M=()=>["auto",{span:["full",F,X,$]},F,X,$],L=()=>[F,"auto",X,$],O=()=>["auto","min","max","fr",X,$],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...E()],et=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...E()],er=()=>[e,X,$],en=()=>[...w(),G,_,{position:[X,$]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",Z,U,{size:[X,$]}],eo=()=>[R,q,z],ea=()=>["","none","full",u,X,$],el=()=>["",D,q,z],eu=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ec=()=>[D,R,G,_],eh=()=>["","none",m,X,$],ep=()=>["none",D,X,$],em=()=>["none",D,X,$],ef=()=>[D,X,$],eg=()=>[C,"full",...E()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[V],breakpoint:[V],color:[j],container:[V],"drop-shadow":[V],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[V],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[V],shadow:[V],spacing:["px",D],text:[V],"text-shadow":[V],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,$,X,g]}],container:["container"],columns:[{columns:[D,$,X,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[F,"auto",X,$]}],basis:[{basis:[C,"full","auto",a,...E()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[D,C,"auto","initial","none",$]}],grow:[{grow:["",D,X,$]}],shrink:[{shrink:["",D,X,$]}],order:[{order:[F,"first","last","none",X,$]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:E()}],"gap-x":[{"gap-x":E()}],"gap-y":[{"gap-y":E()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:E()}],px:[{px:E()}],py:[{py:E()}],ps:[{ps:E()}],pe:[{pe:E()}],pt:[{pt:E()}],pr:[{pr:E()}],pb:[{pb:E()}],pl:[{pl:E()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":E()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":E()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,q,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,X,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,$]}],"font-family":[{font:[K,$,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,X,$]}],"line-clamp":[{"line-clamp":[D,"none",X,W]}],leading:[{leading:[s,...E()]}],"list-image":[{"list-image":["none",X,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[D,"from-font","auto",X,z]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[D,"auto",X,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},F,X,$],radial:["",X,$],conic:[F,X,$]},Q,Y]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[D,X,$]}],"outline-w":[{outline:["",D,q,z]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",d,J,H]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",c,J,H]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[D,z]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",h,J,H]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[D,X,$]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[D]}],"mask-image-linear-from-pos":[{"mask-linear-from":ec()}],"mask-image-linear-to-pos":[{"mask-linear-to":ec()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ec()}],"mask-image-t-to-pos":[{"mask-t-to":ec()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ec()}],"mask-image-r-to-pos":[{"mask-r-to":ec()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ec()}],"mask-image-b-to-pos":[{"mask-b-to":ec()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ec()}],"mask-image-l-to-pos":[{"mask-l-to":ec()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ec()}],"mask-image-x-to-pos":[{"mask-x-to":ec()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ec()}],"mask-image-y-to-pos":[{"mask-y-to":ec()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[X,$]}],"mask-image-radial-from-pos":[{"mask-radial-from":ec()}],"mask-image-radial-to-pos":[{"mask-radial-to":ec()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[D]}],"mask-image-conic-from-pos":[{"mask-conic-from":ec()}],"mask-image-conic-to-pos":[{"mask-conic-to":ec()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,$]}],filter:[{filter:["","none",X,$]}],blur:[{blur:eh()}],brightness:[{brightness:[D,X,$]}],contrast:[{contrast:[D,X,$]}],"drop-shadow":[{"drop-shadow":["","none",p,J,H]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",D,X,$]}],"hue-rotate":[{"hue-rotate":[D,X,$]}],invert:[{invert:["",D,X,$]}],saturate:[{saturate:[D,X,$]}],sepia:[{sepia:["",D,X,$]}],"backdrop-filter":[{"backdrop-filter":["","none",X,$]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[D,X,$]}],"backdrop-contrast":[{"backdrop-contrast":[D,X,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",D,X,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[D,X,$]}],"backdrop-invert":[{"backdrop-invert":["",D,X,$]}],"backdrop-opacity":[{"backdrop-opacity":[D,X,$]}],"backdrop-saturate":[{"backdrop-saturate":[D,X,$]}],"backdrop-sepia":[{"backdrop-sepia":["",D,X,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":E()}],"border-spacing-x":[{"border-spacing-x":E()}],"border-spacing-y":[{"border-spacing-y":E()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[D,"initial",X,$]}],ease:[{ease:["linear","initial",v,X,$]}],delay:[{delay:[D,X,$]}],animate:[{animate:["none",y,X,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,X,$]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[X,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,$]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[D,q,z,W]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},84027:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84818:(e,t,r)=>{r.d(t,{a:()=>s});var n=r(43210),i=r(52315);function s(){let e=(0,n.useRef)(!1);return(0,i.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},85778:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86044:(e,t,r)=>{r.d(t,{xQ:()=>s});var n=r(43210),i=r(21279);function s(e=!0){let t=(0,n.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:o,register:a}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,n.useCallback)(()=>e&&o&&o(l),[l,o,e]);return!r&&o?[!1,u]:[!0]}},88920:(e,t,r)=>{r.d(t,{N:()=>y});var n=r(60687),i=r(43210),s=r(12157),o=r(72789),a=r(15124),l=r(21279),u=r(18171),d=r(32582);class c extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t,anchorX:r,root:s}){let o=(0,i.useId)(),a=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(d.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:n,top:i,left:d,right:c}=l.current;if(t||!a.current||!e||!n)return;let h="left"===r?`left: ${d}`:`right: ${c}`;a.current.dataset.motionPopId=o;let p=document.createElement("style");u&&(p.nonce=u);let m=s??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            ${h}px !important;
            top: ${i}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[t]),(0,n.jsx)(c,{isPresent:t,childRef:a,sizeRef:l,children:i.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:s,custom:a,presenceAffectsLayout:u,mode:d,anchorX:c,root:p})=>{let f=(0,o.M)(m),g=(0,i.useId)(),v=!0,y=(0,i.useMemo)(()=>(v=!1,{id:g,initial:t,isPresent:r,custom:a,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;s&&s()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[r,f,s]);return u&&v&&(y={...y}),(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),i.useEffect(()=>{r||f.size||!s||s()},[r]),"popLayout"===d&&(e=(0,n.jsx)(h,{isPresent:r,anchorX:c,root:p,children:e})),(0,n.jsx)(l.t.Provider,{value:y,children:e})};function m(){return new Map}var f=r(86044);let g=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:d="sync",propagate:c=!1,anchorX:h="left",root:m})=>{let[y,b]=(0,f.xQ)(c),x=(0,i.useMemo)(()=>v(e),[e]),w=c&&!y?[]:x.map(g),k=(0,i.useRef)(!0),P=(0,i.useRef)(x),T=(0,o.M)(()=>new Map),[E,S]=(0,i.useState)(x),[A,M]=(0,i.useState)(x);(0,a.E)(()=>{k.current=!1,P.current=x;for(let e=0;e<A.length;e++){let t=g(A[e]);w.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[A,w.length,w.join("-")]);let C=[];if(x!==E){let e=[...x];for(let t=0;t<A.length;t++){let r=A[t],n=g(r);w.includes(n)||(e.splice(t,0,r),C.push(r))}return"wait"===d&&C.length&&(e=C),M(v(e)),S(x),null}let{forceRender:D}=(0,i.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:A.map(e=>{let i=g(e),s=(!c||!!y)&&(x===A||w.includes(i));return(0,n.jsx)(p,{isPresent:s,initial:(!k.current||!!r)&&void 0,custom:t,presenceAffectsLayout:u,mode:d,root:m,onExitComplete:s?void 0:()=>{if(!T.has(i))return;T.set(i,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(D?.(),M(P.current),c&&b?.(),l&&l())},anchorX:h,children:e},i)})})}},96474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99923:(e,t,r)=>{r.d(t,{_:()=>a});var n=Object.defineProperty,i=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,s=(e,t,r)=>(i(e,"symbol"!=typeof t?t+"":t,r),r);class o{constructor(){s(this,"current",this.detect()),s(this,"handoffState","pending"),s(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let a=new o}};