1:"$Sreact.fragment"
2:I[87555,[],""]
3:I[31295,[],""]
4:I[13049,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"default"]
5:I[40545,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"default"]
6:I[17703,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"Card"]
7:I[17703,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"CardHeader"]
8:I[17703,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"CardTitle"]
9:I[17703,["271","static/chunks/271-f20f12eebf350dd7.js","874","static/chunks/874-ecf0a55c01b1a08d.js","556","static/chunks/556-ea4a73854543e5f9.js","49","static/chunks/49-fb81c9ee503c4b2f.js","662","static/chunks/app/settings/page-c1178b4b1550641e.js"],"CardContent"]
a:I[59665,[],"OutletBoundary"]
d:I[74911,[],"AsyncMetadataOutlet"]
f:I[59665,[],"ViewportBoundary"]
11:I[59665,[],"MetadataBoundary"]
13:I[26614,[],""]
:HL["/_next/static/css/0e3da69ba8c5faa1.css","style"]
0:{"P":null,"b":"qYSbtxzbLzoL4HY6wvNRl","p":"","c":["","settings"],"i":false,"f":[[["",{"children":["settings",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0e3da69ba8c5faa1.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["settings",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L4",null,{"children":["$","div",null,{"className":"space-y-8","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent","children":"Settings"}],["$","p",null,{"className":"text-gray-600 mt-2","children":"Manage your account and application preferences"}]]}],["$","$L5",null,{}],["$","$L6",null,{"children":[["$","$L7",null,{"children":["$","$L8",null,{"children":"Account Settings"}]}],["$","$L9",null,{"children":["$","p",null,{"className":"text-gray-600","children":"Additional account settings will be implemented in future updates."}]}]]}],["$","$L6",null,{"children":[["$","$L7",null,{"children":["$","$L8",null,{"children":"Application Info"}]}],["$","$L9",null,{"children":["$","div",null,{"className":"space-y-2","children":[["$","p",null,{"children":[["$","strong",null,{"children":"Version:"}]," 2.0.0"]}],["$","p",null,{"children":[["$","strong",null,{"children":"Built with:"}]," Next.js, Supabase, TypeScript, Tailwind CSS, Framer Motion"]}],["$","p",null,{"children":["$","strong",null,{"children":"Features:"}]}],["$","ul",null,{"className":"list-disc list-inside ml-4 space-y-1","children":[["$","li",null,{"children":"Transaction Management"}],["$","li",null,{"children":"Asset Management"}],["$","li",null,{"children":"Liability Tracking"}],["$","li",null,{"children":"Receivables Management"}],["$","li",null,{"children":"Category Management"}],["$","li",null,{"children":"Net Worth Calculation"}],["$","li",null,{"children":"Multi-Currency Support (LKR/USD)"}],["$","li",null,{"children":"Search and Filtering"}],["$","li",null,{"children":"Premium UI with Animations"}],["$","li",null,{"children":"Responsive Design"}],["$","li",null,{"children":"Secure Authentication"}]]}]]}]}]]}]]}]}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Wwhf74KhsvCHoDK00MqZxv",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],["$","$L11",null,{"children":"$L12"}]]}],false]],"m":"$undefined","G":["$13","$undefined"],"s":false,"S":true}
14:"$Sreact.suspense"
15:I[74911,[],"AsyncMetadata"]
12:["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":["$","$L15",null,{"promise":"$@16"}]}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
e:{"metadata":[["$","title","0",{"children":"Create Next App"}],["$","meta","1",{"name":"description","content":"Generated by create next app"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
16:{"metadata":"$e:metadata","error":null,"digest":"$undefined"}
