(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{19895:(e,t,s)=>{"use strict";s.d(t,{default:()=>R});var a=s(95155),l=s(12115),r=s(46308),i=s(16785),n=s(84616),c=s(72713),d=s(33109),o=s(52643);async function x(){let e=(0,o.U)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)throw Error("User not authenticated");let{data:a}=await e.from("assets").select("current_value, asset_type, currency").eq("user_id",t.id),{data:l}=await e.from("liabilities").select("current_balance, liability_type, status, due_date, currency").eq("user_id",t.id),{data:r}=await e.from("receivables").select("current_balance, status, due_date, currency").eq("user_id",t.id),{data:i}=await e.from("transactions").select("\n      *,\n      categories (\n        name,\n        color\n      )\n    ").eq("user_id",t.id).order("transaction_date",{ascending:!1}).limit(5),n=(null==a?void 0:a.reduce((e,t)=>e+t.current_value,0))||0,c=(null==l?void 0:l.filter(e=>"active"===e.status).reduce((e,t)=>e+t.current_balance,0))||0,d=(null==r?void 0:r.filter(e=>"active"===e.status).reduce((e,t)=>e+t.current_balance,0))||0,x=(null==a?void 0:a.reduce((e,t)=>{let s=e.find(e=>e.type===t.asset_type);return s?(s.value+=t.current_value,s.count+=1):e.push({type:t.asset_type,value:t.current_value,count:1}),e},[]))||[],u=(null==l?void 0:l.filter(e=>"active"===e.status).reduce((e,t)=>{let s=e.find(e=>e.type===t.liability_type);return s?(s.value+=t.current_balance,s.count+=1):e.push({type:t.liability_type,value:t.current_balance,count:1}),e},[]))||[],m=new Date;return{totalAssets:n,totalLiabilities:c,totalReceivables:d,netWorth:n+d-c,assetsByType:x,liabilitiesByType:u,recentTransactions:i||[],overdueItems:{liabilities:(null==l?void 0:l.filter(e=>"active"===e.status&&e.due_date&&new Date(e.due_date)<m).length)||0,receivables:(null==r?void 0:r.filter(e=>"active"===e.status&&e.due_date&&new Date(e.due_date)<m).length)||0}}}var u=s(13741),m=s(17703),h=s(68500),v=s(59434);function g(e){let{totalAssets:t,totalLiabilities:s,totalReceivables:l,netWorth:r}=e,i=r>=0;return(0,a.jsxs)(m.Card,{className:"col-span-2",children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsxs)(m.CardTitle,{className:"flex items-center",children:["Net Worth",i?(0,a.jsx)(d.A,{className:"ml-2 h-5 w-5 text-green-500"}):(0,a.jsx)(h.A,{className:"ml-2 h-5 w-5 text-red-500"})]})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-3xl font-bold",children:(0,a.jsx)("span",{className:i?"text-green-600":"text-red-600",children:(0,v.vv)(r)})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-500",children:"Assets"}),(0,a.jsx)("div",{className:"font-semibold text-green-600",children:(0,v.vv)(t)})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-500",children:"Receivables"}),(0,a.jsx)("div",{className:"font-semibold text-blue-600",children:(0,v.vv)(l)})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-500",children:"Liabilities"}),(0,a.jsx)("div",{className:"font-semibold text-red-600",children:(0,v.vv)(s)})]})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Net Worth = Assets + Receivables - Liabilities"})]})})]})}var b=s(81586),j=s(17580),p=s(1243);function N(e){let{totalAssets:t,totalLiabilities:s,totalReceivables:l,overdueItems:r}=e,i=[{title:"Total Assets",value:(0,v.vv)(t),icon:d.A,color:"text-green-600",bgColor:"bg-green-50"},{title:"Total Liabilities",value:(0,v.vv)(s),icon:b.A,color:"text-red-600",bgColor:"bg-red-50"},{title:"Total Receivables",value:(0,v.vv)(l),icon:j.A,color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Overdue Items",value:"".concat(r.liabilities+r.receivables),icon:p.A,color:r.liabilities+r.receivables>0?"text-red-600":"text-gray-600",bgColor:r.liabilities+r.receivables>0?"bg-red-50":"bg-gray-50"}];return(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:i.map((e,t)=>(0,a.jsx)(m.Card,{children:(0,a.jsx)(m.CardContent,{className:"p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-5 w-5 lg:h-6 lg:w-6 ".concat(e.color)})}),(0,a.jsxs)("div",{className:"ml-3 lg:ml-4 min-w-0 flex-1",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-600 truncate",children:e.title}),(0,a.jsx)("p",{className:"text-lg lg:text-2xl font-bold ".concat(e.color," truncate"),children:e.value})]})]})})},t))})}var f=s(14474),y=s(3786),w=s(7611),C=s(54811),_=s(24021);let A=["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4"];function T(e){let{data:t}=e,s=t.map(e=>({name:e.type.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),value:e.value,count:e.count}));return 0===t.length?(0,a.jsxs)(m.Card,{children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsx)(m.CardTitle,{children:"Assets by Type"})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No assets to display"})})]}):(0,a.jsxs)(m.Card,{children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsx)(m.CardTitle,{children:"Assets by Type"})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(y.r,{children:[(0,a.jsx)(w.F,{data:s,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:s}=e;return"".concat(t," ").concat((100*s).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:s.map((e,t)=>(0,a.jsx)(C.f,{fill:A[t%A.length]},"cell-".concat(t)))}),(0,a.jsx)(_.m,{content:(0,a.jsx)(e=>{let{active:t,payload:s}=e;if(t&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded shadow",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-blue-600",children:["Value: ",(0,v.vv)(e.value)]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Count: ",e.count]})]})}return null},{})})]})})})]})}function R(){let[e,t]=(0,l.useState)(null),[s,o]=(0,l.useState)(!0);(0,l.useEffect)(()=>{h()},[]);let h=async()=>{try{let e=await x();t(e)}catch(e){console.error("Error loading dashboard data:",e)}finally{o(!1)}};return s?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):e?(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Overview of your financial portfolio"})]}),(0,a.jsx)(N,{totalAssets:e.totalAssets,totalLiabilities:e.totalLiabilities,totalReceivables:e.totalReceivables,overdueItems:e.overdueItems}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(g,{totalAssets:e.totalAssets,totalLiabilities:e.totalLiabilities,totalReceivables:e.totalReceivables,netWorth:e.netWorth}),(0,a.jsx)(T,{data:e.assetsByType})]}),(0,a.jsxs)(m.Card,{children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsx)(m.CardTitle,{children:"Quick Actions"})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4",children:[(0,a.jsxs)(u.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-blue-50 hover:border-blue-300 transition-all duration-200",onClick:()=>window.location.href="/transactions",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-blue-600"}),(0,a.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Add Transaction"})]}),(0,a.jsxs)(u.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-green-50 hover:border-green-300 transition-all duration-200",onClick:()=>window.location.href="/budget",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-green-600"}),(0,a.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Budget Planner"})]}),(0,a.jsxs)(u.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-purple-50 hover:border-purple-300 transition-all duration-200",onClick:()=>window.location.href="/assets",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-purple-600"}),(0,a.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Add Asset"})]}),(0,a.jsxs)(u.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-orange-50 hover:border-orange-300 transition-all duration-200",onClick:()=>window.location.href="/reports",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-orange-600"}),(0,a.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"View Reports"})]})]})})]}),e.recentTransactions&&e.recentTransactions.length>0&&(0,a.jsxs)(m.Card,{children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsxs)(m.CardTitle,{className:"flex items-center justify-between",children:["Recent Transactions",(0,a.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>window.location.href="/transactions",children:"View All"})]})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.recentTransactions.slice(0,5).map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 rounded-full mr-3 ".concat("income"===e.type?"bg-green-100 text-green-600":"expense"===e.type?"bg-red-100 text-red-600":"bg-blue-100 text-blue-600"),children:"income"===e.type?(0,a.jsx)(d.A,{className:"h-4 w-4"}):"expense"===e.type?(0,a.jsx)(d.A,{className:"h-4 w-4 rotate-180"}):(0,a.jsx)(r.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.description||"No description"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:(null==(t=e.categories)?void 0:t.name)||"Uncategorized"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-semibold ".concat("income"===e.type?"text-green-600":"text-red-600"),children:["income"===e.type?"+":"-",new Intl.NumberFormat("en-US",{style:"currency",currency:e.currency||"LKR",currencyDisplay:"symbol"}).format(e.amount).replace("LKR","Rs.")]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.transaction_date).toLocaleDateString()})]})]},e.id)})})})]}),e.overdueItems.liabilities>0||e.overdueItems.receivables>0?(0,a.jsxs)(m.Card,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(m.CardHeader,{children:(0,a.jsx)(m.CardTitle,{className:"text-red-800",children:"⚠️ Attention Required"})}),(0,a.jsx)(m.CardContent,{children:(0,a.jsxs)("div",{className:"text-red-700 space-y-1",children:[e.overdueItems.liabilities>0&&(0,a.jsxs)("p",{children:["• ",e.overdueItems.liabilities," overdue liability(ies) need attention"]}),e.overdueItems.receivables>0&&(0,a.jsxs)("p",{children:["• ",e.overdueItems.receivables," overdue receivable(s) need follow-up"]})]})})]}):null]}):(0,a.jsx)("div",{className:"text-center py-8 text-red-600",children:"Error loading dashboard data. Please try again."})}},80577:(e,t,s)=>{Promise.resolve().then(s.bind(s,19895)),Promise.resolve().then(s.bind(s,13049))}},e=>{var t=t=>e(e.s=t);e.O(0,[271,874,556,196,49,441,684,358],()=>t(80577)),_N_E=e.O()}]);