{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4e1c08c4._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_dd15cafc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMAtdUbsubgDP5IsA8bFq2Vl4TCbSe812PztFsz/xqw=", "__NEXT_PREVIEW_MODE_ID": "dcd0d3e3b51239787b7ac91599e4a9f6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "00f4d96df12e68390a20e3fc9e03b085e2e1c8ce5db40c955b132cd6c30ae877", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dc0f784394daf1e28cc9baba2d2bafb190b96e9b0c7c78df4bbcd41edba656a8"}}}, "instrumentation": null, "functions": {}}