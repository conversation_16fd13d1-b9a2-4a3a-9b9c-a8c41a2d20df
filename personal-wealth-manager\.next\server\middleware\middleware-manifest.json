{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4e1c08c4._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_dd15cafc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMAtdUbsubgDP5IsA8bFq2Vl4TCbSe812PztFsz/xqw=", "__NEXT_PREVIEW_MODE_ID": "3c6f18258a86106156f6dd5852383545", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e28ec4e26bae1dd63b61b4b332b98b3b0a6c92f8471921e425eb8618c101a6f2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "79b93e4a8525243963fdc5226c1b3bc9cff7ef6fb9f79ca193efbd61290cee12"}}}, "instrumentation": null, "functions": {}}