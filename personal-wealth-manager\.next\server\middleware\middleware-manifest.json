{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4e1c08c4._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_dd15cafc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMAtdUbsubgDP5IsA8bFq2Vl4TCbSe812PztFsz/xqw=", "__NEXT_PREVIEW_MODE_ID": "b3fa5b39d1da6e2360420626f31caafc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "255036aabb9d0c29bc15118056f9017035c5138aee8abe5526b538ab7e3df32a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fd58008c20e845345a289e4cd4caca0051416dbb938228c67d4d84ecb398ad3d"}}}, "instrumentation": null, "functions": {}}