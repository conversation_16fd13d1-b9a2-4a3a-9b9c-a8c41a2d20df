{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4e1c08c4._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_dd15cafc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMAtdUbsubgDP5IsA8bFq2Vl4TCbSe812PztFsz/xqw=", "__NEXT_PREVIEW_MODE_ID": "692421179693b2471dbec03156e8b03d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "460ad18188c8c8aadceb191b1b53b9e37e3473b1c014eb72edafa214755e8ccc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc2ff91c31ec544923cdb4ca2ba754949102f707760dfaee6732f8d6dc0000d1"}}}, "instrumentation": null, "functions": {}}