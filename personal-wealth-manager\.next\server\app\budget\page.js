(()=>{var e={};e.id=942,e.ids=[942],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6059:(e,r,t)=>{Promise.resolve().then(t.bind(t,90408)),Promise.resolve().then(t.bind(t,63087))},10099:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let o={children:["",{children:["budget",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42226)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\budget\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\budget\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/budget/page",pathname:"/budget",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17901:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\budget\\\\BudgetPlanner.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\budget\\BudgetPlanner.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42226:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(37413),a=t(46655),n=t(17901);function l(){return(0,s.jsx)(a.default,{children:(0,s.jsx)(n.default,{})})}},45811:(e,r,t)=>{Promise.resolve().then(t.bind(t,17901)),Promise.resolve().then(t.bind(t,46655))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90408:(e,r,t)=>{"use strict";t.d(r,{default:()=>v});var s=t(60687),a=t(43210),n=t(26001),l=t(62688);let i=(0,l.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),d=(0,l.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var o=t(96474),c=t(28947),u=t(25541),x=t(2643),m=t(28749),h=t(51907),p=t(43949),g=t(70695),j=t(4780);function v(){let[e,r]=(0,a.useState)([]),[t,l]=(0,a.useState)([]),[v,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!0),[N,_]=(0,a.useState)({category:"",budgeted_amount:0,period:"monthly",currency:"LKR"}),[k,P]=(0,a.useState)({name:"",target_amount:0,target_date:"",currency:"LKR"}),q=e=>{let r=e.spent_amount/e.budgeted_amount*100;return r>100?{status:"over",color:"text-red-600 bg-red-50",icon:i}:r>80?{status:"warning",color:"text-yellow-600 bg-yellow-50",icon:i}:{status:"good",color:"text-green-600 bg-green-50",icon:d}},A=e=>e.current_amount/e.target_amount*100;return w?(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded w-1/4"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6",children:[(0,s.jsx)("div",{className:"h-64 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-64 bg-gray-300 rounded"})]})]})}):(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Budget Planner"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Track your spending and achieve your financial goals"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsxs)(x.$,{onClick:()=>y(!0),variant:"outline",className:"w-full sm:w-auto",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Budget"]}),(0,s.jsxs)(x.$,{onClick:()=>b(!0),variant:"gradient",className:"w-full sm:w-auto",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Goal"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[(0,s.jsxs)(m.Card,{children:[(0,s.jsx)(m.CardHeader,{children:(0,s.jsx)(m.CardTitle,{children:"Monthly Budgets"})}),(0,s.jsx)(m.CardContent,{children:(0,s.jsx)("div",{className:"space-y-4",children:e.map(e=>{let r=q(e),t=Math.min(e.spent_amount/e.budgeted_amount*100,100),a=r.icon;return(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"p-4 border rounded-xl hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"font-semibold",children:e.category}),(0,s.jsxs)("div",{className:`flex items-center px-2 py-1 rounded-full text-xs ${r.color}`,children:[(0,s.jsx)(a,{className:"h-3 w-3 mr-1"}),r.status]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsxs)("span",{children:["Spent: ",(0,j.vv)(e.spent_amount,e.currency)]}),(0,s.jsxs)("span",{children:["Budget: ",(0,j.vv)(e.budgeted_amount,e.currency)]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${t>100?"bg-red-500":t>80?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(t,100)}%`}})}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[t.toFixed(1),"% used"]})]})]},e.id)})})})]}),(0,s.jsxs)(m.Card,{children:[(0,s.jsx)(m.CardHeader,{children:(0,s.jsx)(m.CardTitle,{children:"Financial Goals"})}),(0,s.jsx)(m.CardContent,{children:(0,s.jsx)("div",{className:"space-y-4",children:t.map(e=>{let r=A(e),t=Math.ceil((new Date(e.target_date).getTime()-new Date().getTime())/864e5);return(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"p-4 border rounded-xl hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"font-semibold",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center text-blue-600 text-xs",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),t>0?`${t} days left`:"Overdue"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsxs)("span",{children:["Saved: ",(0,j.vv)(e.current_amount,e.currency)]}),(0,s.jsxs)("span",{children:["Target: ",(0,j.vv)(e.target_amount,e.currency)]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 bg-blue-500 rounded-full transition-all duration-300",style:{width:`${Math.min(r,100)}%`}})}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[r.toFixed(1),"% complete"]})]})]},e.id)})})})]})]}),v&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)(m.Card,{className:"w-full max-w-md",children:[(0,s.jsx)(m.CardHeader,{children:(0,s.jsx)(m.CardTitle,{children:"Add New Budget"})}),(0,s.jsx)(m.CardContent,{children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),y(!1),_({category:"",budgeted_amount:0,period:"monthly",currency:"LKR"})},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"category",children:"Category"}),(0,s.jsx)(h.p,{id:"category",value:N.category,onChange:e=>_(r=>({...r,category:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"amount",children:"Budget Amount"}),(0,s.jsx)(h.p,{id:"amount",type:"number",step:"0.01",value:N.budgeted_amount,onChange:e=>_(r=>({...r,budgeted_amount:parseFloat(e.target.value)||0})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"period",children:"Period"}),(0,s.jsxs)(g.l,{id:"period",value:N.period,onChange:e=>_(r=>({...r,period:e.target.value})),children:[(0,s.jsx)("option",{value:"weekly",children:"Weekly"}),(0,s.jsx)("option",{value:"monthly",children:"Monthly"}),(0,s.jsx)("option",{value:"yearly",children:"Yearly"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"currency",children:"Currency"}),(0,s.jsxs)(g.l,{id:"currency",value:N.currency,onChange:e=>_(r=>({...r,currency:e.target.value})),children:[(0,s.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,s.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>y(!1),children:"Cancel"}),(0,s.jsx)(x.$,{type:"submit",children:"Add Budget"})]})]})})]})}),f&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)(m.Card,{className:"w-full max-w-md",children:[(0,s.jsx)(m.CardHeader,{children:(0,s.jsx)(m.CardTitle,{children:"Add Financial Goal"})}),(0,s.jsx)(m.CardContent,{children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),b(!1),P({name:"",target_amount:0,target_date:"",currency:"LKR"})},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"goalName",children:"Goal Name"}),(0,s.jsx)(h.p,{id:"goalName",value:k.name,onChange:e=>P(r=>({...r,name:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"targetAmount",children:"Target Amount"}),(0,s.jsx)(h.p,{id:"targetAmount",type:"number",step:"0.01",value:k.target_amount,onChange:e=>P(r=>({...r,target_amount:parseFloat(e.target.value)||0})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"targetDate",children:"Target Date"}),(0,s.jsx)(h.p,{id:"targetDate",type:"date",value:k.target_date,onChange:e=>P(r=>({...r,target_date:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"goalCurrency",children:"Currency"}),(0,s.jsxs)(g.l,{id:"goalCurrency",value:k.currency,onChange:e=>P(r=>({...r,currency:e.target.value})),children:[(0,s.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,s.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>b(!1),children:"Cancel"}),(0,s.jsx)(x.$,{type:"submit",children:"Add Goal"})]})]})})]})})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,145,79,814,435,235],()=>t(10099));module.exports=s})();