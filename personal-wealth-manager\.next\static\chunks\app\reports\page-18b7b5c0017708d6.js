(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{53010:(e,t,s)=>{Promise.resolve().then(s.bind(s,13049)),Promise.resolve().then(s.bind(s,79198))},68500:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79198:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a=s(95155),r=s(12115),l=s(76408);let n=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var i=s(33109),c=s(68500),d=s(72713),o=s(69074),m=s(13741),x=s(17703),h=s(30353),p=s(59434),g=s(72901),u=s(81053),j=s(47839),N=s(76443),v=s(19816);function y(){let[e,t]=(0,r.useState)(!0),[s,y]=(0,r.useState)("monthly"),[f,b]=(0,r.useState)([]),[w,C]=(0,r.useState)([]),[k,I]=(0,r.useState)([]),[S,D]=(0,r.useState)({start:new Date(new Date().getFullYear(),new Date().getMonth(),1).toISOString().split("T")[0],end:new Date().toISOString().split("T")[0]});(0,r.useEffect)(()=>{T()},[s,S]);let T=async()=>{t(!0);try{let[e,t,s,a,r]=await Promise.all([(0,g.I0)(),(0,u.Y)(),(0,j.mz)(),(0,N.A0)(),(0,v.bW)()]),l=e.data||[],n=t.data||[],i=s.data||[],c=a.data||[],d=(r.data||[]).reduce((e,t)=>(e[t.id]=t.name,e),{}),o=new Date(S.start),m=new Date(S.end);m.setHours(23,59,59,999);let x=l.filter(e=>{let t=new Date(e.transaction_date);return t>=o&&t<=m}),h=x.filter(e=>"income"===e.type).reduce((e,t)=>e+t.amount,0),y=x.filter(e=>"expense"===e.type).reduce((e,t)=>e+t.amount,0),f=n.reduce((e,t)=>e+t.current_value,0),w=i.reduce((e,t)=>e+t.current_balance,0),k=c.reduce((e,t)=>e+t.current_balance,0),D={period:"".concat((0,p.Yq)(S.start)," - ").concat((0,p.Yq)(S.end)),income:h,expenses:y,netIncome:h-y,assets:f,liabilities:w,netWorth:f+k-w};b([D]);let T=x.filter(e=>"income"===e.type).reduce((e,t)=>{let s=t.category_id?d[t.category_id]||"Unknown Category":"Uncategorized";return e[s]=(e[s]||0)+t.amount,e},{}),A=x.filter(e=>"expense"===e.type).reduce((e,t)=>{let s=t.category_id?d[t.category_id]||"Unknown Category":"Uncategorized";return e[s]=(e[s]||0)+t.amount,e},{}),M=["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4","#84CC16","#F97316"],_=Object.entries(T).map((e,t)=>{let[s,a]=e;return{category:s,amount:a,percentage:h>0?Math.round(a/h*100):0,color:M[t%M.length]}}),F=Object.entries(A).map((e,t)=>{let[s,a]=e;return{category:s,amount:a,percentage:y>0?Math.round(a/y*100):0,color:M[t%M.length]}});C(_),I(F)}catch(e){console.error("Error loading report data:",e)}finally{t(!1)}},A=()=>f[f.length-1]||{period:"",income:0,expenses:0,netIncome:0,assets:0,liabilities:0,netWorth:0},M=(e,t)=>0===t?0:(e-t)/t*100;if(e)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-1/4"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"h-32 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-32 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-32 bg-gray-300 rounded"})]})]})});let _=A(),F=f[f.length-2]||A();return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Financial Reports"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Analyze your financial performance and trends"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 items-start sm:items-center",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,a.jsx)("label",{className:"text-sm font-medium whitespace-nowrap",children:"From:"}),(0,a.jsx)("input",{type:"date",value:S.start,onChange:e=>D(t=>({...t,start:e.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,a.jsx)("label",{className:"text-sm font-medium whitespace-nowrap",children:"To:"}),(0,a.jsx)("input",{type:"date",value:S.end,onChange:e=>D(t=>({...t,end:e.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,a.jsxs)(h.l,{value:s,onChange:e=>{y(e.target.value);let t=new Date;if("weekly"===e.target.value){let e=new Date(t.setDate(t.getDate()-t.getDay())),s=new Date(t.setDate(e.getDate()+6));D({start:e.toISOString().split("T")[0],end:s.toISOString().split("T")[0]})}else"monthly"===e.target.value&&D({start:new Date(t.getFullYear(),t.getMonth(),1).toISOString().split("T")[0],end:new Date(t.getFullYear(),t.getMonth()+1,0).toISOString().split("T")[0]})},className:"w-full sm:w-auto",children:[(0,a.jsx)("option",{value:"custom",children:"Custom Range"}),(0,a.jsx)("option",{value:"weekly",children:"This Week"}),(0,a.jsx)("option",{value:"monthly",children:"This Month"}),(0,a.jsx)("option",{value:"quarterly",children:"This Quarter"}),(0,a.jsx)("option",{value:"yearly",children:"This Year"})]}),(0,a.jsxs)(m.$,{onClick:()=>{console.log("Exporting report...")},variant:"outline",className:"w-full sm:w-auto",children:[(0,a.jsx)(n,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6",children:[(0,a.jsx)(x.Card,{className:"bg-gradient-to-br from-green-50 to-emerald-100 border-green-200",children:(0,a.jsx)(x.CardContent,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Net Income"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-700",children:(0,p.vv)(_.netIncome,"LKR")}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[M(_.netIncome,F.netIncome)>=0?(0,a.jsx)(i.A,{className:"h-4 w-4 text-green-600 mr-1"}):(0,a.jsx)(c.A,{className:"h-4 w-4 text-red-600 mr-1"}),(0,a.jsxs)("span",{className:"text-sm ".concat(M(_.netIncome,F.netIncome)>=0?"text-green-600":"text-red-600"),children:[Math.abs(M(_.netIncome,F.netIncome)).toFixed(1),"%"]})]})]}),(0,a.jsx)("div",{className:"p-3 bg-green-200 rounded-xl",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-green-700"})})]})})}),(0,a.jsx)(x.Card,{className:"bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200",children:(0,a.jsx)(x.CardContent,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Net Worth"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-700",children:(0,p.vv)(_.netWorth,"LKR")}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[M(_.netWorth,F.netWorth)>=0?(0,a.jsx)(i.A,{className:"h-4 w-4 text-blue-600 mr-1"}):(0,a.jsx)(c.A,{className:"h-4 w-4 text-red-600 mr-1"}),(0,a.jsxs)("span",{className:"text-sm ".concat(M(_.netWorth,F.netWorth)>=0?"text-blue-600":"text-red-600"),children:[Math.abs(M(_.netWorth,F.netWorth)).toFixed(1),"%"]})]})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-200 rounded-xl",children:(0,a.jsx)(i.A,{className:"h-6 w-6 text-blue-700"})})]})})}),(0,a.jsx)(x.Card,{className:"bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200",children:(0,a.jsx)(x.CardContent,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-purple-600 text-sm font-medium",children:"Savings Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-purple-700",children:[_.income>0?(_.netIncome/_.income*100).toFixed(1):0,"%"]}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-purple-600 mr-1"}),(0,a.jsx)("span",{className:"text-sm text-purple-600",children:"This period"})]})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-200 rounded-xl",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-purple-700"})})]})})})]}),(0,a.jsxs)(x.Card,{children:[(0,a.jsx)(x.CardHeader,{children:(0,a.jsx)(x.CardTitle,{children:"Income vs Expenses Trend"})}),(0,a.jsx)(x.CardContent,{children:(0,a.jsx)("div",{className:"space-y-4",children:f.map((e,t)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center justify-between p-4 border rounded-xl",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold",children:e.period}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Income: ",(0,p.vv)(e.income,"LKR")]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["Expenses: ",(0,p.vv)(e.expenses,"LKR")]})]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold text-lg",children:(0,p.vv)(e.netIncome,"LKR")}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Net Income"})]})]},e.period))})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[(0,a.jsxs)(x.Card,{children:[(0,a.jsx)(x.CardHeader,{children:(0,a.jsx)(x.CardTitle,{children:"Income Breakdown"})}),(0,a.jsx)(x.CardContent,{children:(0,a.jsx)("div",{className:"space-y-4",children:w.map((e,t)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"font-medium",children:e.category})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold",children:(0,p.vv)(e.amount,"LKR")}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.percentage,"%"]})]})]},e.category))})})]}),(0,a.jsxs)(x.Card,{children:[(0,a.jsx)(x.CardHeader,{children:(0,a.jsx)(x.CardTitle,{children:"Expense Breakdown"})}),(0,a.jsx)(x.CardContent,{children:(0,a.jsx)("div",{className:"space-y-4",children:k.map((e,t)=>(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"font-medium",children:e.category})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-semibold",children:(0,p.vv)(e.amount,"LKR")}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.percentage,"%"]})]})]},e.category))})})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[271,874,556,49,441,684,358],()=>t(53010)),_N_E=e.O()}]);