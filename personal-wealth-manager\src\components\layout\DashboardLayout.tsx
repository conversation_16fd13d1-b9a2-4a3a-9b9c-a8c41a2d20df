import Sidebar from './Sidebar'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import GradientBackground from '@/components/ui/GradientBackground'
import QuickActionButton from '@/components/ui/QuickActionButton'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <ProtectedRoute>
      <GradientBackground variant="dashboard">
        <div className="flex h-screen">
          <Sidebar />
          <main className="flex-1 overflow-auto">
            <div className="p-8">
              {children}
            </div>
          </main>
          <QuickActionButton />
        </div>
      </GradientBackground>
    </ProtectedRoute>
  )
}
