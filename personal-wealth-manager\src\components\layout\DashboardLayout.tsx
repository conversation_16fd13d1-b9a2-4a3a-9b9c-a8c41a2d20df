'use client'

import { useState } from 'react'
import Sidebar from './Sidebar'
import MobileSidebar from './MobileSidebar'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import GradientBackground from '@/components/ui/GradientBackground'
import QuickActionButton from '@/components/ui/QuickActionButton'
import { Menu } from 'lucide-react'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <ProtectedRoute>
      <GradientBackground variant="dashboard">
        <div className="flex h-screen">
          {/* Desktop Sidebar */}
          <div className="hidden lg:block">
            <Sidebar />
          </div>

          {/* Mobile Sidebar */}
          <MobileSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            {/* Mobile Header */}
            <div className="lg:hidden bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 py-3 flex items-center justify-between">
              <button
                onClick={() => setSidebarOpen(true)}
                className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              >
                <Menu className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">W</span>
                </div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Wealth Manager
                </h1>
              </div>
            </div>

            {/* Page Content */}
            <div className="p-4 sm:p-6 lg:p-8">
              {children}
            </div>
          </main>

          {/* Quick Action Button - Hidden on mobile */}
          <div className="hidden sm:block">
            <QuickActionButton />
          </div>
        </div>
      </GradientBackground>
    </ProtectedRoute>
  )
}
