[{"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\assets\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\budget\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\categories\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\dashboard\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\liabilities\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\login\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\receivables\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\reports\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\settings\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\signup\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\transactions\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\assets\\AssetForm.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\assets\\AssetList.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\auth\\ProtectedRoute.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\budget\\BudgetPlanner.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\categories\\CategoryForm.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\categories\\CategoryList.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\AssetChart.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\Dashboard.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\NetWorthCard.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\QuickStats.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\DashboardLayout.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\MobileSidebar.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\Sidebar.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\liabilities\\LiabilityForm.tsx": "27", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\liabilities\\LiabilityList.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\receivables\\ReceivableForm.tsx": "29", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\receivables\\ReceivableList.tsx": "30", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\reports\\FinancialReports.tsx": "31", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\settings\\CurrencySettings.tsx": "32", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\transactions\\QuickTransactionForm.tsx": "33", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\transactions\\TransactionList.tsx": "34", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Button.tsx": "35", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx": "36", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\FilterDropdown.tsx": "37", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\GradientBackground.tsx": "38", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Input.tsx": "39", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Label.tsx": "40", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\QuickActionButton.tsx": "41", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\SearchInput.tsx": "42", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Select.tsx": "43", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Table.tsx": "44", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Textarea.tsx": "45", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\assets.ts": "46", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\categories.ts": "47", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\dashboard.ts": "48", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\liabilities.ts": "49", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\profile.ts": "50", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\receivables.ts": "51", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\transactions.ts": "52", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\auth.ts": "53", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\currency.ts": "54", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\client.ts": "55", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\middleware.ts": "56", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\server.ts": "57", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\types\\database.ts": "58", "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\utils.ts": "59"}, {"size": 242, "mtime": 1752081231883, "results": "60", "hashOfConfig": "61"}, {"size": 254, "mtime": 1752084360608, "results": "62", "hashOfConfig": "61"}, {"size": 259, "mtime": 1752083023231, "results": "63", "hashOfConfig": "61"}, {"size": 248, "mtime": 1752081663529, "results": "64", "hashOfConfig": "61"}, {"size": 689, "mtime": 1752080490648, "results": "65", "hashOfConfig": "61"}, {"size": 264, "mtime": 1752081462817, "results": "66", "hashOfConfig": "61"}, {"size": 4763, "mtime": 1752082891424, "results": "67", "hashOfConfig": "61"}, {"size": 711, "mtime": 1752082182803, "results": "68", "hashOfConfig": "61"}, {"size": 267, "mtime": 1752081561103, "results": "69", "hashOfConfig": "61"}, {"size": 265, "mtime": 1752084420473, "results": "70", "hashOfConfig": "61"}, {"size": 2027, "mtime": 1752083466147, "results": "71", "hashOfConfig": "61"}, {"size": 5203, "mtime": 1752080976233, "results": "72", "hashOfConfig": "61"}, {"size": 272, "mtime": 1752083262125, "results": "73", "hashOfConfig": "61"}, {"size": 12197, "mtime": 1752090082344, "results": "74", "hashOfConfig": "61"}, {"size": 11189, "mtime": 1752090167987, "results": "75", "hashOfConfig": "61"}, {"size": 1391, "mtime": 1752080987174, "results": "76", "hashOfConfig": "61"}, {"size": 13878, "mtime": 1752089908634, "results": "77", "hashOfConfig": "61"}, {"size": 5256, "mtime": 1752090205919, "results": "78", "hashOfConfig": "61"}, {"size": 7338, "mtime": 1752083010710, "results": "79", "hashOfConfig": "61"}, {"size": 2270, "mtime": 1752081624907, "results": "80", "hashOfConfig": "61"}, {"size": 7582, "mtime": 1752088604705, "results": "81", "hashOfConfig": "61"}, {"size": 2177, "mtime": 1752081612127, "results": "82", "hashOfConfig": "61"}, {"size": 2177, "mtime": 1752088629104, "results": "83", "hashOfConfig": "61"}, {"size": 2319, "mtime": 1752088263487, "results": "84", "hashOfConfig": "61"}, {"size": 5664, "mtime": 1752088293563, "results": "85", "hashOfConfig": "61"}, {"size": 3140, "mtime": 1752084445579, "results": "86", "hashOfConfig": "61"}, {"size": 8547, "mtime": 1752089517104, "results": "87", "hashOfConfig": "61"}, {"size": 14959, "mtime": 1752089735453, "results": "88", "hashOfConfig": "61"}, {"size": 7765, "mtime": 1752089562390, "results": "89", "hashOfConfig": "61"}, {"size": 17374, "mtime": 1752089837560, "results": "90", "hashOfConfig": "61"}, {"size": 18223, "mtime": 1752088369401, "results": "91", "hashOfConfig": "61"}, {"size": 4946, "mtime": 1752083444480, "results": "92", "hashOfConfig": "61"}, {"size": 11984, "mtime": 1752088689248, "results": "93", "hashOfConfig": "61"}, {"size": 15852, "mtime": 1752088492487, "results": "94", "hashOfConfig": "61"}, {"size": 2456, "mtime": 1752082708022, "results": "95", "hashOfConfig": "61"}, {"size": 2224, "mtime": 1752084134751, "results": "96", "hashOfConfig": "61"}, {"size": 3258, "mtime": 1752081710503, "results": "97", "hashOfConfig": "61"}, {"size": 1318, "mtime": 1752083753141, "results": "98", "hashOfConfig": "61"}, {"size": 1004, "mtime": 1752082764670, "results": "99", "hashOfConfig": "61"}, {"size": 464, "mtime": 1752081098838, "results": "100", "hashOfConfig": "61"}, {"size": 3557, "mtime": 1752083314341, "results": "101", "hashOfConfig": "61"}, {"size": 1022, "mtime": 1752081688716, "results": "102", "hashOfConfig": "61"}, {"size": 762, "mtime": 1752081148637, "results": "103", "hashOfConfig": "61"}, {"size": 2775, "mtime": 1752081116343, "results": "104", "hashOfConfig": "61"}, {"size": 774, "mtime": 1752081156282, "results": "105", "hashOfConfig": "61"}, {"size": 2017, "mtime": 1752084984919, "results": "106", "hashOfConfig": "61"}, {"size": 2878, "mtime": 1752082950505, "results": "107", "hashOfConfig": "61"}, {"size": 3618, "mtime": 1752085138545, "results": "108", "hashOfConfig": "61"}, {"size": 2760, "mtime": 1752084998351, "results": "109", "hashOfConfig": "61"}, {"size": 1110, "mtime": 1752083419010, "results": "110", "hashOfConfig": "61"}, {"size": 2732, "mtime": 1752085013945, "results": "111", "hashOfConfig": "61"}, {"size": 5311, "mtime": 1752086050197, "results": "112", "hashOfConfig": "61"}, {"size": 1313, "mtime": 1752082153728, "results": "113", "hashOfConfig": "61"}, {"size": 2070, "mtime": 1752083384239, "results": "114", "hashOfConfig": "61"}, {"size": 212, "mtime": 1752080660398, "results": "115", "hashOfConfig": "61"}, {"size": 1943, "mtime": 1752080682263, "results": "116", "hashOfConfig": "61"}, {"size": 790, "mtime": 1752082120623, "results": "117", "hashOfConfig": "61"}, {"size": 7936, "mtime": 1752087716736, "results": "118", "hashOfConfig": "61"}, {"size": 1038, "mtime": 1752083399645, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ynoq7n", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\assets\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\budget\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\liabilities\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\login\\page.tsx", ["297"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\receivables\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\signup\\page.tsx", ["298", "299"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\transactions\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\assets\\AssetForm.tsx", ["300", "301", "302", "303"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\assets\\AssetList.tsx", ["304"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\budget\\BudgetPlanner.tsx", ["305"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\categories\\CategoryForm.tsx", ["306", "307", "308"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\categories\\CategoryList.tsx", ["309", "310"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\AssetChart.tsx", ["311", "312"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\Dashboard.tsx", ["313"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\NetWorthCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\QuickStats.tsx", ["314", "315"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\MobileSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\liabilities\\LiabilityForm.tsx", ["316", "317"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\liabilities\\LiabilityList.tsx", ["318"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\receivables\\ReceivableForm.tsx", ["319", "320"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\receivables\\ReceivableList.tsx", ["321"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\reports\\FinancialReports.tsx", ["322", "323", "324"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\settings\\CurrencySettings.tsx", ["325"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\transactions\\QuickTransactionForm.tsx", ["326", "327"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\transactions\\TransactionList.tsx", ["328", "329"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\FilterDropdown.tsx", ["330", "331"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\GradientBackground.tsx", ["332"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Input.tsx", ["333"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\QuickActionButton.tsx", ["334"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\SearchInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Select.tsx", ["335"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Textarea.tsx", ["336"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\assets.ts", ["337"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\categories.ts", ["338"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\dashboard.ts", ["339"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\liabilities.ts", ["340"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\profile.ts", ["341"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\receivables.ts", ["342"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\api\\transactions.ts", ["343", "344", "345", "346", "347", "348", "349", "350", "351", "352"], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\currency.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\types\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\lib\\utils.ts", [], [], {"ruleId": "353", "severity": 2, "message": "354", "line": 105, "column": 18, "nodeType": "355", "messageId": "356", "suggestions": "357"}, {"ruleId": "358", "severity": 2, "message": "359", "line": 15, "column": 9, "nodeType": null, "messageId": "360", "endLine": 15, "endColumn": 15}, {"ruleId": "353", "severity": 2, "message": "354", "line": 42, "column": 17, "nodeType": "355", "messageId": "356", "suggestions": "361"}, {"ruleId": "358", "severity": 2, "message": "362", "line": 3, "column": 20, "nodeType": null, "messageId": "360", "endLine": 3, "endColumn": 29}, {"ruleId": "363", "severity": 2, "message": "364", "line": 53, "column": 48, "nodeType": "365", "messageId": "366", "endLine": 53, "endColumn": 51, "suggestions": "367"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 73, "column": 14, "nodeType": "365", "messageId": "366", "endLine": 73, "endColumn": 17, "suggestions": "368"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 90, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 90, "endColumn": 50, "suggestions": "369"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 128, "column": 51, "nodeType": "365", "messageId": "366", "endLine": 128, "endColumn": 54, "suggestions": "370"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 263, "column": 98, "nodeType": "365", "messageId": "366", "endLine": 263, "endColumn": 101, "suggestions": "371"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 44, "column": 55, "nodeType": "365", "messageId": "366", "endLine": 44, "endColumn": 58, "suggestions": "372"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 46, "column": 42, "nodeType": "365", "messageId": "366", "endLine": 46, "endColumn": 45, "suggestions": "373"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 56, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 56, "endColumn": 50, "suggestions": "374"}, {"ruleId": "358", "severity": 2, "message": "375", "line": 7, "column": 29, "nodeType": null, "messageId": "360", "endLine": 7, "endColumn": 39}, {"ruleId": "358", "severity": 2, "message": "376", "line": 7, "column": 41, "nodeType": null, "messageId": "360", "endLine": 7, "endColumn": 50}, {"ruleId": "358", "severity": 2, "message": "377", "line": 3, "column": 61, "nodeType": null, "messageId": "360", "endLine": 3, "endColumn": 67}, {"ruleId": "363", "severity": 2, "message": "364", "line": 20, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 20, "endColumn": 50, "suggestions": "378"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 130, "column": 70, "nodeType": "365", "messageId": "366", "endLine": 130, "endColumn": 73, "suggestions": "379"}, {"ruleId": "358", "severity": 2, "message": "375", "line": 1, "column": 29, "nodeType": null, "messageId": "360", "endLine": 1, "endColumn": 39}, {"ruleId": "358", "severity": 2, "message": "376", "line": 1, "column": 41, "nodeType": null, "messageId": "360", "endLine": 1, "endColumn": 50}, {"ruleId": "380", "severity": 1, "message": "381", "line": 41, "column": 6, "nodeType": "382", "endLine": 41, "endColumn": 44, "suggestions": "383"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 70, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 70, "endColumn": 50, "suggestions": "384"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "385", "line": 382, "column": 11}, {"ruleId": "380", "severity": 1, "message": "381", "line": 44, "column": 6, "nodeType": "382", "endLine": 44, "endColumn": 45, "suggestions": "386"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 78, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 78, "endColumn": 50, "suggestions": "387"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "385", "line": 422, "column": 11}, {"ruleId": "358", "severity": 2, "message": "388", "line": 5, "column": 67, "nodeType": null, "messageId": "360", "endLine": 5, "endColumn": 73}, {"ruleId": "358", "severity": 2, "message": "389", "line": 9, "column": 10, "nodeType": null, "messageId": "360", "endLine": 9, "endColumn": 15}, {"ruleId": "380", "severity": 1, "message": "390", "line": 47, "column": 6, "nodeType": "382", "endLine": 47, "endColumn": 31, "suggestions": "391"}, {"ruleId": "358", "severity": 2, "message": "392", "line": 6, "column": 10, "nodeType": null, "messageId": "360", "endLine": 6, "endColumn": 16}, {"ruleId": "380", "severity": 1, "message": "393", "line": 48, "column": 6, "nodeType": "382", "endLine": 48, "endColumn": 21, "suggestions": "394"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 91, "column": 47, "nodeType": "365", "messageId": "366", "endLine": 91, "endColumn": 50, "suggestions": "395"}, {"ruleId": "358", "severity": 2, "message": "396", "line": 5, "column": 39, "nodeType": null, "messageId": "360", "endLine": 5, "endColumn": 43}, {"ruleId": "358", "severity": 2, "message": "388", "line": 5, "column": 53, "nodeType": null, "messageId": "360", "endLine": 5, "endColumn": 59}, {"ruleId": "363", "severity": 2, "message": "364", "line": 17, "column": 26, "nodeType": "365", "messageId": "366", "endLine": 17, "endColumn": 29, "suggestions": "397"}, {"ruleId": "363", "severity": 2, "message": "364", "line": 18, "column": 34, "nodeType": "365", "messageId": "366", "endLine": 18, "endColumn": 37, "suggestions": "398"}, {"ruleId": "358", "severity": 2, "message": "399", "line": 3, "column": 10, "nodeType": null, "messageId": "360", "endLine": 3, "endColumn": 16}, {"ruleId": "400", "severity": 2, "message": "401", "line": 5, "column": 18, "nodeType": "402", "messageId": "403", "endLine": 5, "endColumn": 28, "suggestions": "404"}, {"ruleId": "358", "severity": 2, "message": "392", "line": 6, "column": 10, "nodeType": null, "messageId": "360", "endLine": 6, "endColumn": 16}, {"ruleId": "400", "severity": 2, "message": "401", "line": 4, "column": 18, "nodeType": "402", "messageId": "403", "endLine": 4, "endColumn": 29, "suggestions": "405"}, {"ruleId": "400", "severity": 2, "message": "401", "line": 4, "column": 18, "nodeType": "402", "messageId": "403", "endLine": 4, "endColumn": 31, "suggestions": "406"}, {"ruleId": "358", "severity": 2, "message": "407", "line": 2, "column": 15, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 20}, {"ruleId": "358", "severity": 2, "message": "408", "line": 2, "column": 15, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 23}, {"ruleId": "363", "severity": 2, "message": "364", "line": 10, "column": 23, "nodeType": "365", "messageId": "366", "endLine": 10, "endColumn": 26, "suggestions": "409"}, {"ruleId": "358", "severity": 2, "message": "410", "line": 2, "column": 15, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 24}, {"ruleId": "358", "severity": 2, "message": "411", "line": 2, "column": 15, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 22}, {"ruleId": "358", "severity": 2, "message": "412", "line": 2, "column": 15, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 25}, {"ruleId": "358", "severity": 2, "message": "413", "line": 2, "column": 23, "nodeType": null, "messageId": "360", "endLine": 2, "endColumn": 34}, {"ruleId": "358", "severity": 2, "message": "414", "line": 3, "column": 27, "nodeType": null, "messageId": "360", "endLine": 3, "endColumn": 42}, {"ruleId": "358", "severity": 2, "message": "415", "line": 4, "column": 28, "nodeType": null, "messageId": "360", "endLine": 4, "endColumn": 44}, {"ruleId": "358", "severity": 2, "message": "416", "line": 107, "column": 5, "nodeType": null, "messageId": "360", "endLine": 107, "endColumn": 17}, {"ruleId": "358", "severity": 2, "message": "417", "line": 108, "column": 5, "nodeType": null, "messageId": "360", "endLine": 108, "endColumn": 22}, {"ruleId": "358", "severity": 2, "message": "418", "line": 109, "column": 5, "nodeType": null, "messageId": "360", "endLine": 109, "endColumn": 22}, {"ruleId": "358", "severity": 2, "message": "419", "line": 110, "column": 5, "nodeType": null, "messageId": "360", "endLine": 110, "endColumn": 14}, {"ruleId": "358", "severity": 2, "message": "420", "line": 111, "column": 5, "nodeType": null, "messageId": "360", "endLine": 111, "endColumn": 18}, {"ruleId": "358", "severity": 2, "message": "421", "line": 112, "column": 5, "nodeType": null, "messageId": "360", "endLine": 112, "endColumn": 19}, {"ruleId": "358", "severity": 2, "message": "422", "line": 113, "column": 5, "nodeType": null, "messageId": "360", "endLine": 113, "endColumn": 15}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["423", "424", "425", "426"], "@typescript-eslint/no-unused-vars", "'router' is assigned a value but never used.", "unusedVar", ["427", "428", "429", "430"], "'useEffect' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["431", "432"], ["433", "434"], ["435", "436"], ["437", "438"], ["439", "440"], ["441", "442"], ["443", "444"], ["445", "446"], "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Legend' is defined but never used.", ["447", "448"], ["449", "450"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formData.current_balance'. Either include it or remove the dependency array.", "ArrayExpression", ["451"], ["452", "453"], "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?", ["454"], ["455", "456"], "'Filter' is defined but never used.", "'Input' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadReportData'. Either include it or remove the dependency array.", ["457"], "'Button' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCategories'. Either include it or remove the dependency array.", ["458"], ["459", "460"], "'Edit' is defined but never used.", ["461", "462"], ["463", "464"], "'motion' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["465"], ["466"], ["467"], "'Asset' is defined but never used.", "'Category' is defined but never used.", ["468", "469"], "'Liability' is defined but never used.", "'Profile' is defined but never used.", "'Receivable' is defined but never used.", "'updateAsset' is defined but never used.", "'updateLiability' is defined but never used.", "'updateReceivable' is defined but never used.", "'updateAssets' is assigned a value but never used.", "'updateLiabilities' is assigned a value but never used.", "'updateReceivables' is assigned a value but never used.", "'assetName' is assigned a value but never used.", "'liabilityName' is assigned a value but never used.", "'receivableName' is assigned a value but never used.", "'debtorName' is assigned a value but never used.", {"messageId": "470", "data": "471", "fix": "472", "desc": "473"}, {"messageId": "470", "data": "474", "fix": "475", "desc": "476"}, {"messageId": "470", "data": "477", "fix": "478", "desc": "479"}, {"messageId": "470", "data": "480", "fix": "481", "desc": "482"}, {"messageId": "470", "data": "483", "fix": "484", "desc": "473"}, {"messageId": "470", "data": "485", "fix": "486", "desc": "476"}, {"messageId": "470", "data": "487", "fix": "488", "desc": "479"}, {"messageId": "470", "data": "489", "fix": "490", "desc": "482"}, {"messageId": "491", "fix": "492", "desc": "493"}, {"messageId": "494", "fix": "495", "desc": "496"}, {"messageId": "491", "fix": "497", "desc": "493"}, {"messageId": "494", "fix": "498", "desc": "496"}, {"messageId": "491", "fix": "499", "desc": "493"}, {"messageId": "494", "fix": "500", "desc": "496"}, {"messageId": "491", "fix": "501", "desc": "493"}, {"messageId": "494", "fix": "502", "desc": "496"}, {"messageId": "491", "fix": "503", "desc": "493"}, {"messageId": "494", "fix": "504", "desc": "496"}, {"messageId": "491", "fix": "505", "desc": "493"}, {"messageId": "494", "fix": "506", "desc": "496"}, {"messageId": "491", "fix": "507", "desc": "493"}, {"messageId": "494", "fix": "508", "desc": "496"}, {"messageId": "491", "fix": "509", "desc": "493"}, {"messageId": "494", "fix": "510", "desc": "496"}, {"messageId": "491", "fix": "511", "desc": "493"}, {"messageId": "494", "fix": "512", "desc": "496"}, {"messageId": "491", "fix": "513", "desc": "493"}, {"messageId": "494", "fix": "514", "desc": "496"}, {"desc": "515", "fix": "516"}, {"messageId": "491", "fix": "517", "desc": "493"}, {"messageId": "494", "fix": "518", "desc": "496"}, {"desc": "519", "fix": "520"}, {"messageId": "491", "fix": "521", "desc": "493"}, {"messageId": "494", "fix": "522", "desc": "496"}, {"desc": "523", "fix": "524"}, {"desc": "525", "fix": "526"}, {"messageId": "491", "fix": "527", "desc": "493"}, {"messageId": "494", "fix": "528", "desc": "496"}, {"messageId": "491", "fix": "529", "desc": "493"}, {"messageId": "494", "fix": "530", "desc": "496"}, {"messageId": "491", "fix": "531", "desc": "493"}, {"messageId": "494", "fix": "532", "desc": "496"}, {"messageId": "533", "fix": "534", "desc": "535"}, {"messageId": "533", "fix": "536", "desc": "535"}, {"messageId": "533", "fix": "537", "desc": "535"}, {"messageId": "491", "fix": "538", "desc": "493"}, {"messageId": "494", "fix": "539", "desc": "496"}, "replaceWithAlt", {"alt": "540"}, {"range": "541", "text": "542"}, "Replace with `&apos;`.", {"alt": "543"}, {"range": "544", "text": "545"}, "Replace with `&lsquo;`.", {"alt": "546"}, {"range": "547", "text": "548"}, "Replace with `&#39;`.", {"alt": "549"}, {"range": "550", "text": "551"}, "Replace with `&rsquo;`.", {"alt": "540"}, {"range": "552", "text": "553"}, {"alt": "543"}, {"range": "554", "text": "555"}, {"alt": "546"}, {"range": "556", "text": "557"}, {"alt": "549"}, {"range": "558", "text": "559"}, "suggestUnknown", {"range": "560", "text": "561"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "562", "text": "563"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "564", "text": "561"}, {"range": "565", "text": "563"}, {"range": "566", "text": "561"}, {"range": "567", "text": "563"}, {"range": "568", "text": "561"}, {"range": "569", "text": "563"}, {"range": "570", "text": "561"}, {"range": "571", "text": "563"}, {"range": "572", "text": "561"}, {"range": "573", "text": "563"}, {"range": "574", "text": "561"}, {"range": "575", "text": "563"}, {"range": "576", "text": "561"}, {"range": "577", "text": "563"}, {"range": "578", "text": "561"}, {"range": "579", "text": "563"}, {"range": "580", "text": "561"}, {"range": "581", "text": "563"}, "Update the dependencies array to be: [formData.current_balance, formData.principal_amount, liability]", {"range": "582", "text": "583"}, {"range": "584", "text": "561"}, {"range": "585", "text": "563"}, "Update the dependencies array to be: [formData.current_balance, formData.principal_amount, receivable]", {"range": "586", "text": "587"}, {"range": "588", "text": "561"}, {"range": "589", "text": "563"}, "Update the dependencies array to be: [reportPeriod, dateRange, loadReportData]", {"range": "590", "text": "591"}, "Update the dependencies array to be: [formData.type, loadCategories]", {"range": "592", "text": "593"}, {"range": "594", "text": "561"}, {"range": "595", "text": "563"}, {"range": "596", "text": "561"}, {"range": "597", "text": "563"}, {"range": "598", "text": "561"}, {"range": "599", "text": "563"}, "replaceEmptyInterfaceWithSuper", {"range": "600", "text": "601"}, "Replace empty interface with a type alias.", {"range": "602", "text": "603"}, {"range": "604", "text": "605"}, {"range": "606", "text": "561"}, {"range": "607", "text": "563"}, "&apos;", [4418, 4455], "\n              Don&apos;t have an account?", "&lsquo;", [4418, 4455], "\n              Don&lsquo;t have an account?", "&#39;", [4418, 4455], "\n              Don&#39;t have an account?", "&rsquo;", [4418, 4455], "\n              Don&rsquo;t have an account?", [1225, 1357], "\n              We&apos;ve sent you a confirmation link. Please check your email and click the link to activate your account.\n            ", [1225, 1357], "\n              We&lsquo;ve sent you a confirmation link. Please check your email and click the link to activate your account.\n            ", [1225, 1357], "\n              We&#39;ve sent you a confirmation link. Please check your email and click the link to activate your account.\n            ", [1225, 1357], "\n              We&rsquo;ve sent you a confirmation link. Please check your email and click the link to activate your account.\n            ", [1621, 1624], "unknown", [1621, 1624], "never", [2549, 2552], [2549, 2552], [3010, 3013], [3010, 3013], [4044, 4047], [4044, 4047], [10192, 10195], [10192, 10195], [1581, 1584], [1581, 1584], [1642, 1645], [1642, 1645], [1845, 1848], [1845, 1848], [676, 679], [676, 679], [4796, 4799], [4796, 4799], [1631, 1669], "[formData.current_balance, formData.principal_amount, liability]", [2394, 2397], [2394, 2397], [1739, 1778], "[formData.current_balance, formData.principal_amount, receivable]", [2642, 2645], [2642, 2645], [1633, 1658], "[report<PERSON><PERSON><PERSON>, dateRange, loadReportData]", [1484, 1499], "[formData.type, loadCategories]", [2889, 2892], [2889, 2892], [442, 445], [442, 445], [480, 483], [480, 483], [136, 205], "type InputProps = InputHTMLAttributes<HTMLInputElement>", [98, 170], "type SelectProps = SelectHTMLAttributes<HTMLSelectElement>", [100, 178], "type TextareaProps = TextareaHTMLAttributes<HTMLTextAreaElement>", [339, 342], [339, 342]]