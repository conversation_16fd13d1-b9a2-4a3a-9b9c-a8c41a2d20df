import { createClient } from '@/lib/supabase/client'
import type { Profile, Database } from '@/lib/types/database'

type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

export async function getProfile() {
  const supabase = createClient()
  
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  
  if (userError || !user) {
    return { profile: null, error: userError }
  }

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  return { profile, error: profileError }
}

export async function updateProfile(updates: ProfileUpdate) {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error('User not authenticated')

  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', user.id)
    .select()
    .single()

  return { data, error }
}

export async function updateCurrency(currency: 'LKR' | 'USD') {
  return updateProfile({ preferred_currency: currency })
}
