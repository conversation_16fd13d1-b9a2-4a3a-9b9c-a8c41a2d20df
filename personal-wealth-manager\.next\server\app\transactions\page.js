(()=>{var e={};e.id=790,e.ids=[790],e.modules={1688:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\transactions\\\\TransactionList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\transactions\\TransactionList.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d={children:["",{children:["transactions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,40390)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\transactions\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\transactions\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/transactions/page",pathname:"/transactions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},39944:(e,s,t)=>{Promise.resolve().then(t.bind(t,63087)),Promise.resolve().then(t.bind(t,95966))},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40390:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(46655),n=t(1688);function l(){return(0,r.jsx)(a.default,{children:(0,r.jsx)(n.default,{})})}},44547:(e,s,t)=>{"use strict";t.d(s,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>l,nA:()=>x,nd:()=>o});var r=t(60687),a=t(43210),n=t(4780);let l=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let i=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let c=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));c.displayName="TableBody",(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("bg-gray-900/5 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",e),...s}));d.displayName="TableRow";let o=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",(0,a.forwardRef)(({className:e,...s},t)=>(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-gray-500",e),...s})).displayName="TableCaption"},53096:(e,s,t)=>{Promise.resolve().then(t.bind(t,46655)),Promise.resolve().then(t.bind(t,1688))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95966:(e,s,t)=>{"use strict";t.d(s,{default:()=>y});var r=t(60687),a=t(43210),n=t(88920),l=t(26001),i=t(96474),c=t(5748),d=t(66594),o=t(40228),x=t(88233),m=t(2643),p=t(28749),h=t(44547),u=t(99760),j=t(99537),f=t(4780),g=t(67413);function y(){let[e,s]=(0,a.useState)([]),[t,y]=(0,a.useState)(!0),[b,N]=(0,a.useState)(!1),[v,w]=(0,a.useState)(""),[C,A]=(0,a.useState)("all"),[k,_]=(0,a.useState)({income:0,expense:0,transfer:0,netIncome:0}),P=async()=>{y(!0);let{data:e}=await (0,j.I0)();e&&s(e),y(!1)},T=async()=>{let{data:e}=await (0,j.zA)();e&&_(e)},q=async e=>{confirm("Are you sure you want to delete this transaction?")&&(await (0,j.Uw)(e),P(),T())},R=e.filter(e=>{let s=""===v||e.description?.toLowerCase().includes(v.toLowerCase())||e.categories?.name.toLowerCase().includes(v.toLowerCase()),t="all"===C||e.type===C;return s&&t}),M=e=>{switch(e){case"income":default:return i.A;case"expense":return c.A;case"transfer":return d.A}},$=e=>{switch(e){case"income":return"text-green-600 bg-green-50";case"expense":return"text-red-600 bg-red-50";case"transfer":return"text-blue-600 bg-blue-50";default:return"text-gray-600 bg-gray-50"}};return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Transactions"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Track your income, expenses, and transfers"})]}),(0,r.jsxs)(m.$,{onClick:()=>N(!0),variant:"gradient",className:"w-full sm:w-auto",children:[(0,r.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"Add Transaction"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,r.jsx)(p.Card,{className:"bg-gradient-to-br from-green-50 to-emerald-100 border-green-200",children:(0,r.jsx)(p.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Total Income"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-700",children:(0,f.vv)(k.income,"LKR")})]}),(0,r.jsx)("div",{className:"p-3 bg-green-200 rounded-xl",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-green-700"})})]})})}),(0,r.jsx)(p.Card,{className:"bg-gradient-to-br from-red-50 to-rose-100 border-red-200",children:(0,r.jsx)(p.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-red-600 text-sm font-medium",children:"Total Expenses"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-700",children:(0,f.vv)(k.expense,"LKR")})]}),(0,r.jsx)("div",{className:"p-3 bg-red-200 rounded-xl",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-red-700"})})]})})}),(0,r.jsx)(p.Card,{className:"bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200",children:(0,r.jsx)(p.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Total Transfers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-700",children:(0,f.vv)(k.transfer,"LKR")})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-200 rounded-xl",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-blue-700"})})]})})}),(0,r.jsx)(p.Card,{className:`bg-gradient-to-br ${k.netIncome>=0?"from-emerald-50 to-green-100 border-emerald-200":"from-red-50 to-rose-100 border-red-200"}`,children:(0,r.jsx)(p.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:`text-sm font-medium ${k.netIncome>=0?"text-emerald-600":"text-red-600"}`,children:"Net Income"}),(0,r.jsx)("p",{className:`text-2xl font-bold ${k.netIncome>=0?"text-emerald-700":"text-red-700"}`,children:(0,f.vv)(k.netIncome,"LKR")})]}),(0,r.jsx)("div",{className:`p-3 rounded-xl ${k.netIncome>=0?"bg-emerald-200":"bg-red-200"}`,children:(0,r.jsx)(o.A,{className:`h-6 w-6 ${k.netIncome>=0?"text-emerald-700":"text-red-700"}`})})]})})})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)(u.A,{value:v,onChange:w,placeholder:"Search transactions...",className:"flex-1"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:["all","income","expense","transfer"].map(e=>(0,r.jsx)(m.$,{variant:C===e?"primary":"outline",size:"sm",onClick:()=>A(e),className:"capitalize flex-1 sm:flex-none min-w-0",children:"all"===e?"All":e},e))})]}),(0,r.jsxs)(p.Card,{children:[(0,r.jsx)(p.CardHeader,{children:(0,r.jsx)(p.CardTitle,{children:"Recent Transactions"})}),(0,r.jsx)(p.CardContent,{children:t?(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===R.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),(0,r.jsx)("p",{className:"text-gray-500 mb-6",children:0===e.length?"Start tracking your finances by adding your first transaction":"No transactions match your search criteria"}),(0,r.jsxs)(m.$,{onClick:()=>N(!0),children:[(0,r.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Add Transaction"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsxs)(h.XI,{children:[(0,r.jsx)(h.A0,{children:(0,r.jsxs)(h.Hj,{children:[(0,r.jsx)(h.nd,{children:"Type"}),(0,r.jsx)(h.nd,{children:"Description"}),(0,r.jsx)(h.nd,{children:"Category"}),(0,r.jsx)(h.nd,{children:"Amount"}),(0,r.jsx)(h.nd,{children:"Date"}),(0,r.jsx)(h.nd,{children:"Actions"})]})}),(0,r.jsx)(h.BF,{children:(0,r.jsx)(n.N,{children:R.map((e,s)=>{let t=M(e.type),a=$(e.type);return(0,r.jsxs)(l.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.05*s},className:"hover:bg-gray-50",children:[(0,r.jsx)(h.nA,{children:(0,r.jsxs)("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a}`,children:[(0,r.jsx)(t,{className:"h-4 w-4 mr-2"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,r.jsx)(h.nA,{className:"font-medium",children:e.description||"No description"}),(0,r.jsx)(h.nA,{children:e.categories?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.categories.color}}),e.categories.name]}):(0,r.jsx)("span",{className:"text-gray-500",children:"Uncategorized"})}),(0,r.jsx)(h.nA,{className:"font-semibold",children:(0,f.vv)(e.amount,e.currency)}),(0,r.jsx)(h.nA,{children:(0,f.Yq)(e.transaction_date)}),(0,r.jsx)(h.nA,{children:(0,r.jsx)("div",{className:"flex space-x-2",children:(0,r.jsx)(m.$,{size:"sm",variant:"destructive",onClick:()=>q(e.id),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})})})]},e.id)})})})]})}),(0,r.jsx)("div",{className:"lg:hidden space-y-4",children:(0,r.jsx)(n.N,{children:R.map((e,s)=>{let t=M(e.type),a=$(e.type);return(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.05*s},className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a}`,children:[(0,r.jsx)(t,{className:"h-4 w-4 mr-2"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]}),(0,r.jsx)(m.$,{size:"sm",variant:"destructive",onClick:()=>q(e.id),className:"ml-2",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.description||"No description"})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center",children:e.categories?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.categories.color}}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:e.categories.name})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Uncategorized"})}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,f.Yq)(e.transaction_date)})]}),(0,r.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(0,f.vv)(e.amount,e.currency)})})]})]},e.id)})})})]})})]}),(0,r.jsx)(n.N,{children:b&&(0,r.jsx)(g.A,{onSuccess:()=>{N(!1),P(),T()},onCancel:()=>N(!1)})})]})}},99760:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(60687);let a=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var n=t(11860),l=t(51907),i=t(2643);function c({value:e,onChange:s,placeholder:t="Search...",className:c}){return(0,r.jsxs)("div",{className:`relative ${c}`,children:[(0,r.jsx)(a,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(l.p,{type:"text",placeholder:t,value:e,onChange:e=>s(e.target.value),className:"pl-10 pr-10"}),e&&(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>s(""),children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,145,79,814,435,235],()=>t(34879));module.exports=r})();