(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{15592:(e,t,a)=>{Promise.resolve().then(a.bind(a,19349))},19349:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(95155),r=a(12115),l=a(35695),n=a(6874),i=a.n(n),c=a(79323);function o(){let[e,t]=(0,r.useState)(""),[a,n]=(0,r.useState)(""),[o,u]=(0,r.useState)(""),[d,m]=(0,r.useState)(!1),[x,p]=(0,r.useState)(""),[h,f]=(0,r.useState)(!1);(0,l.useRouter)();let b=async t=>{t.preventDefault(),m(!0),p("");let{error:s}=await (0,c.Hh)(e,a,o);s?p(s.message):f(!0),m(!1)};return h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Check your email"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"We've sent you a confirmation link. Please check your email and click the link to activate your account."}),(0,s.jsx)(i(),{href:"/login",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-500",children:"Back to sign in"})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(i(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:b,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{id:"fullName",name:"fullName",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your full name",value:o,onChange:e=>u(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Create a password",value:a,onChange:e=>n(e.target.value)})]})]}),x&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center",children:x}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:d,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Creating account...":"Create account"})})]})]})})}},52643:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(43865);function r(){return(0,s.createBrowserClient)("https://lwfiqyypbdphguadzqbe.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.KaxWQZgd1EBY-ksnefW6pokzLTO4LracWY36dnGC0us")}},79323:(e,t,a)=>{"use strict";a.d(t,{CI:()=>n,Hh:()=>r,Jv:()=>l});var s=a(52643);async function r(e,t,a){let r=(0,s.U)(),{data:l,error:n}=await r.auth.signUp({email:e,password:t,options:{data:{full_name:a}}});return{data:l,error:n}}async function l(e,t){let a=(0,s.U)(),{data:r,error:l}=await a.auth.signInWithPassword({email:e,password:t});return{data:r,error:l}}async function n(){let e=(0,s.U)(),{error:t}=await e.auth.signOut();return{error:t}}}},e=>{var t=t=>e(e.s=t);e.O(0,[271,874,441,684,358],()=>t(15592)),_N_E=e.O()}]);