'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Minus, ArrowRightLeft, Edit, Trash2, Filter, Calendar } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import SearchInput from '@/components/ui/SearchInput'
import { getTransactions, deleteTransaction, getTransactionSummary } from '@/lib/api/transactions'
import { formatCurrency, formatDate } from '@/lib/utils'
import QuickTransactionForm from './QuickTransactionForm'
import type { Transaction } from '@/lib/api/transactions'

export default function TransactionList() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [summary, setSummary] = useState({
    income: 0,
    expense: 0,
    transfer: 0,
    netIncome: 0
  })

  useEffect(() => {
    loadTransactions()
    loadSummary()
  }, [])

  const loadTransactions = async () => {
    setLoading(true)
    const { data } = await getTransactions()
    if (data) setTransactions(data)
    setLoading(false)
  }

  const loadSummary = async () => {
    const { data } = await getTransactionSummary()
    if (data) setSummary(data)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this transaction?')) {
      await deleteTransaction(id)
      loadTransactions()
      loadSummary()
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    loadTransactions()
    loadSummary()
  }

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = searchTerm === '' || 
      transaction.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.categories?.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = filterType === 'all' || transaction.type === filterType

    return matchesSearch && matchesType
  })

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'income': return Plus
      case 'expense': return Minus
      case 'transfer': return ArrowRightLeft
      default: return Plus
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'income': return 'text-green-600 bg-green-50'
      case 'expense': return 'text-red-600 bg-red-50'
      case 'transfer': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Transactions
          </h1>
          <p className="text-gray-600 mt-2">Track your income, expenses, and transfers</p>
        </div>
        <Button onClick={() => setShowForm(true)} variant="gradient">
          <Plus className="h-5 w-5 mr-2" />
          Add Transaction
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Total Income</p>
                <p className="text-2xl font-bold text-green-700">
                  {formatCurrency(summary.income, 'LKR')}
                </p>
              </div>
              <div className="p-3 bg-green-200 rounded-xl">
                <Plus className="h-6 w-6 text-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-rose-100 border-red-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-600 text-sm font-medium">Total Expenses</p>
                <p className="text-2xl font-bold text-red-700">
                  {formatCurrency(summary.expense, 'LKR')}
                </p>
              </div>
              <div className="p-3 bg-red-200 rounded-xl">
                <Minus className="h-6 w-6 text-red-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Transfers</p>
                <p className="text-2xl font-bold text-blue-700">
                  {formatCurrency(summary.transfer, 'LKR')}
                </p>
              </div>
              <div className="p-3 bg-blue-200 rounded-xl">
                <ArrowRightLeft className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className={`bg-gradient-to-br ${summary.netIncome >= 0 ? 'from-emerald-50 to-green-100 border-emerald-200' : 'from-red-50 to-rose-100 border-red-200'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${summary.netIncome >= 0 ? 'text-emerald-600' : 'text-red-600'}`}>
                  Net Income
                </p>
                <p className={`text-2xl font-bold ${summary.netIncome >= 0 ? 'text-emerald-700' : 'text-red-700'}`}>
                  {formatCurrency(summary.netIncome, 'LKR')}
                </p>
              </div>
              <div className={`p-3 rounded-xl ${summary.netIncome >= 0 ? 'bg-emerald-200' : 'bg-red-200'}`}>
                <Calendar className={`h-6 w-6 ${summary.netIncome >= 0 ? 'text-emerald-700' : 'text-red-700'}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search transactions..."
          className="flex-1"
        />
        <div className="flex gap-2">
          {['all', 'income', 'expense', 'transfer'].map((type) => (
            <Button
              key={type}
              variant={filterType === type ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilterType(type)}
              className="capitalize"
            >
              {type === 'all' ? 'All' : type}
            </Button>
          ))}
        </div>
      </div>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
              <p className="text-gray-500 mb-6">
                {transactions.length === 0 
                  ? 'Start tracking your finances by adding your first transaction'
                  : 'No transactions match your search criteria'
                }
              </p>
              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Transaction
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <AnimatePresence>
                  {filteredTransactions.map((transaction, index) => {
                    const Icon = getTransactionIcon(transaction.type)
                    const colorClass = getTransactionColor(transaction.type)
                    
                    return (
                      <motion.tr
                        key={transaction.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ delay: index * 0.05 }}
                        className="hover:bg-gray-50"
                      >
                        <TableCell>
                          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}>
                            <Icon className="h-4 w-4 mr-2" />
                            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {transaction.description || 'No description'}
                        </TableCell>
                        <TableCell>
                          {transaction.categories ? (
                            <div className="flex items-center">
                              <div 
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: transaction.categories.color }}
                              />
                              {transaction.categories.name}
                            </div>
                          ) : (
                            <span className="text-gray-500">Uncategorized</span>
                          )}
                        </TableCell>
                        <TableCell className="font-semibold">
                          {formatCurrency(transaction.amount, transaction.currency)}
                        </TableCell>
                        <TableCell>
                          {formatDate(transaction.transaction_date)}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(transaction.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </motion.tr>
                    )
                  })}
                </AnimatePresence>
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Transaction Form Modal */}
      <AnimatePresence>
        {showForm && (
          <QuickTransactionForm
            onSuccess={handleFormSuccess}
            onCancel={() => setShowForm(false)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
