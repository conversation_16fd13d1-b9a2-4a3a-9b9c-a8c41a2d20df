{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\n\nexport default function Home() {\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const checkUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n\n      if (user) {\n        router.push('/dashboard')\n      } else {\n        router.push('/login')\n      }\n    }\n\n    checkUser()\n  }, [router, supabase.auth])\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAEtD,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;QAEA;IACF,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}]}