(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[990],{1243:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});let l=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13717:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});let l=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},22691:(e,a,t)=>{"use strict";t.d(a,{A:()=>x});var l=t(95155),s=t(12115),i=t(19946);let r=(0,i.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),n=(0,i.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var d=t(13741),c=t(30353),u=t(93915),o=t(33985);function x(e){let{filters:a,values:t,onChange:i,onClear:x}=e,[m,h]=(0,s.useState)(!1),p=Object.values(t).filter(e=>""!==e&&null!=e).length;return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(d.$,{variant:"outline",onClick:()=>h(!m),className:"flex items-center",children:[(0,l.jsx)(r,{className:"h-4 w-4 mr-2"}),"Filters",p>0&&(0,l.jsx)("span",{className:"ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5",children:p}),(0,l.jsx)(n,{className:"h-4 w-4 ml-2"})]}),m&&(0,l.jsx)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:(0,l.jsxs)("div",{className:"p-4 space-y-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h3",{className:"font-medium",children:"Filters"}),(0,l.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>{x(),h(!1)},children:"Clear All"})]}),a.map(e=>(0,l.jsxs)("div",{children:[(0,l.jsx)(o.J,{htmlFor:e.key,children:e.label}),"select"===e.type&&e.options?(0,l.jsxs)(c.l,{id:e.key,value:t[e.key]||"",onChange:a=>i(e.key,a.target.value),children:[(0,l.jsx)("option",{value:"",children:"All"}),e.options.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,l.jsx)(u.p,{id:e.key,type:"date"===e.type?"date":"number"===e.type?"number":"text",value:t[e.key]||"",onChange:a=>i(e.key,a.target.value)})]},e.key)),(0,l.jsx)("div",{className:"flex justify-end pt-2",children:(0,l.jsx)(d.$,{size:"sm",onClick:()=>h(!1),children:"Apply Filters"})})]})})]})}},43071:(e,a,t)=>{"use strict";t.d(a,{default:()=>_});var l=t(95155),s=t(12115),i=t(1243),r=t(84616),n=t(13717),d=t(62525),c=t(13741),u=t(17703),o=t(75399),x=t(93154),m=t(22691),h=t(47839),p=t(59434),j=t(93915),y=t(33985),v=t(30353),b=t(41659);function f(e){let{liability:a,onSuccess:t,onCancel:i}=e,[r,n]=(0,s.useState)(!1),[d,o]=(0,s.useState)({name:(null==a?void 0:a.name)||"",description:(null==a?void 0:a.description)||"",principal_amount:(null==a?void 0:a.principal_amount)||0,current_balance:(null==a?void 0:a.current_balance)||0,interest_rate:(null==a?void 0:a.interest_rate)||0,due_date:(null==a?void 0:a.due_date)||"",liability_type:(null==a?void 0:a.liability_type)||"loan_taken",status:(null==a?void 0:a.status)||"active",currency:(null==a?void 0:a.currency)||"LKR"}),[x,m]=(0,s.useState)("annual");(0,s.useEffect)(()=>{!a&&d.principal_amount>0&&0===d.current_balance&&o(e=>({...e,current_balance:e.principal_amount}))},[d.principal_amount,a]);let p=async e=>{e.preventDefault(),n(!0);try{let e;if((e=a?await (0,h.p9)(a.id,d):await (0,h.W2)(d)).error){console.error("Error saving liability:",e.error),alert("Error saving liability: "+e.error.message);return}t()}catch(e){console.error("Error saving liability:",e),alert("Error saving liability: "+e.message)}finally{n(!1)}},f=(e,a)=>{if(("principal_amount"===e||"current_balance"===e||"interest_rate"===e)&&"number"==typeof a&&a>0xe8d4a50fff)return void alert("Value is too large. Please enter a smaller amount.");o(t=>({...t,[e]:a}))};return(0,l.jsxs)(u.Card,{className:"w-full max-w-2xl",children:[(0,l.jsx)(u.CardHeader,{children:(0,l.jsx)(u.CardTitle,{children:a?"Edit Liability":"Add New Liability"})}),(0,l.jsx)(u.CardContent,{children:(0,l.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"name",children:"Liability Name *"}),(0,l.jsx)(j.p,{id:"name",value:d.name,onChange:e=>f("name",e.target.value),required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"liability_type",children:"Type *"}),(0,l.jsxs)(v.l,{id:"liability_type",value:d.liability_type,onChange:e=>f("liability_type",e.target.value),required:!0,children:[(0,l.jsx)("option",{value:"loan_taken",children:"Loan Taken"}),(0,l.jsx)("option",{value:"credit_card",children:"Credit Card"}),(0,l.jsx)("option",{value:"mortgage",children:"Mortgage"}),(0,l.jsx)("option",{value:"insurance",children:"Insurance"}),(0,l.jsx)("option",{value:"utilities",children:"Utilities"}),(0,l.jsx)("option",{value:"taxes",children:"Taxes"}),(0,l.jsx)("option",{value:"subscription",children:"Subscription"}),(0,l.jsx)("option",{value:"rent",children:"Rent"}),(0,l.jsx)("option",{value:"medical",children:"Medical"}),(0,l.jsx)("option",{value:"education",children:"Education"}),(0,l.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"description",children:"Description"}),(0,l.jsx)(b.T,{id:"description",value:d.description,onChange:e=>f("description",e.target.value),rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"principal_amount",children:"Principal Amount *"}),(0,l.jsx)(j.p,{id:"principal_amount",type:"number",step:"0.01",value:d.principal_amount,onChange:e=>f("principal_amount",parseFloat(e.target.value)||0),required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"current_balance",children:"Current Balance *"}),(0,l.jsx)(j.p,{id:"current_balance",type:"number",step:"0.01",value:d.current_balance,onChange:e=>f("current_balance",parseFloat(e.target.value)||0),required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"interest_rate",children:"Interest Rate (%) - Optional"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(j.p,{id:"interest_rate",type:"number",step:"0.01",min:"0",max:"100",value:d.interest_rate||"",onChange:e=>f("interest_rate",parseFloat(e.target.value)||0),placeholder:"0.00",className:"flex-1"}),(0,l.jsxs)(v.l,{value:x,onChange:e=>m(e.target.value),className:"w-32",children:[(0,l.jsx)("option",{value:"annual",children:"Annual"}),(0,l.jsx)("option",{value:"monthly",children:"Monthly"}),(0,l.jsx)("option",{value:"daily",children:"Daily"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"due_date",children:"Due Date - Optional"}),(0,l.jsx)(j.p,{id:"due_date",type:"date",value:d.due_date,onChange:e=>f("due_date",e.target.value)})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"status",children:"Status"}),(0,l.jsxs)(v.l,{id:"status",value:d.status,onChange:e=>f("status",e.target.value),children:[(0,l.jsx)("option",{value:"active",children:"Active"}),(0,l.jsx)("option",{value:"paid_off",children:"Paid Off"}),(0,l.jsx)("option",{value:"defaulted",children:"Defaulted"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(y.J,{htmlFor:"currency",children:"Currency"}),(0,l.jsxs)(v.l,{id:"currency",value:d.currency,onChange:e=>f("currency",e.target.value),children:[(0,l.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,l.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,l.jsx)(c.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,l.jsx)(c.$,{type:"submit",disabled:r,children:r?"Saving...":a?"Update Liability":"Create Liability"})]})]})})]})}function _(){let[e,a]=(0,s.useState)([]),[t,j]=(0,s.useState)(!0),[y,v]=(0,s.useState)(!1),[b,_]=(0,s.useState)(),[g,N]=(0,s.useState)(""),[w,C]=(0,s.useState)({liability_type:"",status:"",min_balance:"",max_balance:"",due_date_from:"",due_date_to:""});(0,s.useEffect)(()=>{k()},[]);let k=async()=>{j(!0);let{data:e}=await (0,h.mz)();e&&a(e),j(!1)},A=async e=>{confirm("Are you sure you want to delete this liability?")&&(await (0,h.pq)(e),k())},D=e=>{_(e),v(!0)},S=(0,s.useMemo)(()=>e.filter(e=>{var a;let t=""===g||e.name.toLowerCase().includes(g.toLowerCase())||(null==(a=e.description)?void 0:a.toLowerCase().includes(g.toLowerCase()))||e.liability_type.toLowerCase().includes(g.toLowerCase()),l=""===w.liability_type||e.liability_type===w.liability_type,s=""===w.status||e.status===w.status,i=""===w.min_balance||e.current_balance>=parseFloat(w.min_balance),r=""===w.max_balance||e.current_balance<=parseFloat(w.max_balance),n=""===w.due_date_from||!e.due_date||new Date(e.due_date)>=new Date(w.due_date_from),d=""===w.due_date_to||!e.due_date||new Date(e.due_date)<=new Date(w.due_date_to);return t&&l&&s&&i&&r&&n&&d}),[e,g,w]),F=S.filter(e=>"active"===e.status).reduce((e,a)=>e+a.current_balance,0),L=S.filter(e=>"active"===e.status&&e.due_date&&new Date(e.due_date)<new Date),T=e=>{switch(e){case"active":return"text-green-600";case"paid_off":return"text-blue-600";case"defaulted":return"text-red-600";default:return"text-gray-600"}};return y?(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsx)(f,{liability:b,onSuccess:()=>{v(!1),_(void 0),k()},onCancel:()=>{v(!1),_(void 0)}})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Liabilities"}),(0,l.jsxs)("p",{className:"text-gray-600",children:["Total Outstanding: ",(0,p.vv)(F),S.length!==e.length&&(0,l.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",S.length," of ",e.length," shown)"]})]}),L.length>0&&(0,l.jsxs)("p",{className:"text-red-600 flex items-center mt-1",children:[(0,l.jsx)(i.A,{className:"h-4 w-4 mr-1"}),L.length," overdue liability(ies)"]})]}),(0,l.jsxs)(c.$,{onClick:()=>v(!0),className:"w-full sm:w-auto",children:[(0,l.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Add Liability"]})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,l.jsx)(x.A,{value:g,onChange:N,placeholder:"Search liabilities...",className:"flex-1"}),(0,l.jsx)(m.A,{filters:[{key:"liability_type",label:"Liability Type",type:"select",options:[{value:"loan_taken",label:"Loan Taken"},{value:"credit_card",label:"Credit Card"},{value:"mortgage",label:"Mortgage"},{value:"other",label:"Other"}]},{key:"status",label:"Status",type:"select",options:[{value:"active",label:"Active"},{value:"paid_off",label:"Paid Off"},{value:"defaulted",label:"Defaulted"}]},{key:"min_balance",label:"Minimum Balance",type:"number"},{key:"max_balance",label:"Maximum Balance",type:"number"},{key:"due_date_from",label:"Due Date From",type:"date"},{key:"due_date_to",label:"Due Date To",type:"date"}],values:w,onChange:(e,a)=>{C(t=>({...t,[e]:a}))},onClear:()=>{C({liability_type:"",status:"",min_balance:"",max_balance:"",due_date_from:"",due_date_to:""}),N("")}})]}),(0,l.jsxs)(u.Card,{children:[(0,l.jsx)(u.CardHeader,{children:(0,l.jsx)(u.CardTitle,{children:"Your Liabilities"})}),(0,l.jsxs)(u.CardContent,{children:[t?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===S.length?(0,l.jsx)("div",{className:"text-center py-8 text-gray-500",children:0===e.length?"No liabilities found. Add your first liability to get started.":"No liabilities match your search criteria."}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"hidden lg:block",children:(0,l.jsxs)(o.XI,{children:[(0,l.jsx)(o.A0,{children:(0,l.jsxs)(o.Hj,{children:[(0,l.jsx)(o.nd,{children:"Name"}),(0,l.jsx)(o.nd,{children:"Type"}),(0,l.jsx)(o.nd,{children:"Principal"}),(0,l.jsx)(o.nd,{children:"Current Balance"}),(0,l.jsx)(o.nd,{children:"Interest Rate"}),(0,l.jsx)(o.nd,{children:"Due Date"}),(0,l.jsx)(o.nd,{children:"Status"}),(0,l.jsx)(o.nd,{children:"Actions"})]})}),(0,l.jsx)(o.BF,{children:S.map(e=>{let a="active"===e.status&&e.due_date&&new Date(e.due_date)<new Date;return(0,l.jsxs)(o.Hj,{className:a?"bg-red-50":"",children:[(0,l.jsxs)(o.nA,{className:"font-medium",children:[e.name,a&&(0,l.jsx)(i.A,{className:"h-4 w-4 text-red-500 inline ml-2"})]}),(0,l.jsx)(o.nA,{className:"capitalize",children:e.liability_type.replace("_"," ")}),(0,l.jsx)(o.nA,{children:(0,p.vv)(e.principal_amount,e.currency)}),(0,l.jsx)(o.nA,{children:(0,p.vv)(e.current_balance,e.currency)}),(0,l.jsx)(o.nA,{children:e.interest_rate?"".concat(e.interest_rate,"%"):"-"}),(0,l.jsx)(o.nA,{children:e.due_date?(0,p.Yq)(e.due_date):"-"}),(0,l.jsx)(o.nA,{children:(0,l.jsx)("span",{className:"capitalize ".concat(T(e.status)),children:e.status.replace("_"," ")})}),(0,l.jsx)(o.nA,{children:(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:(0,l.jsx)(n.A,{className:"h-4 w-4"})}),(0,l.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),(0,l.jsx)("div",{className:"lg:hidden space-y-4",children:S.map(e=>{let a="active"===e.status&&e.due_date&&new Date(e.due_date)<new Date;return(0,l.jsxs)("div",{className:"bg-white border rounded-lg p-4 shadow-sm ".concat(a?"border-red-200 bg-red-50":"border-gray-200"),children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"font-medium text-gray-900 flex items-center",children:[e.name,a&&(0,l.jsx)(i.A,{className:"h-4 w-4 text-red-500 ml-2"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-500 capitalize",children:e.liability_type.replace("_"," ")})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:(0,l.jsx)(n.A,{className:"h-4 w-4"})}),(0,l.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Principal:"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:(0,p.vv)(e.principal_amount,e.currency)})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Current Balance:"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:(0,p.vv)(e.current_balance,e.currency)})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Interest Rate:"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:e.interest_rate?"".concat(e.interest_rate,"%"):"-"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Due Date:"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:e.due_date?(0,p.Yq)(e.due_date):"-"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"Status:"}),(0,l.jsx)("span",{className:"text-sm font-medium capitalize ".concat(T(e.status)),children:e.status.replace("_"," ")})]})]})]},e.id)})})]}),")}"]})]})]})}},62525:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});let l=(0,t(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},75399:(e,a,t)=>{"use strict";t.d(a,{A0:()=>n,BF:()=>d,Hj:()=>c,XI:()=>r,nA:()=>o,nd:()=>u});var l=t(95155),s=t(12115),i=t(59434);let r=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("div",{className:"relative w-full overflow-auto",children:(0,l.jsx)("table",{ref:a,className:(0,i.cn)("w-full caption-bottom text-sm",t),...s})})});r.displayName="Table";let n=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("thead",{ref:a,className:(0,i.cn)("[&_tr]:border-b",t),...s})});n.displayName="TableHeader";let d=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("tbody",{ref:a,className:(0,i.cn)("[&_tr:last-child]:border-0",t),...s})});d.displayName="TableBody",(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("tfoot",{ref:a,className:(0,i.cn)("bg-gray-900/5 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter";let c=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("tr",{ref:a,className:(0,i.cn)("border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",t),...s})});c.displayName="TableRow";let u=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("th",{ref:a,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",t),...s})});u.displayName="TableHead";let o=(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("td",{ref:a,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})});o.displayName="TableCell",(0,s.forwardRef)((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("caption",{ref:a,className:(0,i.cn)("mt-4 text-sm text-gray-500",t),...s})}).displayName="TableCaption"},91351:(e,a,t)=>{Promise.resolve().then(t.bind(t,13049)),Promise.resolve().then(t.bind(t,43071))},93154:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var l=t(95155);let s=(0,t(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var i=t(54416),r=t(93915),n=t(13741);function d(e){let{value:a,onChange:t,placeholder:d="Search...",className:c}=e;return(0,l.jsxs)("div",{className:"relative ".concat(c),children:[(0,l.jsx)(s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(r.p,{type:"text",placeholder:d,value:a,onChange:e=>t(e.target.value),className:"pl-10 pr-10"}),a&&(0,l.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>t(""),children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[271,874,556,49,441,684,358],()=>a(91351)),_N_E=e.O()}]);