"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[49],{13049:(e,t,a)=>{a.d(t,{default:()=>q});var r=a(95155),s=a(12115),n=a(6874),i=a.n(n),l=a(35695),o=a(73783),c=a(46308),d=a(16785),u=a(72713),m=a(33109),h=a(81586),x=a(17580),p=a(43332),g=a(381),f=a(34835),b=a(79323),y=a(59434);let v=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Transactions",href:"/transactions",icon:c.A},{name:"Budget",href:"/budget",icon:d.A},{name:"Reports",href:"/reports",icon:u.A},{name:"Assets",href:"/assets",icon:m.A},{name:"Liabilities",href:"/liabilities",icon:h.A},{name:"Receivables",href:"/receivables",icon:x.A},{name:"Categories",href:"/categories",icon:p.A},{name:"Settings",href:"/settings",icon:g.A}];function w(){let e=(0,l.usePathname)(),t=async()=>{await (0,b.CI)(),window.location.href="/login"};return(0,r.jsxs)("div",{className:"flex h-full w-64 flex-col bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl",children:[(0,r.jsx)("div",{className:"flex h-20 items-center px-6 border-b border-gray-200/50",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"W"})}),(0,r.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 px-4 py-6",children:v.map(t=>{let a=e===t.href;return(0,r.jsxs)(i(),{href:t.href,className:(0,y.cn)("group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 hover:scale-105 active:scale-95",a?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25":"text-gray-700 hover:bg-white/60 hover:text-gray-900 hover:shadow-md"),children:[(0,r.jsx)(t.icon,{className:(0,y.cn)("mr-3 h-5 w-5 flex-shrink-0",a?"text-white":"text-gray-500 group-hover:text-gray-700")}),t.name]},t.name)})}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-3",children:(0,r.jsxs)("button",{onClick:t,className:"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(f.A,{className:"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500"}),"Sign out"]})})]})}var j=a(15939),N=a(80280),C=a(54416);let A=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Transactions",href:"/transactions",icon:c.A},{name:"Budget",href:"/budget",icon:d.A},{name:"Reports",href:"/reports",icon:u.A},{name:"Assets",href:"/assets",icon:m.A},{name:"Liabilities",href:"/liabilities",icon:h.A},{name:"Receivables",href:"/receivables",icon:x.A},{name:"Categories",href:"/categories",icon:p.A},{name:"Settings",href:"/settings",icon:g.A}];function U(e){let{isOpen:t,onClose:a}=e,n=(0,l.usePathname)(),o=async()=>{await (0,b.CI)(),window.location.href="/login"};return(0,r.jsx)(j.e.Root,{show:t,as:s.Fragment,children:(0,r.jsxs)(N.lG,{as:"div",className:"relative z-50 lg:hidden",onClose:a,children:[(0,r.jsx)(j.e.Child,{as:s.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-900/80"})}),(0,r.jsx)("div",{className:"fixed inset-0 flex",children:(0,r.jsx)(j.e.Child,{as:s.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:(0,r.jsx)(N.lG.Panel,{className:"relative mr-16 flex w-full max-w-xs flex-1",children:(0,r.jsxs)("div",{className:"flex grow flex-col gap-y-5 overflow-y-auto bg-white/95 backdrop-blur-xl px-6 pb-4 shadow-xl",children:[(0,r.jsxs)("div",{className:"flex h-16 shrink-0 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,r.jsx)("h1",{className:"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]}),(0,r.jsx)("button",{type:"button",className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",onClick:a,children:(0,r.jsx)(C.A,{className:"h-6 w-6"})})]}),(0,r.jsx)("nav",{className:"flex flex-1 flex-col",children:(0,r.jsxs)("ul",{role:"list",className:"flex flex-1 flex-col gap-y-7",children:[(0,r.jsx)("li",{children:(0,r.jsx)("ul",{role:"list",className:"-mx-2 space-y-1",children:A.map(e=>{let t=n===e.href;return(0,r.jsx)("li",{children:(0,r.jsxs)(i(),{href:e.href,onClick:a,className:(0,y.cn)("group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-semibold transition-all duration-200",t?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg":"text-gray-700 hover:text-gray-900 hover:bg-gray-50"),children:[(0,r.jsx)(e.icon,{className:(0,y.cn)("h-6 w-6 shrink-0",t?"text-white":"text-gray-400 group-hover:text-gray-600")}),e.name]})},e.name)})})}),(0,r.jsx)("li",{className:"mt-auto",children:(0,r.jsxs)("button",{onClick:o,className:"group -mx-2 flex w-full gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(f.A,{className:"h-6 w-6 shrink-0 text-gray-400 group-hover:text-gray-600"}),"Sign out"]})})]})})]})})})})]})})}var R=a(52643);function k(e){let{children:t}=e,[a,n]=(0,s.useState)(null),[i,o]=(0,s.useState)(!0),c=(0,l.useRouter)(),d=(0,R.U)();return((0,s.useEffect)(()=>{(async()=>{let{data:{user:e}}=await d.auth.getUser();n(e),o(!1),e||c.push("/login")})();let{data:{subscription:e}}=d.auth.onAuthStateChange((e,t)=>{"SIGNED_OUT"!==e&&t?"SIGNED_IN"===e&&n(t.user):c.push("/login")});return()=>e.unsubscribe()},[c,d.auth]),i)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):a?(0,r.jsx)(r.Fragment,{children:t}):null}let _=function(e){let{children:t,variant:a="default"}=e;return(0,r.jsxs)("div",{className:"min-h-screen ".concat({default:"bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",dashboard:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",auth:"bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800"}[a]," relative overflow-hidden"),children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl"})]}),(0,r.jsx)("div",{className:"relative z-10",children:t})]})};var S=a(60760),I=a(76408),L=a(84616),T=a(87712),F=a(76356),D=a(14943);function E(){let[e,t]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!1),[i,l]=(0,s.useState)("expense"),o=[{type:"income",icon:L.A,label:"Income",color:"bg-green-500 hover:bg-green-600"},{type:"expense",icon:T.A,label:"Expense",color:"bg-red-500 hover:bg-red-600"},{type:"transfer",icon:F.A,label:"Transfer",color:"bg-blue-500 hover:bg-blue-600"}],c=e=>{l(e),n(!0),t(!1)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"fixed bottom-6 right-6 z-40",children:[(0,r.jsx)(S.N,{children:e&&(0,r.jsx)(I.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"absolute bottom-16 right-0 space-y-3",children:o.map((e,t)=>(0,r.jsxs)(I.P.button,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{delay:.1*t},onClick:()=>c(e.type),className:"\n                    flex items-center space-x-3 px-4 py-3 rounded-xl text-white font-medium\n                    shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\n                    ".concat(e.color,"\n                  "),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"whitespace-nowrap",children:e.label})]},e.type))})}),(0,r.jsx)(I.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>t(!e),className:"\n            w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200\n            flex items-center justify-center text-white font-bold text-xl\n            ".concat(e?"bg-gray-500 hover:bg-gray-600":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700","\n          "),children:(0,r.jsx)(I.P.div,{animate:{rotate:45*!!e},transition:{duration:.2},children:e?(0,r.jsx)(C.A,{className:"h-6 w-6"}):(0,r.jsx)(L.A,{className:"h-6 w-6"})})})]}),(0,r.jsx)(S.N,{children:a&&(0,r.jsx)(D.A,{defaultType:i,onSuccess:()=>{n(!1),window.location.reload()},onCancel:()=>n(!1)})})]})}var J=a(74783);function q(e){let{children:t}=e,[a,n]=(0,s.useState)(!1);return(0,r.jsx)(k,{children:(0,r.jsx)(_,{variant:"dashboard",children:(0,r.jsxs)("div",{className:"flex h-screen",children:[(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsx)(w,{})}),(0,r.jsx)(U,{isOpen:a,onClose:()=>n(!1)}),(0,r.jsxs)("main",{className:"flex-1 overflow-auto",children:[(0,r.jsxs)("div",{className:"lg:hidden bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 py-3 flex items-center justify-between",children:[(0,r.jsx)("button",{onClick:()=>n(!0),className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:(0,r.jsx)(J.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,r.jsx)("h1",{className:"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]})]}),(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8",children:t})]}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)(E,{})})]})})})}},13741:(e,t,a)=>{a.d(t,{$:()=>l});var r=a(95155),s=a(12115),n=a(76408),i=a(59434);let l=(0,s.forwardRef)((e,t)=>{let{className:a,variant:s="primary",size:l="md",loading:o=!1,children:c,...d}=e;return(0,r.jsxs)(n.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:(0,i.cn)("inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm hover:shadow-md active:scale-95",{primary:"bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-200",secondary:"bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300",outline:"border-2 border-blue-600 text-blue-600 bg-transparent hover:bg-blue-50",ghost:"text-gray-600 hover:bg-gray-100 hover:text-gray-900",destructive:"bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-200",gradient:"bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 text-white hover:from-purple-700 hover:via-blue-700 hover:to-teal-700"}[s],{sm:"h-9 px-4 text-sm",md:"h-11 px-6 py-2.5",lg:"h-13 px-8 text-lg"}[l],a),ref:t,disabled:o,...d,children:[o&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});l.displayName="Button"},14943:(e,t,a)=>{a.d(t,{A:()=>b});var r=a(95155),s=a(12115),n=a(76408),i=a(84616),l=a(87712),o=a(76356),c=a(54416),d=a(13741),u=a(93915),m=a(33985),h=a(30353),x=a(41659),p=a(17703),g=a(72901),f=a(19816);function b(e){let{onSuccess:t,onCancel:a,defaultType:b="expense"}=e,[y,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)([]),[N,C]=(0,s.useState)({type:b,amount:"",description:"",category_id:"",transaction_date:new Date().toISOString().split("T")[0],currency:"LKR",updateAssets:!1,updateLiabilities:!1,updateReceivables:!1,assetName:"",liabilityName:"",receivableName:"",debtorName:""});(0,s.useEffect)(()=>{A()},[N.type]);let A=async()=>{let{data:e}=await (0,f.bW)(N.type);e&&j(e)},U=async e=>{e.preventDefault(),v(!0);try{let e=await (0,g.ON)({type:N.type,amount:parseFloat(N.amount),description:N.description||null,category_id:N.category_id||null,transaction_date:N.transaction_date,currency:N.currency,updateAssets:N.updateAssets,updateLiabilities:N.updateLiabilities,updateReceivables:N.updateReceivables,assetName:N.assetName,liabilityName:N.liabilityName,receivableName:N.receivableName,debtorName:N.debtorName});if(e.error){console.error("Error creating transaction:",e.error),alert("Error creating transaction: "+e.error.message);return}t()}catch(e){console.error("Error creating transaction:",e),alert("Error creating transaction: "+e.message)}finally{v(!1)}},R=(e,t)=>{C(a=>({...a,[e]:t}))},k=[{value:"income",label:"Income",icon:i.A,color:"text-green-600",bg:"bg-green-50"},{value:"expense",label:"Expense",icon:l.A,color:"text-red-600",bg:"bg-red-50"},{value:"transfer",label:"Transfer",icon:o.A,color:"text-blue-600",bg:"bg-blue-50"}],_=k.find(e=>e.value===N.type);return(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50",children:(0,r.jsxs)(p.Card,{className:"w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)(p.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)(p.CardTitle,{className:"flex items-center",children:[_&&(0,r.jsx)("div",{className:"p-2 rounded-xl ".concat(_.bg," mr-3"),children:(0,r.jsx)(_.icon,{className:"h-5 w-5 ".concat(_.color)})}),"Add New Transaction"]}),(0,r.jsx)(d.$,{variant:"ghost",size:"sm",onClick:a,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(p.CardContent,{children:(0,r.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{children:"Transaction Type"}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-2 sm:gap-3 mt-2",children:k.map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>R("type",e.value),className:"\n                      p-3 sm:p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center space-y-1 sm:space-y-2\n                      ".concat(N.type===e.value?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                    "),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5 sm:h-6 sm:w-6 ".concat(e.color)}),(0,r.jsx)("span",{className:"font-medium text-xs sm:text-sm",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"amount",children:"Amount *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.p,{id:"amount",type:"number",step:"0.01",value:N.amount,onChange:e=>R("amount",e.target.value),placeholder:"0.00",required:!0,className:"pl-12"}),(0,r.jsx)("span",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium",children:"LKR"===N.currency?"Rs.":"$"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"currency",children:"Currency"}),(0,r.jsxs)(h.l,{id:"currency",value:N.currency,onChange:e=>R("currency",e.target.value),children:[(0,r.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,r.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"category_id",children:"Category"}),(0,r.jsxs)(h.l,{id:"category_id",value:N.category_id,onChange:e=>R("category_id",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),w.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"transaction_date",children:"Date"}),(0,r.jsx)(u.p,{id:"transaction_date",type:"date",value:N.transaction_date,onChange:e=>R("transaction_date",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)(x.T,{id:"description",value:N.description,onChange:e=>R("description",e.target.value),placeholder:"Add a note about this transaction...",rows:3})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Advanced Options"}),(0,r.jsxs)("div",{className:"space-y-4",children:["income"===N.type&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateAssets",checked:N.updateAssets,onChange:e=>R("updateAssets",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateAssets",children:"Add to Assets"}),N.updateAssets&&(0,r.jsx)(u.p,{placeholder:"Asset name",value:N.assetName,onChange:e=>R("assetName",e.target.value),className:"flex-1"})]}),"expense"===N.type&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateLiabilities",checked:N.updateLiabilities,onChange:e=>R("updateLiabilities",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateLiabilities",children:"Add to Liabilities"}),N.updateLiabilities&&(0,r.jsx)(u.p,{placeholder:"Liability name",value:N.liabilityName,onChange:e=>R("liabilityName",e.target.value),className:"flex-1"})]}),"transfer"===N.type&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateReceivables",checked:N.updateReceivables,onChange:e=>R("updateReceivables",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateReceivables",children:"Add to Receivables"})]}),N.updateReceivables&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsx)(u.p,{placeholder:"Receivable name",value:N.receivableName,onChange:e=>R("receivableName",e.target.value)}),(0,r.jsx)(u.p,{placeholder:"Debtor name",value:N.debtorName,onChange:e=>R("debtorName",e.target.value)})]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,r.jsx)(d.$,{type:"submit",loading:y,children:"Add Transaction"})]})]})})]})})}},17703:(e,t,a)=>{a.d(t,{Card:()=>l,CardContent:()=>d,CardHeader:()=>o,CardTitle:()=>c});var r=a(95155),s=a(12115),n=a(76408),i=a(59434);let l=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},ref:t,className:(0,i.cn)("rounded-2xl border border-gray-100 bg-white shadow-lg shadow-gray-100/50 backdrop-blur-sm hover:shadow-xl hover:shadow-gray-200/50 transition-all duration-300",a),...s})});l.displayName="Card";let o=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-2 p-8 pb-4",a),...s})});o.displayName="CardHeader";let c=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-bold leading-none tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",a),...s})});c.displayName="CardTitle",(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-gray-500 leading-relaxed",a),...s})}).displayName="CardDescription";let d=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("px-8 pb-8",a),...s})});d.displayName="CardContent",(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center px-8 pb-8 pt-0",a),...s})}).displayName="CardFooter"},19816:(e,t,a)=>{a.d(t,{K7:()=>l,bW:()=>s,st:()=>i,zZ:()=>n});var r=a(52643);async function s(e){let t=(0,r.U)().from("categories").select("*").order("name");e&&(t=t.eq("type",e));let{data:a,error:s}=await t;return{data:a,error:s}}async function n(e){let t=(0,r.U)(),{data:{user:a}}=await t.auth.getUser();if(!a)throw Error("User not authenticated");let{data:s,error:n}=await t.from("categories").insert({...e,user_id:a.id}).select().single();return{data:s,error:n}}async function i(e,t){let a=(0,r.U)(),{data:s,error:n}=await a.from("categories").update(t).eq("id",e).select().single();return{data:s,error:n}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("categories").delete().eq("id",e);return{error:a}}},30353:(e,t,a)=>{a.d(t,{l:()=>i});var r=a(95155),s=a(12115),n=a(59434);let i=(0,s.forwardRef)((e,t)=>{let{className:a,children:s,...i}=e;return(0,r.jsx)("select",{className:(0,n.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...i,children:s})});i.displayName="Select"},33985:(e,t,a)=>{a.d(t,{J:()=>i});var r=a(95155),s=a(12115),n=a(59434);let i=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("label",{ref:t,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",a),...s})});i.displayName="Label"},41659:(e,t,a)=>{a.d(t,{T:()=>i});var r=a(95155),s=a(12115),n=a(59434);let i=(0,s.forwardRef)((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...s})});i.displayName="Textarea"},47839:(e,t,a)=>{a.d(t,{W2:()=>n,mz:()=>s,p9:()=>i,pq:()=>l});var r=a(52643);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("liabilities").select("\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    ").order("created_at",{ascending:!1});return{data:t,error:a}}async function n(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:n,error:i}=await t.from("liabilities").insert({...e,user_id:a.id}).select().single();return{data:n,error:i}}async function i(e,t){let a=(0,r.U)(),{data:s,error:n}=await a.from("liabilities").update(t).eq("id",e).select().single();return{data:s,error:n}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("liabilities").delete().eq("id",e);return{error:a}}},52643:(e,t,a)=>{a.d(t,{U:()=>s});var r=a(43865);function s(){return(0,r.createBrowserClient)("https://lwfiqyypbdphguadzqbe.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3ZmlxeXlwYmRwaGd1YWR6cWJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwODA2NTMsImV4cCI6MjA2NzY1NjY1M30.KaxWQZgd1EBY-ksnefW6pokzLTO4LracWY36dnGC0us")}},59434:(e,t,a)=>{a.d(t,{Yq:()=>l,cn:()=>n,vv:()=>i});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"LKR";return"LKR"===t?new Intl.NumberFormat("en-US",{style:"currency",currency:"LKR",currencyDisplay:"symbol",minimumFractionDigits:2,maximumFractionDigits:2}).format(e).replace("LKR","Rs."):new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}},72901:(e,t,a)=>{a.d(t,{I0:()=>l,ON:()=>d,Uw:()=>c,zA:()=>u});var r=a(52643),s=a(81053),n=a(47839),i=a(76443);async function l(){let e=(0,r.U)(),{data:t,error:a}=await e.from("transactions").select("\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    ").order("transaction_date",{ascending:!1});return{data:t,error:a}}async function o(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:n,error:i}=await t.from("transactions").insert({...e,user_id:a.id}).select().single();return{data:n,error:i}}async function c(e){let t=(0,r.U)(),{error:a}=await t.from("transactions").delete().eq("id",e);return{error:a}}async function d(e){let{updateAssets:t,updateLiabilities:a,updateReceivables:r,assetName:l,liabilityName:c,receivableName:d,debtorName:u,...m}=e,{data:h,error:x}=await o(m);return x||!h?{data:null,error:x}:("income"===e.type&&e.updateAssets&&e.assetName&&await (0,s.$o)({name:e.assetName,current_value:e.amount,asset_type:"cash",currency:e.currency||"LKR",description:"From transaction: ".concat(e.description||"Income")}),"expense"===e.type&&e.updateLiabilities&&e.liabilityName&&await (0,n.W2)({name:e.liabilityName,principal_amount:e.amount,current_balance:e.amount,liability_type:"other",currency:e.currency||"LKR",description:"From transaction: ".concat(e.description||"Expense")}),"transfer"===e.type&&e.updateReceivables&&e.receivableName&&e.debtorName&&await (0,i.XM)({debtor_name:e.debtorName,principal_amount:e.amount,current_balance:e.amount,currency:e.currency||"LKR",description:"From transaction: ".concat(e.description||"Transfer")}),{data:h,error:null})}async function u(e,t){let a=(0,r.U)().from("transactions").select("type, amount, currency");e&&(a=a.gte("transaction_date",e)),t&&(a=a.lte("transaction_date",t));let{data:s,error:n}=await a;if(n||!s)return{data:null,error:n};let i=s.reduce((e,t)=>{let{type:a,amount:r}=t;return e[a]||(e[a]=0),e[a]+=r,e},{});return{data:{income:i.income||0,expense:i.expense||0,transfer:i.transfer||0,netIncome:(i.income||0)-(i.expense||0)},error:null}}},76443:(e,t,a)=>{a.d(t,{A0:()=>s,Be:()=>o,OX:()=>i,Sc:()=>c,XM:()=>n,eg:()=>l});var r=a(52643);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("receivables").select("\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    ").order("created_at",{ascending:!1});return{data:t,error:a}}async function n(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:n,error:i}=await t.from("receivables").insert({...e,user_id:a.id}).select().single();return{data:n,error:i}}async function i(e,t){let a=(0,r.U)(),{data:s,error:n}=await a.from("receivables").update(t).eq("id",e).select().single();return{data:s,error:n}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("receivables").delete().eq("id",e);return{error:a}}async function o(){let e=(0,r.U)(),{data:t,error:a}=await e.from("categories").select("*").eq("type","receivable").order("name");return{data:t,error:a}}function c(e){let t=new Date(e);return Math.max(0,Math.ceil((new Date().getTime()-t.getTime())/864e5))}},79323:(e,t,a)=>{a.d(t,{CI:()=>i,Hh:()=>s,Jv:()=>n});var r=a(52643);async function s(e,t,a){let s=(0,r.U)(),{data:n,error:i}=await s.auth.signUp({email:e,password:t,options:{data:{full_name:a}}});return{data:n,error:i}}async function n(e,t){let a=(0,r.U)(),{data:s,error:n}=await a.auth.signInWithPassword({email:e,password:t});return{data:s,error:n}}async function i(){let e=(0,r.U)(),{error:t}=await e.auth.signOut();return{error:t}}},81053:(e,t,a)=>{a.d(t,{$o:()=>n,AO:()=>l,Y:()=>s,gT:()=>i});var r=a(52643);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("assets").select("\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    ").order("created_at",{ascending:!1});return{data:t,error:a}}async function n(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:n,error:i}=await t.from("assets").insert({...e,user_id:a.id}).select().single();return{data:n,error:i}}async function i(e,t){let a=(0,r.U)(),{data:s,error:n}=await a.from("assets").update(t).eq("id",e).select().single();return{data:s,error:n}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("assets").delete().eq("id",e);return{error:a}}},93915:(e,t,a)=>{a.d(t,{p:()=>l});var r=a(95155),s=a(12115),n=a(76408),i=a(59434);let l=(0,s.forwardRef)((e,t)=>{let{className:a,type:s,...l}=e;return(0,r.jsx)(n.P.input,{whileFocus:{scale:1.02},type:s,className:(0,i.cn)("flex h-12 w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-sm font-medium ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md",a),ref:t,...l})});l.displayName="Input"}}]);