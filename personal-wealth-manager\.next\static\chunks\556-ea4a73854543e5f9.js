"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[556],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4993:(e,t,n)=>{var r=n(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useSyncExternalStore,s=r.useRef,a=r.useEffect,l=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=s(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var p=o(e,(d=l(function(){function e(e){if(!a){if(a=!0,o=e,e=r(e),void 0!==c&&h.hasValue){var t=h.value;if(c(t,e))return s=t}return s=e}if(t=s,i(o,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(o=e,t):(o=e,s=n)}var o,s,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,c]))[0],d[1]);return a(function(){h.hasValue=!0,h.value=p},[p]),u(p),p}},6232:(e,t,n)=>{n.d(t,{Y:()=>o});var r=n(12115),i=n(21231);function o(e){let t=(0,r.useRef)(e);return(0,i.s)(()=>{t.current=e},[e]),t}},6983:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},7856:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},13250:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(12115),i=n(21231);function o(){let e=(0,r.useRef)(!1);return(0,i.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},15939:(e,t,n)=>{n.d(t,{e:()=>F,_:()=>D});var r,i,o=n(12115),s=n(48014),a=n(30797),l=n(13250),u=n(21231),c=n(6232),d=n(89925),h=n(47769),p=n(45261),m=n(49509);void 0!==m&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==m?void 0:m.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(i=null==Element?void 0:Element.prototype)?void 0:i.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var f=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(f||{}),g=n(91525),v=n(20379),y=n(27279),b=n(84554);function x(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:S)!==o.Fragment||1===o.Children.count(e.children)}let w=(0,o.createContext)(null);w.displayName="TransitionContext";var k=(e=>(e.Visible="visible",e.Hidden="hidden",e))(k||{});let E=(0,o.createContext)(null);function P(e){return"children"in e?P(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function T(e,t){let n=(0,c.Y)(e),r=(0,o.useRef)([]),i=(0,l.a)(),u=(0,s.L)(),d=(0,a._)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:b.mK.Hidden,o=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==o&&((0,y.Y)(t,{[b.mK.Unmount](){r.current.splice(o,1)},[b.mK.Hidden](){r.current[o].state="hidden"}}),u.microTask(()=>{var e;!P(r)&&i.current&&(null==(e=n.current)||e.call(n))}))}),h=(0,a._)(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,b.mK.Unmount)}),p=(0,o.useRef)([]),m=(0,o.useRef)(Promise.resolve()),f=(0,o.useRef)({enter:[],leave:[]}),g=(0,a._)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(f.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?m.current=m.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),v=(0,a._)((e,t,n)=>{Promise.all(f.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:h,unregister:d,onStart:g,onStop:v,wait:m,chains:f}),[h,d,r,g,v,f,m])}E.displayName="NestingContext";let S=o.Fragment,A=b.Ac.RenderStrategy,M=(0,b.FX)(function(e,t){let{show:n,appear:r=!1,unmount:i=!0,...s}=e,l=(0,o.useRef)(null),c=x(e),p=(0,h.P)(...c?[l,t]:null===t?[]:[t]);(0,d.g)();let m=(0,g.O_)();if(void 0===n&&null!==m&&(n=(m&g.Uw.Open)===g.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[f,v]=(0,o.useState)(n?"visible":"hidden"),y=T(()=>{n||v("hidden")}),[k,S]=(0,o.useState)(!0),M=(0,o.useRef)([n]);(0,u.s)(()=>{!1!==k&&M.current[M.current.length-1]!==n&&(M.current.push(n),S(!1))},[M,n]);let D=(0,o.useMemo)(()=>({show:n,appear:r,initial:k}),[n,r,k]);(0,u.s)(()=>{n?v("visible"):P(y)||null===l.current||v("hidden")},[n,y]);let F={unmount:i},R=(0,a._)(()=>{var t;k&&S(!1),null==(t=e.beforeEnter)||t.call(e)}),V=(0,a._)(()=>{var t;k&&S(!1),null==(t=e.beforeLeave)||t.call(e)}),j=(0,b.Ci)();return o.createElement(E.Provider,{value:y},o.createElement(w.Provider,{value:D},j({ourProps:{...F,as:o.Fragment,children:o.createElement(C,{ref:p,...F,...s,beforeEnter:R,beforeLeave:V})},theirProps:{},defaultTag:o.Fragment,features:A,visible:"visible"===f,name:"Transition"})))}),C=(0,b.FX)(function(e,t){var n,r;let{transition:i=!0,beforeEnter:l,afterEnter:c,beforeLeave:m,afterLeave:f,enter:k,enterFrom:M,enterTo:C,entered:D,leave:F,leaveFrom:R,leaveTo:V,...j}=e,[L,O]=(0,o.useState)(null),I=(0,o.useRef)(null),N=x(e),B=(0,h.P)(...N?[I,t,O]:null===t?[]:[t]),U=null==(n=j.unmount)||n?b.mK.Unmount:b.mK.Hidden,{show:z,appear:_,initial:W}=function(){let e=(0,o.useContext)(w);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[$,Y]=(0,o.useState)(z?"visible":"hidden"),H=function(){let e=(0,o.useContext)(E);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:X,unregister:K}=H;(0,u.s)(()=>X(I),[X,I]),(0,u.s)(()=>{if(U===b.mK.Hidden&&I.current)return z&&"visible"!==$?void Y("visible"):(0,y.Y)($,{hidden:()=>K(I),visible:()=>X(I)})},[$,I,X,K,z,U]);let q=(0,d.g)();(0,u.s)(()=>{if(N&&q&&"visible"===$&&null===I.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[I,$,q,N]);let G=W&&!_,Z=_&&z&&W,Q=(0,o.useRef)(!1),J=T(()=>{Q.current||(Y("hidden"),K(I))},H),ee=(0,a._)(e=>{Q.current=!0,J.onStart(I,e?"enter":"leave",e=>{"enter"===e?null==l||l():"leave"===e&&(null==m||m())})}),et=(0,a._)(e=>{let t=e?"enter":"leave";Q.current=!1,J.onStop(I,t,e=>{"enter"===e?null==c||c():"leave"===e&&(null==f||f())}),"leave"!==t||P(J)||(Y("hidden"),K(I))});(0,o.useEffect)(()=>{N&&i||(ee(z),et(z))},[z,N,i]);let[,en]=function(e,t,n,r){let[i,a]=(0,o.useState)(n),{hasFlag:l,addFlag:c,removeFlag:d}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,o.useState)(e),r=(0,o.useCallback)(e=>n(e),[t]),i=(0,o.useCallback)(e=>n(t=>t|e),[t]),s=(0,o.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:i,hasFlag:s,removeFlag:(0,o.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,o.useCallback)(e=>n(t=>t^e),[n])}}(e&&i?3:0),h=(0,o.useRef)(!1),m=(0,o.useRef)(!1),f=(0,s.L)();return(0,u.s)(()=>{var i;if(e){if(n&&a(!0),!t){n&&c(3);return}return null==(i=null==r?void 0:r.start)||i.call(r,n),function(e,t){let{prepare:n,run:r,done:i,inFlight:o}=t,s=(0,p.e)();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let i=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=i}(e,{prepare:n,inFlight:o}),s.nextFrame(()=>{r(),s.requestAnimationFrame(()=>{s.add(function(e,t){var n,r;let i=(0,p.e)();if(!e)return i.dispose;let o=!1;i.add(()=>{o=!0});let s=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===s.length?t():Promise.allSettled(s.map(e=>e.finished)).then(()=>{o||t()}),i.dispose}(e,i))})}),s.dispose}(t,{inFlight:h,prepare(){m.current?m.current=!1:m.current=h.current,h.current=!0,m.current||(n?(c(3),d(4)):(c(4),d(2)))},run(){m.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;m.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(h.current=!1,d(7),n||a(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,f]),e?[i,{closed:l(1),enter:l(2),leave:l(4),transition:l(2)||l(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!i||!N||!q||G),L,z,{start:ee,end:et}),er=(0,b.oE)({ref:B,className:(null==(r=(0,v.x)(j.className,Z&&k,Z&&M,en.enter&&k,en.enter&&en.closed&&M,en.enter&&!en.closed&&C,en.leave&&F,en.leave&&!en.closed&&R,en.leave&&en.closed&&V,!en.transition&&z&&D))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(en)}),ei=0;"visible"===$&&(ei|=g.Uw.Open),"hidden"===$&&(ei|=g.Uw.Closed),z&&"hidden"===$&&(ei|=g.Uw.Opening),z||"visible"!==$||(ei|=g.Uw.Closing);let eo=(0,b.Ci)();return o.createElement(E.Provider,{value:J},o.createElement(g.El,{value:ei},eo({ourProps:er,theirProps:j,defaultTag:S,features:A,visible:"visible"===$,name:"Transition.Child"})))}),D=(0,b.FX)(function(e,t){let n=null!==(0,o.useContext)(w),r=null!==(0,g.O_)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(M,{ref:t,...e}):o.createElement(C,{ref:t,...e}))}),F=Object.assign(M,{Child:D,Root:M})},16785:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(12115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:h,...p}=e;return(0,r.createElement)("svg",{ref:t,...u,width:i,height:i,stroke:n,strokeWidth:s?24*Number(o)/Number(i):o,className:a("lucide",c),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:l,...u}=n;return(0,r.createElement)(c,{ref:o,iconNode:t,className:a("lucide-".concat(i(s(e))),"lucide-".concat(e),l),...u})});return n.displayName=s(e),n}},20379:(e,t,n)=>{n.d(t,{x:()=>r});function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},21231:(e,t,n)=>{n.d(t,{s:()=>o});var r=n(12115),i=n(87657);let o=(e,t)=>{i._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},27279:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t){for(var n=arguments.length,i=Array(n>2?n-2:0),o=2;o<n;o++)i[o-2]=arguments[o];if(e in t){let n=t[e];return"function"==typeof n?n(...i):n}let s=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(s,r),s}},27351:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(6983);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},30797:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(12115),i=n(6232);let o=function(e){let t=(0,i.Y)(e);return r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])}},32082:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(12115),i=n(80845);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},33109:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},34835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},39611:(e,t,n)=>{e.exports=n(4993)},39688:(e,t,n)=>{n.d(t,{QP:()=>eu});let r=e=>{let t=a(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,t)||s(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?i(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},o=/^\[(.+)\]$/,s=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},a=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)l(n[e],r,e,t);return r},l=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e)return c(e)?void l(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,i=0,o=0;for(let s=0;s<e.length;s++){let a=e[s];if(0===r&&0===i){if(":"===a){n.push(e.slice(o,s)),o=s+1;continue}if("/"===a){t=s;continue}}"["===a?r++:"]"===a?r--:"("===a?i++:")"===a&&i--}let s=0===n.length?e:e.substring(o),a=p(s);return{modifiers:n,hasImportantModifier:a!==s,baseClassName:a,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},f=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:m(e),...r(e)}),g=/\s+/,v=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,s=[],a=e.trim().split(g),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=n(t);if(u){l=t+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?h.substring(0,p):h);if(!f){if(!m||!(f=r(h))){l=t+(l.length>0?" "+l:l);continue}m=!1}let g=o(c).join(":"),v=d?g+"!":g,y=v+f;if(s.includes(y))continue;s.push(y);let b=i(f,m);for(let e=0;e<b.length;++e){let t=b[e];s.push(v+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>E.test(e),D=e=>!!e&&!Number.isNaN(Number(e)),F=e=>!!e&&Number.isInteger(Number(e)),R=e=>e.endsWith("%")&&D(e.slice(0,-1)),V=e=>P.test(e),j=()=>!0,L=e=>T.test(e)&&!S.test(e),O=()=>!1,I=e=>A.test(e),N=e=>M.test(e),B=e=>!z(e)&&!X(e),U=e=>ee(e,ei,O),z=e=>w.test(e),_=e=>ee(e,eo,L),W=e=>ee(e,es,D),$=e=>ee(e,en,O),Y=e=>ee(e,er,N),H=e=>ee(e,el,I),X=e=>k.test(e),K=e=>et(e,eo),q=e=>et(e,ea),G=e=>et(e,en),Z=e=>et(e,ei),Q=e=>et(e,er),J=e=>et(e,el,!0),ee=(e,t,n)=>{let r=w.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},et=(e,t,n=!1)=>{let r=k.exec(e);return!!r&&(r[1]?t(r[1]):n)},en=e=>"position"===e||"percentage"===e,er=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,eo=e=>"length"===e,es=e=>"number"===e,ea=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let n,r,i,o=function(a){return r=(n=f(t.reduce((e,t)=>t(e),e()))).cache.get,i=n.cache.set,o=s,s(a)};function s(e){let t=r(e);if(t)return t;let o=v(e,n);return i(e,o),o}return function(){return o(y.apply(null,arguments))}}(()=>{let e=x("color"),t=x("font"),n=x("text"),r=x("font-weight"),i=x("tracking"),o=x("leading"),s=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),c=x("shadow"),d=x("inset-shadow"),h=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),v=x("ease"),y=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),X,z],E=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],T=()=>[X,z,l],S=()=>[C,"full","auto",...T()],A=()=>[F,"none","subgrid",X,z],M=()=>["auto",{span:["full",F,X,z]},F,X,z],L=()=>[F,"auto",X,z],O=()=>["auto","min","max","fr",X,z],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...T()],et=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],en=()=>[e,X,z],er=()=>[...w(),G,$,{position:[X,z]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",Z,U,{size:[X,z]}],es=()=>[R,K,_],ea=()=>["","none","full",u,X,z],el=()=>["",D,K,_],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[D,R,G,$],eh=()=>["","none",m,X,z],ep=()=>["none",D,X,z],em=()=>["none",D,X,z],ef=()=>[D,X,z],eg=()=>[C,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[V],breakpoint:[V],color:[j],container:[V],"drop-shadow":[V],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[V],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[V],shadow:[V],spacing:["px",D],text:[V],"text-shadow":[V],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,z,X,g]}],container:["container"],columns:[{columns:[D,z,X,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[F,"auto",X,z]}],basis:[{basis:[C,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[D,C,"auto","initial","none",z]}],grow:[{grow:["",D,X,z]}],shrink:[{shrink:["",D,X,z]}],order:[{order:[F,"first","last","none",X,z]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,K,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,X,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,z]}],"font-family":[{font:[q,z,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,X,z]}],"line-clamp":[{"line-clamp":[D,"none",X,W]}],leading:[{leading:[o,...T()]}],"list-image":[{"list-image":["none",X,z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[D,"from-font","auto",X,_]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[D,"auto",X,z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},F,X,z],radial:["",X,z],conic:[F,X,z]},Q,Y]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[D,X,z]}],"outline-w":[{outline:["",D,K,_]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,J,H]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",d,J,H]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[D,_]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",h,J,H]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[D,X,z]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[D]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[X,z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[D]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,z]}],filter:[{filter:["","none",X,z]}],blur:[{blur:eh()}],brightness:[{brightness:[D,X,z]}],contrast:[{contrast:[D,X,z]}],"drop-shadow":[{"drop-shadow":["","none",p,J,H]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",D,X,z]}],"hue-rotate":[{"hue-rotate":[D,X,z]}],invert:[{invert:["",D,X,z]}],saturate:[{saturate:[D,X,z]}],sepia:[{sepia:["",D,X,z]}],"backdrop-filter":[{"backdrop-filter":["","none",X,z]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[D,X,z]}],"backdrop-contrast":[{"backdrop-contrast":[D,X,z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",D,X,z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[D,X,z]}],"backdrop-invert":[{"backdrop-invert":["",D,X,z]}],"backdrop-opacity":[{"backdrop-opacity":[D,X,z]}],"backdrop-saturate":[{"backdrop-saturate":[D,X,z]}],"backdrop-sepia":[{"backdrop-sepia":["",D,X,z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[D,"initial",X,z]}],ease:[{ease:["linear","initial",v,X,z]}],delay:[{delay:[D,X,z]}],animate:[{animate:["none",y,X,z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,X,z]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[X,z,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,z]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[D,K,_,W]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},43332:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},45261:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,i)=>(e.addEventListener(t,r,i),n.add(()=>e.removeEventListener(t,r,i))),requestAnimationFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let i=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(i))},nextFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let i=setTimeout(...t);return n.add(()=>clearTimeout(i))},microTask(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];let o={current:!0};return(0,r._)(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(7856)},46308:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("receipt",[["path",{d:"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z",key:"q3az6g"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 17.5v-11",key:"1jc1ny"}]])},47769:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>s});var r=n(12115),i=n(30797);let o=Symbol();function s(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[o]:t})}function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let s=(0,r.useRef)(t);(0,r.useEffect)(()=>{s.current=t},[t]);let a=(0,i._)(e=>{for(let t of s.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[o]))?void 0:a}},48014:(e,t,n)=>{n.d(t,{L:()=>o});var r=n(12115),i=n(45261);function o(){let[e]=(0,r.useState)(i.e);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},51508:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(12115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},52596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},60760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(95155),i=n(12115),o=n(90869),s=n(82885),a=n(97494),l=n(80845),u=n(27351),c=n(51508);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:o,root:s}=e,a=(0,i.useId)(),l=(0,i.useRef)(null),u=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:c}=u.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=a;let d=document.createElement("style");h&&(d.nonce=h);let p=null!=s?s:document.head;return p.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(d),p.contains(d)&&p.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:l,sizeRef:u,children:i.cloneElement(t,{ref:l})})}let p=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:p,root:f}=e,g=(0,s.M)(m),v=(0,i.useId)(),y=!0,b=(0,i.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;a&&a()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[o,g,a]);return c&&y&&(b={...b}),(0,i.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[o]),i.useEffect(()=>{o||g.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(h,{isPresent:o,anchorX:p,root:f,children:t})),(0,r.jsx)(l.t.Provider,{value:b,children:t})};function m(){return new Map}var f=n(32082);let g=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:m="left",root:y}=e,[b,x]=(0,f.xQ)(h),w=(0,i.useMemo)(()=>v(t),[t]),k=h&&!b?[]:w.map(g),E=(0,i.useRef)(!0),P=(0,i.useRef)(w),T=(0,s.M)(()=>new Map),[S,A]=(0,i.useState)(w),[M,C]=(0,i.useState)(w);(0,a.E)(()=>{E.current=!1,P.current=w;for(let e=0;e<M.length;e++){let t=g(M[e]);k.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[M,k.length,k.join("-")]);let D=[];if(w!==S){let e=[...w];for(let t=0;t<M.length;t++){let n=M[t],r=g(n);k.includes(r)||(e.splice(t,0,n),D.push(n))}return"wait"===d&&D.length&&(e=D),C(v(e)),A(w),null}let{forceRender:F}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:M.map(e=>{let t=g(e),i=(!h||!!b)&&(w===M||k.includes(t));return(0,r.jsx)(p,{isPresent:i,initial:(!E.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:y,onExitComplete:i?void 0:()=>{if(!T.has(t))return;T.set(t,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(null==F||F(),C(P.current),h&&(null==x||x()),u&&u())},anchorX:m,children:e},t)})})}},68972:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},72713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},73783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},74783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},76356:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]])},76408:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function a(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oA});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=s,y=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),p.process(i),m.process(i),f.process(i),g.process(i),v.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},b=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:d.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:m,cancel:f,state:g,steps:v}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(y),x=new Set(["width","height","top","left","right","bottom",...y]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function k(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class E{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>k(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){r=void 0}let T={now:()=>(void 0===r&&T.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(P)}},S=e=>!isNaN(parseFloat(e)),A={current:void 0};class M{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=T.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=T.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new E);let n=this.events[e].add(t);return"change"===e?()=>{n(),m.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=T.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(e,t){return new M(e,t)}let D=e=>Array.isArray(e),F=e=>!!(e&&e.getVelocity);function R(e,t){let n=e.getValue("willChange");if(F(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let V=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),j="data-"+V("framerAppearId"),L=(e,t)=>n=>t(e(n)),O=(...e)=>e.reduce(L),I=(e,t,n)=>n>t?t:n<e?e:n,N=e=>1e3*e,B=e=>e/1e3,U={layout:0,mainThread:0,waapi:0},z=()=>{},_=()=>{},W=e=>t=>"string"==typeof t&&t.startsWith(e),$=W("--"),Y=W("var(--"),H=e=>!!Y(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},q={...K,transform:e=>I(0,1,e)},G={...K,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},en=e=>I(0,255,e),er={...K,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(q.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ea=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(q.transform(r))+")"},em={test:e=>ei.test(e)||eo.test(e)||ep.test(e),parse:e=>ei.test(e)?ei.parse(e):ep.test(e)?ep.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ep.transform(e),getAnimatableNone:e=>{let t=em.parse(e);return t.alpha=0,em.transform(t)}},ef=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ev="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(ey,e=>(em.test(e)?(r.color.push(o),i.push(ev),n.push(em.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eg),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function ex(e){return eb(e).values}function ew(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eg?i+=Z(e[o]):t===ev?i+=em.transform(e[o]):i+=e[o]}return i}}let ek=e=>"number"==typeof e?0:em.test(e)?em.getAnimatableNone(e):e,eE={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(ef)?.length||0)>0},parse:ex,createTransformer:ew,getAnimatableNone:function(e){let t=ex(e);return ew(e)(t.map(ek))}};function eP(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eT(e,t){return n=>n>0?t:e}let eS=(e,t,n)=>e+(t-e)*n,eA=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eM=[eo,ei,ep],eC=e=>eM.find(t=>t.test(e));function eD(e){let t=eC(e);if(z(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ep&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=eP(a,r,e+1/3),o=eP(a,r,e),s=eP(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let eF=(e,t)=>{let n=eD(e),r=eD(t);if(!n||!r)return eT(e,t);let i={...n};return e=>(i.red=eA(n.red,r.red,e),i.green=eA(n.green,r.green,e),i.blue=eA(n.blue,r.blue,e),i.alpha=eS(n.alpha,r.alpha,e),ei.transform(i))},eR=new Set(["none","hidden"]);function eV(e,t){return n=>eS(e,t,n)}function ej(e){return"number"==typeof e?eV:"string"==typeof e?H(e)?eT:em.test(e)?eF:eI:Array.isArray(e)?eL:"object"==typeof e?em.test(e)?eF:eO:eT}function eL(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>ej(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eO(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=ej(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eI=(e,t)=>{let n=eE.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eR.has(e)&&!i.values.length||eR.has(t)&&!r.values.length?function(e,t){return eR.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):O(eL(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(z(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eT(e,t))};function eN(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eS(e,t,n):ej(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>m.update(t,e),stop:()=>f(t),now:()=>g.isProcessing?g.timestamp:T.now()}},eU=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function ez(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function e_(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let eY=["duration","bounce"],eH=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eK(e=eW.visualDuration,t=eW.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eH)&&eX(e,eY))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eW.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eW.duration,bounce:t=eW.bounce,velocity:n=eW.velocity,mass:r=eW.mass}){let i,o;z(e<=N(eW.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=I(eW.minDamping,eW.maxDamping,s),e=I(eW.minDuration,eW.maxDuration,B(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/e$(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=e$(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=N(e),isNaN(a))return{stiffness:eW.stiffness,damping:eW.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eW.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-B(r.velocity||0)}),f=p||0,g=c/(2*Math.sqrt(u*d)),v=a-s,y=B(Math.sqrt(u/d)),b=5>Math.abs(v);if(i||(i=b?eW.restSpeed.granular:eW.restSpeed.default),o||(o=b?eW.restDelta.granular:eW.restDelta.default),g<1){let e=e$(y,g);n=t=>a-Math.exp(-g*y*t)*((f+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-y*e)*(v+(f+y*v)*e);else{let e=y*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*y*t),r=Math.min(e*t,300);return a-n*((f+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let x={calculatedDuration:m&&h||null,next:e=>{let t=n(e);if(m)l.done=e>=h;else{let r=0===e?f:0;g<1&&(r=0===e?N(f):e_(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(ez(x),2e4),t=eU(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function eq({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,v=n*t,y=p+v,b=void 0===s?y:s(y);b!==y&&(v=b-p);let x=e=>-v*Math.exp(-e/r),w=e=>b+x(e),k=e=>{let t=x(e),n=w(e);m.done=Math.abs(t)<=u,m.value=m.done?b:n},E=e=>{f(m.value)&&(d=e,h=eK({keyframes:[m.value,g(m.value)],velocity:e_(w,e,m.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,k(e),E(e)),void 0!==d&&e>=d)?h.next(e-d):(t||k(e),m)}}}eK.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(ez(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:B(i)}}(e,100,eK);return e.ease=t.ease,e.duration=N(t.duration),e.type="keyframes",e};let eG=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,s,a=0;do(o=eG(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eG(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e4=eZ(.33,1.53,.69,.99),e3=e5(e4),e9=e2(e3),e6=e=>(e*=2)<1?.5*e3(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e8=e5(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e8,backIn:e3,backInOut:e9,backOut:e4,anticipate:e6},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){_(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(_(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(_(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||c.mix||eN,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=O(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=a.length,d=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return a[r](i)};return n?t=>d(I(e[0],e[o-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eS(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let ta=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ta),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let tu={decay:eq,inertia:eq,tween:ts,keyframes:ts,spring:eK};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tp extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==T.now()&&this.tick(T.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||ts;a!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=O(th,eN(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=ez(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(b=o)),y=I(0,1,n)*s}let x=v?{done:!1,value:u[0]}:b.next(y);i&&(x.value=i(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==eq&&(x.value=tl(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=N(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(T.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(T.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tm=e=>180*e/Math.PI,tf=e=>tv(tm(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tf,rotateZ:tf,skewX:e=>tm(Math.atan(e[1])),skewY:e=>tm(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tv=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tx={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tb,scale:e=>(ty(e)+tb(e))/2,rotateX:e=>tv(tm(Math.atan2(e[6],e[5]))),rotateY:e=>tv(tm(Math.atan2(-e[2],e[0]))),rotateZ:tf,rotate:tf,skewX:e=>tm(Math.atan(e[4])),skewY:e=>tm(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tk(e,t){let n,r;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tx,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tw(t);let o=n[t],s=r[1].split(",").map(tP);return"function"==typeof o?o(s):s[o]}let tE=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tk(n,t)};function tP(e){return parseFloat(e.trim())}let tT=e=>e===K||e===eu,tS=new Set(["x","y","z"]),tA=y.filter(e=>!tS.has(e)),tM={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tk(t,"x"),y:(e,{transform:t})=>tk(t,"y")};tM.translateX=tM.x,tM.translateY=tM.y;let tC=new Set,tD=!1,tF=!1,tR=!1;function tV(){if(tF){let e=Array.from(tC).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tF=!1,tD=!1,tC.forEach(e=>e.complete(tR)),tC.clear()}function tj(){tC.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tF=!0)})}class tL{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tC.add(this),tD||(tD=!0,m.read(tj),m.resolveKeyframes(tV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tC.delete(this)}cancel(){"scheduled"===this.state&&(tC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tO=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tN=tI(()=>void 0!==window.ScrollTimeline),tB={},tU=function(e,t){let n=tI(e);return()=>tB[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tz=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,t_={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tz([0,.65,.55,1]),circOut:tz([.55,0,1,.45]),backIn:tz([.31,.01,.66,-.59]),backOut:tz([.33,1.53,.69,.99])};function tW(e){return"function"==typeof e&&"applyToOptions"in e}class t$ extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,_("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tW(e)&&tU()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?tU()?eU(t,n):"ease-out":tt(t)?tz(t):Array.isArray(t)?t.map(t=>e(t,n)||t_.easeOut):t_[t]}(a,i);Array.isArray(d)&&(c.easing=d),h.value&&U.waapi++;let p={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(p.pseudoElement=u);let m=e.animate(c,p);return h.value&&m.finished.finally(()=>{U.waapi--}),m}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tO(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=N(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tN())?(this.animation.timeline=e,u):t(this)}}let tY={anticipate:e6,backInOut:e9,circInOut:te};class tH extends t${constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tY&&(e.ease=tY[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tp({...o,autoplay:!1}),a=N(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eE.test(e)||"0"===e)&&!e.startsWith("url("));var tK,tq,tG=n(27351);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=T.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tL;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:d}=n;this.resolvedAt=T.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tX(i,t),a=tX(o,t);return z(s===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tW(n))&&r)}(e,i,o,s)&&((c.instantAnimations||!a)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},p=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!(0,tG.s)(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(h)?new tH({...h,element:h.motionValue.owner.current}):new tp(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tR=!0,tj(),tV(),tR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t3=(e,{keyframes:t})=>t.length>2?t5:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t4,t9=(e,t,n,r={},i,o)=>s=>{let a=l(r,e)||{},u=a.delay||r.delay||0,{elapsed:d=0}=r;d-=N(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-d,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&Object.assign(h,t3(e,h)),h.duration&&(h.duration=N(h.duration)),h.repeatDelay&&(h.repeatDelay=N(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,p&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,a);if(void 0!==e)return void m.update(()=>{h.onUpdate(e),h.onComplete()})}return a.isSync?new tp(h):new tJ(h)};function t6(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(o=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let s={delay:n,...l(o||{},t)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(i)&&i===a&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[j];if(n){let e=window.MotionHandoffAnimation(n,t,m);null!==e&&(s.startTime=e,h=!0)}}R(e,t),r.start(t9(t,r,i,e.shouldReduceMotion&&x.has(t)?{type:!1}:s,e,h));let p=r.animation;p&&c.push(p)}return s&&Promise.all(c).then(()=>{m.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=a(e,t)||{};for(let t in i={...i,...n}){var o;let n=D(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,C(n))}}(e,s)})}),c}function t7(e,t,n={}){let r=a(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t6(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=0,o=1,s){let a=[],l=e.variantChildren.size,u=(l-1)*i,c="function"==typeof r,d=c?e=>r(e,l):1===o?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(t8).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(t7(e,t,{...s,delay:n+(c?0:r)+d(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,r,o,s,a,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,no=[...nn].reverse(),ns=nn.length;function na(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:na(!0),whileInView:na(),whileHover:na(),whileTap:na(),whileDrag:na(),whileFocus:na(),exit:na()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t7(e,t,n)));else if("string"==typeof t)r=t7(e,t,n);else{let i="function"==typeof t?a(e,t,n.custom):t;r=Promise.all(t6(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,o=t=>(n,r)=>{let i=a(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},p=1/0;for(let t=0;t<ns;t++){var m,f;let a=no[t],g=n[a],v=void 0!==l[a]?l[a]:u[a],y=nt(v),b=a===s?g.isActive:null;!1===b&&(p=t);let x=v===u[a]&&v!==l[a]&&y;if(x&&r&&e.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...h},!g.isActive&&null===b||!v&&!g.prevProp||i(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!ne(f,m)),k=w||a===s&&g.isActive&&!x&&y||t>p&&y,E=!1,P=Array.isArray(v)?v:[v],T=P.reduce(o(a),{});!1===b&&(T={});let{prevResolvedValues:S={}}=g,A={...S,...T},M=t=>{k=!0,d.has(t)&&(E=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in A){let t=T[e],n=S[e];if(h.hasOwnProperty(e))continue;let r=!1;(D(t)&&D(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?M(e):g.protectedKeys[e]=!0:null!=t?M(e):d.add(e)}g.prevProp=v,g.prevResolvedValues=T,g.isActive&&(h={...h,...T}),r&&e.blockInitialAnimation&&(k=!1);let C=!(x&&w)||E;k&&C&&c.push(...P.map(e=>({animation:e,options:{type:a}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let np={x:!1,y:!1};function nm(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nf=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let nv=e=>t=>nf(t)&&e(t,ng(t));function ny(e,t,n,r){return nm(e,t,nv(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nx(e){return e.max-e.min}function nw(e,t,n,r=.5){e.origin=r,e.originPoint=eS(t.min,t.max,e.origin),e.scale=nx(n)/nx(t),e.translate=eS(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nk(e,t,n,r){nw(e.x,t.x,n.x,r?r.originX:void 0),nw(e.y,t.y,n.y,r?r.originY:void 0)}function nE(e,t,n){e.min=n.min+t.min,e.max=e.min+nx(t)}function nP(e,t,n){e.min=t.min-n.min,e.max=e.min+nx(t)}function nT(e,t,n){nP(e.x,t.x,n.x),nP(e.y,t.y,n.y)}let nS=()=>({translate:0,scale:1,origin:0,originPoint:0}),nA=()=>({x:nS(),y:nS()}),nM=()=>({min:0,max:0}),nC=()=>({x:nM(),y:nM()});function nD(e){return[e("x"),e("y")]}function nF(e){return void 0===e||1===e}function nR({scale:e,scaleX:t,scaleY:n}){return!nF(e)||!nF(t)||!nF(n)}function nV(e){return nR(e)||nj(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nj(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nL(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nO(e,t=0,n=1,r,i){e.min=nL(e.min,t,n,r,i),e.max=nL(e.max,t,n,r,i)}function nI(e,{x:t,y:n}){nO(e.x,t.translate,t.scale,t.originPoint),nO(e.y,n.translate,n.scale,n.originPoint)}function nN(e,t){e.min=e.min+t,e.max=e.max+t}function nB(e,t,n,r,i=.5){let o=eS(e.min,e.max,i);nO(e,t,n,o,r)}function nU(e,t){nB(e.x,t.x,t.scaleX,t.scale,t.originX),nB(e.y,t.y,t.scaleY,t.scale,t.originY)}function nz(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let n_=({current:e})=>e?e.ownerDocument.defaultView:null;function nW(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let n$=(e,t)=>Math.abs(e-t);class nY{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nK(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(n$(e.x,t.x)**2+n$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nH(t,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nK("pointercancel"===e.type?this.lastMoveEventInfo:nH(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nf(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let s=nH(ng(e),this.transformPagePoint),{point:a}=s,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,nK(s,this.history)),this.removeListeners=O(ny(this.contextWindow,"pointermove",this.handlePointerMove),ny(this.contextWindow,"pointerup",this.handlePointerUp),ny(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function nH(e,t){return t?{point:t(e.point)}:e}function nX(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nK({point:e},t){return{point:e,delta:nX(e,nq(t)),offset:nX(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nq(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>N(.1)));)n--;if(!r)return{x:0,y:0};let o=B(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nq(e){return e[e.length-1]}function nG(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nC(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new nY(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(np[e])return null;else return np[e]=!0,()=>{np[e]=!1};return np.x||np.y?null:(np.x=np.y=!0,()=>{np.x=np.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nD(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nx(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&m.postRender(()=>i(e,t)),R(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>nD(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:n_(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&m.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eS(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eS(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nW(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nG(e.x,n,i),y:nG(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nD(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nW(t))return!1;let r=t.current;_(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nz(e,n),{scroll:i}=t;return i&&(nN(r.x,i.offset.x),nN(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nZ(e.x,o.x),y:nZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nb(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nD(s=>{if(!n2(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return R(this.visualElement,e),n.start(t9(e,n,0,t,this.visualElement,!1))}stopAnimation(){nD(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nD(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nD(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eS(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nW(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nD(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nx(e),i=nx(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),I(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nD(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eS(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=ny(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nW(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),m.read(t);let i=nm(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nD(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n5 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n4=e=>(t,n)=>{e&&m.postRender(()=>e(t,n))};class n3 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nY(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n_(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n4(e),onStart:n4(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&m.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ny(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n9=n(95155);let{schedule:n6}=p(queueMicrotask,!1);var n7=n(12115),n8=n(32082),re=n(90869);let rt=(0,n7.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ro={},rs=!1;class ra extends n7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in ru)ro[e]=ru[e],$(e)&&(ro[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),rs&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,rs=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||m.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rl(e){let[t,n]=(0,n8.xQ)(),r=(0,n7.useContext)(re.L);return(0,n9.jsx)(ra,{...e,layoutGroup:r,switchLayoutGroup:(0,n7.useContext)(rt),isPresent:t,safeToRemove:n})}let ru={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eE.parse(e);if(r.length>5)return e;let i=eE.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=eS(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var rc=n(6983);function rd(e){return(0,rc.G)(e)&&"ownerSVGElement"in e}let rh=(e,t)=>e.depth-t.depth;class rp{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){k(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rh),this.isDirty=!1,this.children.forEach(e)}}function rm(e){return F(e)?e.get():e}let rf=["TopLeft","TopRight","BottomLeft","BottomRight"],rg=rf.length,rv=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rx=rk(0,.5,e8),rw=rk(.5,.95,u);function rk(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rE(e,t){e.min=t.min,e.max=t.max}function rP(e,t){rE(e.x,t.x),rE(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rS(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rA(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eS(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=eS(o.min,o.max,r);e===o&&(a-=t),e.min=rS(e.min,t,n,a,i),e.max=rS(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let rM=["x","scaleX","originX"],rC=["y","scaleY","originY"];function rD(e,t,n,r){rA(e.x,t,rM,n?n.x:void 0,r?r.x:void 0),rA(e.y,t,rC,n?n.y:void 0,r?r.y:void 0)}function rF(e){return 0===e.translate&&1===e.scale}function rR(e){return rF(e.x)&&rF(e.y)}function rV(e,t){return e.min===t.min&&e.max===t.max}function rj(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rL(e,t){return rj(e.x,t.x)&&rj(e.y,t.y)}function rO(e){return nx(e.x)/nx(e.y)}function rI(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rN{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(k(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rB={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rU=["","X","Y","Z"],rz=0;function r_(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rW({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rz++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rB.nodes=rB.calculatedTargetDeltas=rB.calculatedProjections=0),this.nodes.forEach(rH),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rX),h.addProjectionMetrics&&h.addProjectionMetrics(rB)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rp)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new E),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rd(t)&&!(rd(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;m.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=T.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(f(r),e(o-t))};return m.setup(r,!0),()=>f(r)}(i,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r9,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),u=!this.targetLayout||!rL(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[j];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",m,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rq);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rG);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(r$),this.nodes.forEach(rY)):this.nodes.forEach(rG),this.clearAllSnapshots();let e=T.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rK),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nx(this.snapshot.measuredBox.x)||nx(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rR(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nV(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nC();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nN(t.x,e.offset.x),nN(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nC();if(rP(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rP(t,e),nN(t.x,i.offset.x),nN(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nC();rP(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nU(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nV(r.latestValues)&&nU(n,r.latestValues)}return nV(this.latestValues)&&nU(n,this.latestValues),n}removeTransform(e){let t=nC();rP(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nV(n.latestValues))continue;nR(n.latestValues)&&n.updateSnapshot();let r=nC();rP(r,n.measurePageBox()),rD(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nV(this.latestValues)&&rD(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nT(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nC(),this.targetWithTransforms=nC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,nE(o.x,s.x,a.x),nE(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rP(this.target,this.layout.layoutBox),nI(this.target,this.targetDelta)):rP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nT(this.relativeTargetOrigin,this.target,e.target),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rB.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nR(this.parent.latestValues)||nj(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rP(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nU(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nI(e,o)),r&&nV(i.latestValues)&&nU(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nC());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nk(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&rI(this.projectionDelta.x,this.prevProjectionDelta.x)&&rI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),h.value&&rB.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nA(),this.projectionDelta=nA(),this.projectionDeltaWithTransform=nA()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=nA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nC(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r3));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(s.x,e.x,r),r5(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;nT(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,r4(p.x,m.x,f.x,g),r4(p.y,m.y,f.y,g),n&&(u=this.relativeTarget,h=n,rV(u.x,h.x)&&rV(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nC()),rP(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eS(0,n.opacity??1,rx(r)),e.opacityExit=eS(t.opacity??1,0,rw(r))):o&&(e.opacity=eS(t.opacity??1,n.opacity??1,r));for(let i=0;i<rg;i++){let o=`border${rf[i]}Radius`,s=rb(t,o),a=rb(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||ry(s)===ry(a)?(e[o]=Math.max(eS(rv(s),rv(a),r),0),(el.test(a)||el.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=eS(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{rn.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(e,t,n){let r=F(e)?e:C(e);return r.start(t9("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nC();let t=nx(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nx(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rP(t,n),nU(t,i),nk(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rN),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&r_("z",e,r,this.animationValues);for(let t=0;t<rU.length;t++)r_(`rotate${rU[t]}`,e,r,this.animationValues),r_(`skew${rU[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=rm(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rm(t?.pointerEvents)||""),this.hasProjected&&!nV(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),e.transform=o;let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ro){if(void 0===i[t])continue;let{correct:n,applyTo:s,isCSSVariable:a}=ro[t],l="none"===o?i[t]:n(i[t],r);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?rm(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rq),this.root.sharedNodes.clear()}}}function r$(e){e.updateLayout()}function rY(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?nD(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nx(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nD(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=nx(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nA();nk(s,n,t.layoutBox);let a=nA();o?nk(a,e.applyTransform(r,!0),t.measuredBox):nk(a,n,t.layoutBox);let l=!rR(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=nC();nT(s,t.layoutBox,i.layoutBox);let a=nC();nT(a,n,o.layoutBox),rL(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rH(e){h.value&&rB.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rX(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rK(e){e.clearSnapshot()}function rq(e){e.clearMeasurements()}function rG(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r5(e,t,n){e.translate=eS(t.translate,0,n),e.scale=eS(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r4(e,t,n,r){e.min=eS(t.min,n.min,r),e.max=eS(t.max,n.max,r)}function r3(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r9={duration:.45,ease:[.4,0,.1,1]},r6=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r7=r6("applewebkit/")&&!r6("chrome/")?Math.round:u;function r8(e){e.min=r7(e.min),e.max=r7(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rO(t)-rO(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rW({attachResizeListener:(e,t)=>nm(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=rW({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function ia(e){return!("touch"===e.pointerType||np.x||np.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&m.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{if(!ia(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{ia(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(nm(this.node.current,"focus",()=>this.onFocus()),nm(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iv=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iy(e){return nf(e)&&!(np.x||np.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&m.postRender(()=>i(t,ng(t)))}class ix extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{let r=e.currentTarget;if(!iy(e))return;ip.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iy(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tG.s)(e))&&(e.addEventListener("focus",e=>iv(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ik=new WeakMap,iE=e=>{let t=iw.get(e.target);t&&t(e)},iP=e=>{e.forEach(iE)},iT={some:0,all:1};class iS extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iT[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ik.has(n)||ik.set(n,{});let r=ik.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iP,{root:e,...t})),r[i]}(t);return iw.set(e,n),r.observe(e),()=>{iw.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,n7.createContext)({strict:!1});var iM=n(51508);let iC=(0,n7.createContext)({});function iD(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iF(e){return!!(iD(e)||e.variants)}function iR(e){return Array.isArray(e)?e.join(" "):e}var iV=n(68972);let ij={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in ij)iL[e]={isEnabled:t=>ij[e].some(e=>!!t[e])};let iO=Symbol.for("motionComponentSymbol");var iI=n(80845),iN=n(97494);function iB(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ro[e]||"opacity"===e)}let iU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iz={...K,transform:Math.round},i_={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:ea,rotateX:ea,rotateY:ea,rotateZ:ea,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ea,skewX:ea,skewY:ea,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:q,originX:eh,originY:eh,originZ:eu,zIndex:iz,fillOpacity:q,strokeOpacity:q,numOctaves:iz},iW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i$=y.length;function iY(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(b.has(e)){s=!0;continue}if($(e)){i[e]=n;continue}{let t=iU(n,i_[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<i$;o++){let s=y[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=iU(a,i_[s]);if(!l){i=!1;let t=iW[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,n){for(let r in t)F(t[r])||iB(r,n)||(e[r]=t[r])}let iK={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iG(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(iY(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iK:iq;e[o.offset]=eu.transform(-r);let s=eu.transform(t),a=eu.transform(n);e[o.array]=`${s} ${a}`}(d,i,o,s,!1)}let iZ=()=>({...iH(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i4=n(82885);let i3=e=>(t,n)=>{let r=(0,n7.useContext)(iC),o=(0,n7.useContext)(iI.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},a=r(e,{});for(let e in a)o[e]=rm(a[e]);let{initial:l,animate:u}=e,c=iD(e),d=iF(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let t=Array.isArray(p)?p:[p];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?a():(0,i4.M)(a)};function i9(e,t,n){let{style:r}=e,i={};for(let o in r)(F(r[o])||t.style&&F(t.style[o])||iB(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i6={useVisualState:i3({scrapeMotionValuesFromProps:i9,createRenderState:iH})};function i7(e,t,n){let r=i9(e,t,n);for(let n in e)(F(e[n])||F(t[n]))&&(r[-1!==y.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8={useVisualState:i3({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[K,eu,el,ea,ed,ec,{test:e=>"auto"===e,parse:e=>e}],on=e=>ot.find(oe(e)),or=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),os=new Set(["brightness","contrast","saturate","opacity"]);function oa(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),o=+!!os.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eE,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(oa).join(" "):e}},oc={...i_,color:em,backgroundColor:em,outlineColor:em,fill:em,stroke:em,borderColor:em,borderTopColor:em,borderRightColor:em,borderBottomColor:em,borderLeftColor:em,filter:ou,WebkitFilter:ou},od=e=>oc[e];function oh(e,t){let n=od(e);return n!==ou&&(n=eE),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let op=new Set(["auto","none","0"]);class om extends tL{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&H(r=r.trim())){let i=function e(t,n,r=1){_(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return or(e)?parseFloat(e):e}return H(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!x.has(n)||2!==e.length)return;let[r,i]=e,o=on(r),s=on(i);if(o!==s)if(tT(o)&&tT(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tM[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||oo(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!op.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oh(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tM[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tM[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let of=[...ot,em,eE],og=e=>of.find(oe(e)),ov={current:null},oy={current:!1},ob=new WeakMap,ox=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ow{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=T.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iD(t),this.isVariantNode=iF(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&F(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ob.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oy.current||function(){if(oy.current=!0,iV.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ov.current=e.matches;e.addEventListener("change",t),t()}else ov.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ov.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&m.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nC()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ox.length;t++){let n=ox[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(F(i))e.addValue(r,i);else if(F(o))e.addValue(r,C(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,C(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=C(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(or(n)||oo(n))?n=parseFloat(n):!og(n)&&eE.test(t)&&(n=oh(e,t)),this.setBaseTarget(e,F(n)?n.get():n)),F(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||F(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new E),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ok extends ow{constructor(){super(...arguments),this.KeyframeResolver=om}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;F(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oE(e,{style:t,vars:n},r,i){let o,s=e.style;for(o in t)s[o]=t[o];for(o in i?.applyProjectionStyles(s,r),n)s.setProperty(o,n[o])}class oP extends ok{constructor(){super(...arguments),this.type="html",this.renderInstance=oE}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tw(t):tE(e,t);{let n=window.getComputedStyle(e),r=($(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nz(e,t)}build(e,t,n){iY(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i9(e,t,n)}}let oT=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oS extends ok{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nC}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=od(t);return e&&e.default||0}return t=oT.has(t)?t:V(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i7(e,t,n)}build(e,t,n){iG(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in oE(e,t,void 0,r),t.attrs)e.setAttribute(oT.has(n)?n:V(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let oA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tK={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iS},tap:{Feature:ix},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n3},drag:{Feature:n5,ProjectionNode:io,MeasureLayout:rl},layout:{ProjectionNode:io,MeasureLayout:rl}},tq=(e,t)=>i5(e)?new oS(t):new oP(t,{allowProjection:e!==n7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:r,createVisualElement:i,useRender:o,useVisualState:s,Component:a}=e;function l(e,t){var n,r,l;let u,c={...(0,n7.useContext)(iM.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,n7.useContext)(re.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(iD(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n7.useContext)(iC));return(0,n7.useMemo)(()=>({initial:t,animate:n}),[iR(t),iR(n)])}(e),p=s(e,d);if(!d&&iV.B){r=0,l=0,(0,n7.useContext)(iA).strict;let e=function(e){let{drag:t,layout:n}=iL;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n7.useContext)(iC),s=(0,n7.useContext)(iA),a=(0,n7.useContext)(iI.t),l=(0,n7.useContext)(iM.Q).reducedMotion,u=(0,n7.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n7.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&nW(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n7.useRef)(!1);(0,n7.useInsertionEffect)(()=>{c&&h.current&&c.update(n,a)});let p=n[j],m=(0,n7.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,iN.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n6.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,n7.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(a,p,c,i,e.ProjectionNode)}return(0,n9.jsxs)(iC.Provider,{value:h,children:[u&&h.visualElement?(0,n9.jsx)(u,{visualElement:h.visualElement,...c}):null,o(a,e,(n=h.visualElement,(0,n7.useCallback)(e=>{e&&p.onMount&&p.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):nW(t)&&(t.current=e))},[n])),p,d,h.visualElement)]})}r&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(n=null!=(t=a.displayName)?t:a.name)?n:"",")"));let u=(0,n7.forwardRef)(l);return u[iO]=a,u}({...i5(e)?i8:i6,preloadedFeatures:tK,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(i5(t)?function(e,t,n,r){let i=(0,n7.useMemo)(()=>{let n=iZ();return iG(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n7.useMemo)(()=>{let n=iH();return iY(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n7.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,n7.useMemo)(()=>F(u)?u.get():u,[u]);return(0,n7.createElement)(t,{...l,children:c})}}(t),createVisualElement:tq,Component:e})}))},80280:(e,t,n)=>{n.d(t,{lG:()=>eJ});var r,i,o,s=n(12115),a=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(a||{}),l=n(6232);function u(e,t,n,r){let i=(0,l.Y)(n);(0,s.useEffect)(()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class c extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}var d=n(45261),h=Object.defineProperty,p=(e,t,n)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t,n)=>(p(e,"symbol"!=typeof t?t+"":t,n),n),f=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},g=(e,t,n)=>(f(e,t,"read from private field"),n?n.call(e):t.get(e)),v=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},y=(e,t,n,r)=>(f(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class b{dispose(){this.disposables.dispose()}get state(){return g(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(g(this,r))};return g(this,o).add(n),this.disposables.add(()=>{g(this,o).delete(n)})}on(e,t){return g(this,i).get(e).add(t),this.disposables.add(()=>{g(this,i).get(e).delete(t)})}send(e){let t=this.reduce(g(this,r),e);if(t!==g(this,r)){for(let e of(y(this,r,t),g(this,o))){let t=e.selector(g(this,r));x(e.current,t)||(e.current=t,e.callback(t))}for(let t of g(this,i).get(e.type))t(g(this,r),e)}}constructor(e){v(this,r,{}),v(this,i,new c(()=>new Set)),v(this,o,new Set),m(this,"disposables",(0,d.e)()),y(this,r,e)}}function x(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&w(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&w(e.entries(),t.entries()):!!(k(e)&&k(t))&&w(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function w(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function k(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}r=new WeakMap,i=new WeakMap,o=new WeakMap;var E=n(27279),P=Object.defineProperty,T=(e,t,n)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,S=(e,t,n)=>(T(e,"symbol"!=typeof t?t+"":t,n),n),A=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(A||{});let M={0(e,t){let n=t.id,r=e.stack,i=e.stack.indexOf(n);if(-1!==i){let t=e.stack.slice();return t.splice(i,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let i=e.stack.slice();return i.splice(r,1),{...e,stack:i}}};class C extends b{static new(){return new C({stack:[]})}reduce(e,t){return(0,E.Y)(t.type,M,e,t)}constructor(){super(...arguments),S(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),S(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}}let D=new c(()=>C.new());var F=n(39611),R=n(30797);function V(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:x;return(0,F.useSyncExternalStoreWithSelector)((0,R._)(t=>e.subscribe(j,t)),(0,R._)(()=>e.state),(0,R._)(()=>e.state),(0,R._)(t),n)}function j(e){return e}var L=n(21231);function O(e,t){let n=(0,s.useId)(),r=D.get(t),[i,o]=V(r,(0,s.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,L.s)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!o||i)}var I=n(87657);function N(e){var t,n;return I._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let B=new Map,U=new Map;function z(e){var t;let n=null!=(t=U.get(e))?t:0;return U.set(e,n+1),0!==n||(B.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=U.get(e))?t:1;if(1===n?U.delete(e):U.set(e,n-1),1!==n)return;let r=B.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,B.delete(e))})(e)}function _(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function W(e){return _(e)&&"tagName"in e}function $(e){return W(e)&&"accessKey"in e}function Y(e){return W(e)&&"tabIndex"in e}let H=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),X=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var K=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(K||{}),q=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(q||{}),G=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(G||{}),Z=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Z||{}),Q=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Q||{});function J(e){null==e||e.focus({preventScroll:!0})}function ee(e,t){var n,r,i;let{sorted:o=!0,relativeTo:s=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?o?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),i=t(n);if(null===r||null===i)return 0;let o=r.compareDocumentPosition(i);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(X)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(H)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);a.length>0&&u.length>1&&(u=u.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),s=null!=s?s:l.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(s))-1;if(4&t)return Math.max(0,u.indexOf(s))+1;if(8&t)return u.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),h=32&t?{preventScroll:!0}:{},p=0,m=u.length,f;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(f=u[e])||f.focus(h),p+=c}while(f!==l.activeElement);return 6&t&&null!=(i=null==(r=null==(n=f)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&i&&f.select(),2}function et(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function en(){return et()||/Android/gi.test(window.navigator.userAgent)}function er(e,t,n,r){let i=(0,l.Y)(n);(0,s.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){i.current(e)}},[e,t,r])}function ei(e,t,n,r){let i=(0,l.Y)(n);(0,s.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){i.current(e)}},[e,t,r])}function eo(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.useMemo)(()=>N(...t),[...t])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var es=n(84554),ea=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ea||{});let el=(0,es.FX)(function(e,t){var n;let{features:r=1,...i}=e,o={ref:t,"aria-hidden":(2&r)==2||(null!=(n=i["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,es.Ci)()({ourProps:o,theirProps:i,slot:{},defaultTag:"span",name:"Hidden"})}),eu=(0,s.createContext)(null);function ec(e){let{children:t,node:n}=e,[r,i]=(0,s.useState)(null),o=ed(null!=n?n:r);return s.createElement(eu.Provider,{value:o},t,null===o&&s.createElement(el,{features:ea.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=N(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&W(r)&&null!=r&&r.contains(e)){i(r);break}}}}))}function ed(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!=(e=(0,s.useContext)(eu))?e:t}let eh=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var i=arguments.length,o=Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];let a=t[e].call(n,...o);a&&(n=a,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,d.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:i}=e,o={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(i)},s=[et()?{before(e){let{doc:t,d:n,meta:r}=e;function i(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,d.e)();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,o=null;n.addEventListener(t,"click",e=>{if(Y(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),s=t.querySelector(r);Y(s)&&!i(s)&&(o=s)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{var t;if(Y(e.target)&&W(t=e.target)&&"style"in t)if(i(e.target)){let t=e.target;for(;t.parentElement&&i(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(Y(e.target)){var t;if(!($(t=e.target)&&"INPUT"===t.nodeName))if(i(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{},{before(e){var n;let{doc:r}=e,i=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-i.clientWidth)},after(e){let{doc:n,d:r}=e,i=n.documentElement,o=Math.max(0,i.clientWidth-i.offsetWidth),s=Math.max(0,t-o);r.style(i,"paddingRight","".concat(s,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];s.forEach(e=>{let{before:t}=e;return null==t?void 0:t(o)}),s.forEach(e=>{let{after:t}=e;return null==t?void 0:t(o)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});eh.subscribe(()=>{let e=eh.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&eh.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&eh.dispatch("TEARDOWN",n)}});var ep=n(89925),em=n(47769);let ef=(0,s.createContext)(()=>{});function eg(e){let{value:t,children:n}=e;return s.createElement(ef.Provider,{value:t},n)}var ev=n(91525);let ey=(0,s.createContext)(!1);function eb(e){return s.createElement(ey.Provider,{value:e.force},e.children)}let ex=(0,s.createContext)(void 0),ew=(0,s.createContext)(null);ew.displayName="DescriptionContext";let ek=Object.assign((0,es.FX)(function(e,t){let n=(0,s.useId)(),r=(0,s.useContext)(ex),{id:i="headlessui-description-".concat(n),...o}=e,a=function e(){let t=(0,s.useContext)(ew);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),l=(0,em.P)(t);(0,L.s)(()=>a.register(i),[i,a.register]);let u=r||!1,c=(0,s.useMemo)(()=>({...a.slot,disabled:u}),[a.slot,u]),d={ref:l,...a.props,id:i};return(0,es.Ci)()({ourProps:d,theirProps:o,slot:c,defaultTag:"p",name:a.name||"Description"})}),{});var eE=n(48014),eP=n(13250),eT=n(7856);function eS(e){let t=(0,R._)(e),n=(0,s.useRef)(!1);(0,s.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,eT._)(()=>{n.current&&t()})}),[t])}var eA=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(eA||{});function eM(e,t){let n=(0,s.useRef)([]),r=(0,R._)(e);(0,s.useEffect)(()=>{let e=[...n.current];for(let[i,o]of t.entries())if(n.current[i]!==o){let i=r(t,e);return n.current=t,i}},[r,...t])}let eC=[];function eD(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)W(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!Y(e.target)||e.target===document.body||eC[0]===e.target)return;let t=e.target;t=t.closest(H),eC.unshift(null!=t?t:e.target),(eC=eC.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eF=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eF||{});let eR=Object.assign((0,es.FX)(function(e,t){let n,r=(0,s.useRef)(null),i=(0,em.P)(r,t),{initialFocus:o,initialFocusFallback:a,containers:l,features:c=15,...d}=e;(0,ep.g)()||(c=0);let h=eo(r);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),i=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,s.useRef)(eC.slice());return eM((e,n)=>{let[r]=e,[i]=n;!0===i&&!1===r&&(0,eT._)(()=>{t.current.splice(0)}),!1===i&&!0===r&&(t.current=eC.slice())},[e,eC,t]),(0,R._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eM(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&J(i())},[r]),eS(()=>{r&&J(i())})}(c,{ownerDocument:h});let p=function(e,t){let{ownerDocument:n,container:r,initialFocus:i,initialFocusFallback:o}=t,a=(0,s.useRef)(null),l=O(!!(1&e),"focus-trap#initial-focus"),u=(0,eP.a)();return eM(()=>{if(0===e)return;if(!l){null!=o&&o.current&&J(o.current);return}let t=r.current;t&&(0,eT._)(()=>{if(!u.current)return;let r=null==n?void 0:n.activeElement;if(null!=i&&i.current){if((null==i?void 0:i.current)===r){a.current=r;return}}else if(t.contains(r)){a.current=r;return}if(null!=i&&i.current)J(i.current);else{if(16&e){if(ee(t,K.First|K.AutoFocus)!==q.Error)return}else if(ee(t,K.First)!==q.Error)return;if(null!=o&&o.current&&(J(o.current),(null==n?void 0:n.activeElement)===o.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=null==n?void 0:n.activeElement})},[o,l,e]),a}(c,{ownerDocument:h,container:r,initialFocus:o,initialFocusFallback:a});!function(e,t){let{ownerDocument:n,container:r,containers:i,previousActiveElement:o}=t,s=(0,eP.a)(),a=!!(4&e);u(null==n?void 0:n.defaultView,"focus",e=>{if(!a||!s.current)return;let t=eD(i);$(r.current)&&t.add(r.current);let n=o.current;if(!n)return;let l=e.target;$(l)?eV(t,l)?(o.current=l,J(l)):(e.preventDefault(),e.stopPropagation(),J(n)):J(o.current)},!0)}(c,{ownerDocument:h,container:r,containers:l,previousActiveElement:p});let m=(n=(0,s.useRef)(0),ei(!0,"keydown",e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},!0),n),f=(0,R._)(e=>{if(!$(r.current))return;let t=r.current;(0,E.Y)(m.current,{[eA.Forwards]:()=>{ee(t,K.First,{skipElements:[e.relatedTarget,a]})},[eA.Backwards]:()=>{ee(t,K.Last,{skipElements:[e.relatedTarget,a]})}})}),g=O(!!(2&c),"focus-trap#tab-lock"),v=(0,eE.L)(),y=(0,s.useRef)(!1),b=(0,es.Ci)();return s.createElement(s.Fragment,null,g&&s.createElement(el,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:ea.Focusable}),b({ourProps:{ref:i,onKeyDown(e){"Tab"==e.key&&(y.current=!0,v.requestAnimationFrame(()=>{y.current=!1}))},onBlur(e){if(!(4&c))return;let t=eD(l);$(r.current)&&t.add(r.current);let n=e.relatedTarget;Y(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(eV(t,n)||(y.current?ee(r.current,(0,E.Y)(m.current,{[eA.Forwards]:()=>K.Next,[eA.Backwards]:()=>K.Previous})|K.WrapAround,{relativeTo:e.target}):Y(e.target)&&J(e.target)))}},theirProps:d,defaultTag:"div",name:"FocusTrap"}),g&&s.createElement(el,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:f,features:ea.Focusable}))}),{features:eF});function eV(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var ej=n(47650);let eL=s.Fragment,eO=(0,es.FX)(function(e,t){let{ownerDocument:n=null,...r}=e,i=(0,s.useRef)(null),o=(0,em.P)((0,em.a)(e=>{i.current=e}),t),a=eo(i),l=null!=n?n:a,u=function(e){let t=(0,s.useContext)(ey),n=(0,s.useContext)(eN),[r,i]=(0,s.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(I._.isServer)return null;let i=null==e?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(null===e)return null;let o=e.createElement("div");return o.setAttribute("id","headlessui-portal-root"),e.body.appendChild(o)});return(0,s.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,s.useEffect)(()=>{t||null!==n&&i(n.current)},[n,i,t]),r}(l),[c]=(0,s.useState)(()=>{var e;return I._.isServer?null:null!=(e=null==l?void 0:l.createElement("div"))?e:null}),d=(0,s.useContext)(eB),h=(0,ep.g)();(0,L.s)(()=>{!u||!c||u.contains(c)||(c.setAttribute("data-headlessui-portal",""),u.appendChild(c))},[u,c]),(0,L.s)(()=>{if(c&&d)return d.register(c)},[d,c]),eS(()=>{var e;u&&c&&(_(c)&&u.contains(c)&&u.removeChild(c),u.childNodes.length<=0&&(null==(e=u.parentElement)||e.removeChild(u)))});let p=(0,es.Ci)();return h&&u&&c?(0,ej.createPortal)(p({ourProps:{ref:o},theirProps:r,slot:{},defaultTag:eL,name:"Portal"}),c):null}),eI=s.Fragment,eN=(0,s.createContext)(null),eB=(0,s.createContext)(null),eU=(0,es.FX)(function(e,t){let n=(0,em.P)(t),{enabled:r=!0,ownerDocument:i,...o}=e,a=(0,es.Ci)();return r?s.createElement(eO,{...o,ownerDocument:i,ref:n}):a({ourProps:{ref:n},theirProps:o,slot:{},defaultTag:eL,name:"Portal"})}),ez=(0,es.FX)(function(e,t){let{target:n,...r}=e,i={ref:(0,em.P)(t)},o=(0,es.Ci)();return s.createElement(eN.Provider,{value:n},o({ourProps:i,theirProps:r,defaultTag:eI,name:"Popover.Group"}))}),e_=Object.assign(eU,{Group:ez});var eW=n(15939),e$=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(e$||{}),eY=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(eY||{});let eH={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eX=(0,s.createContext)(null);function eK(e){let t=(0,s.useContext)(eX);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,eK),t}return t}function eq(e,t){return(0,E.Y)(t.type,eH,e,t)}eX.displayName="DialogContext";let eG=(0,es.FX)(function(e,t){let n,r,i,o,c,h,p,m,f,g,v=(0,s.useId)(),{id:y="headlessui-dialog-".concat(v),open:b,onClose:x,initialFocus:w,role:k="dialog",autoFocus:P=!0,__demoMode:T=!1,unmount:S=!1,...A}=e,M=(0,s.useRef)(!1);k="dialog"===k||"alertdialog"===k?k:(M.current||(M.current=!0,console.warn("Invalid role [".concat(k,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let C=(0,ev.O_)();void 0===b&&null!==C&&(b=(C&ev.Uw.Open)===ev.Uw.Open);let F=(0,s.useRef)(null),j=(0,em.P)(F,t),I=eo(F),B=+!b,[U,_]=(0,s.useReducer)(eq,{titleId:null,descriptionId:null,panelRef:(0,s.createRef)()}),X=(0,R._)(()=>x(!1)),K=(0,R._)(e=>_({type:0,id:e})),q=!!(0,ep.g)()&&0===B,[G,Q]=(n=(0,s.useContext)(eB),r=(0,s.useRef)([]),i=(0,R._)(e=>(r.current.push(e),n&&n.register(e),()=>o(e))),o=(0,R._)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),c=(0,s.useMemo)(()=>({register:i,unregister:o,portals:r}),[i,o,r]),[r,(0,s.useMemo)(()=>function(e){let{children:t}=e;return s.createElement(eB.Provider,{value:c},t)},[c])]),J=ed(),{resolveContainers:ee}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=eo(n),i=(0,R._)(()=>{var i,o;let s=[];for(let t of e)null!==t&&(W(t)?s.push(t):"current"in t&&W(t.current)&&s.push(t.current));if(null!=t&&t.current)for(let e of t.current)s.push(e);for(let e of null!=(i=null==r?void 0:r.querySelectorAll("html > *, body > *"))?i:[])e!==document.body&&e!==document.head&&W(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(o=null==n?void 0:n.getRootNode())?void 0:o.host))||s.some(t=>e.contains(t))||s.push(e));return s});return{resolveContainers:i,contains:(0,R._)(e=>i().some(t=>t.contains(e)))}}({mainTreeNode:J,portals:G,defaultContainers:[{get current(){var et;return null!=(et=U.panelRef.current)?et:F.current}}]}),ea=null!==C&&(C&ev.Uw.Closing)===ev.Uw.Closing;!function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=O(e,"inert-others");(0,L.s)(()=>{var e,i;if(!r)return;let o=(0,d.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&o.add(z(t));let s=null!=(i=null==t?void 0:t())?i:[];for(let e of s){if(!e)continue;let t=N(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)s.some(t=>e.contains(t))||o.add(z(e));n=n.parentElement}}return o.dispose},[r,t,n])}(!T&&!ea&&q,{allowed:(0,R._)(()=>{var e,t;return[null!=(t=null==(e=F.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,R._)(()=>{var e;return[null!=(e=null==J?void 0:J.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let el=D.get(null);(0,L.s)(()=>{if(q)return el.actions.push(y),()=>el.actions.pop(y)},[el,y,q]);let eu=V(el,(0,s.useCallback)(e=>el.selectors.isTop(e,y),[el,y]));h=(0,l.Y)(e=>{e.preventDefault(),X()}),p=(0,s.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(ee))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=N(e))?void 0:t.body)&&(0,E.Y)(n,{0:()=>e.matches(H),1(){let t=e;for(;null!==t;){if(t.matches(H))return!0;t=t.parentElement}return!1}})}(n,Z.Loose)||-1===n.tabIndex||e.preventDefault(),h.current(e,n)}},[h,ee]),m=(0,s.useRef)(null),er(eu,"pointerdown",e=>{var t,n;en()||(m.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),er(eu,"pointerup",e=>{if(en()||!m.current)return;let t=m.current;return m.current=null,p(e,()=>t)},!0),f=(0,s.useRef)({x:0,y:0}),er(eu,"touchstart",e=>{f.current.x=e.touches[0].clientX,f.current.y=e.touches[0].clientY},!0),er(eu,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-f.current.x)>=30||Math.abs(t.y-f.current.y)>=30))return p(e,()=>Y(e.target)?e.target:null)},!0),ei(eu,"blur",e=>p(e,()=>{var e;return $(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=O(e,"escape");u(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===a.Escape&&n(e))})}(eu,null==I?void 0:I.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),X()}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=(0,s.useSyncExternalStore)(eh.subscribe,eh.getSnapshot,eh.getSnapshot),i=t?r.get(t):void 0;i&&i.count,(0,L.s)(()=>{if(!(!t||!e))return eh.dispatch("PUSH",t,n),()=>eh.dispatch("POP",t,n)},[e,t])}(O(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!T&&!ea&&q,I,ee),g=(0,l.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&X()}),(0,s.useEffect)(()=>{if(!q)return;let e=null===F?null:$(F)?F:F.current;if(!e)return;let t=(0,d.e)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>g.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>g.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[F,g,q]);let[ec,ef]=function(){let[e,t]=(0,s.useState)([]);return[e.length>0?e.join(" "):void 0,(0,s.useMemo)(()=>function(e){let n=(0,R._)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,s.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return s.createElement(ew.Provider,{value:r},e.children)},[t])]}(),ey=(0,s.useMemo)(()=>[{dialogState:B,close:X,setTitleId:K,unmount:S},U],[B,U,X,K,S]),ex=(0,s.useMemo)(()=>({open:0===B}),[B]),ek={ref:j,id:y,role:k,tabIndex:-1,"aria-modal":T?void 0:0===B||void 0,"aria-labelledby":U.titleId,"aria-describedby":ec,unmount:S},eE=!function(){var e;let[t]=(0,s.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,s.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,L.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),eP=eF.None;q&&!T&&(eP|=eF.RestoreFocus,eP|=eF.TabLock,P&&(eP|=eF.AutoFocus),eE&&(eP|=eF.InitialFocus));let eT=(0,es.Ci)();return s.createElement(ev.$x,null,s.createElement(eb,{force:!0},s.createElement(e_,null,s.createElement(eX.Provider,{value:ey},s.createElement(ez,{target:F},s.createElement(eb,{force:!1},s.createElement(ef,{slot:ex},s.createElement(Q,null,s.createElement(eR,{initialFocus:w,initialFocusFallback:F,containers:ee,features:eP},s.createElement(eg,{value:X},eT({ourProps:ek,theirProps:A,slot:ex,defaultTag:eZ,features:eQ,visible:0===B,name:"Dialog"})))))))))))}),eZ="div",eQ=es.Ac.RenderStrategy|es.Ac.Static,eJ=Object.assign((0,es.FX)(function(e,t){let{transition:n=!1,open:r,...i}=e,o=(0,ev.O_)(),a=e.hasOwnProperty("open")||null!==o,l=e.hasOwnProperty("onClose");if(!a&&!l)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!l)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!o&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==r||n)&&!i.static?s.createElement(ec,null,s.createElement(eW.e,{show:r,transition:n,unmount:i.unmount},s.createElement(eG,{ref:t,...i}))):s.createElement(ec,null,s.createElement(eG,{ref:t,open:r,...i}))}),{Panel:(0,es.FX)(function(e,t){let n=(0,s.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:i=!1,...o}=e,[{dialogState:a,unmount:l},u]=eK("Dialog.Panel"),c=(0,em.P)(t,u.panelRef),d=(0,s.useMemo)(()=>({open:0===a}),[a]),h=(0,R._)(e=>{e.stopPropagation()}),p=i?eW._:s.Fragment,m=(0,es.Ci)();return s.createElement(p,{...i?{unmount:l}:{}},m({ourProps:{ref:c,id:r,onClick:h},theirProps:o,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,es.FX)(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:i,unmount:o}]=eK("Dialog.Backdrop"),a=(0,s.useMemo)(()=>({open:0===i}),[i]),l=n?eW._:s.Fragment,u=(0,es.Ci)();return s.createElement(l,{...n?{unmount:o}:{}},u({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,es.FX)(function(e,t){let n=(0,s.useId)(),{id:r="headlessui-dialog-title-".concat(n),...i}=e,[{dialogState:o,setTitleId:a}]=eK("Dialog.Title"),l=(0,em.P)(t);(0,s.useEffect)(()=>(a(r),()=>a(null)),[r,a]);let u=(0,s.useMemo)(()=>({open:0===o}),[o]);return(0,es.Ci)()({ourProps:{ref:l,id:r},theirProps:i,slot:u,defaultTag:"h2",name:"Dialog.Title"})})),Description:ek})},80845:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(12115).createContext)(null)},81586:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},82885:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(12115);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},84554:(e,t,n)=>{n.d(t,{Ac:()=>s,Ci:()=>l,FX:()=>h,mK:()=>a,oE:()=>p});var r=n(12115),i=n(20379),o=n(27279),s=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(s||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function l(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];if(!r.every(e=>null==e))return e.current=r,t});return(0,r.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:i,features:s,visible:a=!0,name:l,mergeRefs:h}=e;h=null!=h?h:c;let p=d(n,t);if(a)return u(p,r,i,l,h);let m=null!=s?s:0;if(2&m){let{static:e=!1,...t}=p;if(e)return u(t,r,i,l,h)}if(1&m){let{unmount:e=!0,...t}=p;return(0,o.Y)(+!e,{0:()=>null,1:()=>u({...t,hidden:!0,style:{display:"none"}},r,i,l,h)})}return u(p,r,i,l,h)})({mergeRefs:n,...e}),[n])}function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0,{as:a=n,children:l,refName:u="ref",...c}=m(e,["unmount","static"]),h=void 0!==e.ref?{[u]:e.ref}:{},f="function"==typeof l?l(t):l;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let g={};if(t){let e=!1,n=[];for(let[r,i]of Object.entries(t))"boolean"==typeof i&&(e=!0),!0===i&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(g["data-headlessui-state"]=n.join(" "),n))g["data-".concat(e)]=""}if(a===r.Fragment&&(Object.keys(p(c)).length>0||Object.keys(p(g)).length>0))if(!(0,r.isValidElement)(f)||Array.isArray(f)&&f.length>1){if(Object.keys(p(c)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(o,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(p(c)).concat(Object.keys(p(g))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var v;let e=f.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,i.x)(t(...n),c.className)}:(0,i.x)(t,c.className),o=d(f.props,p(m(c,["ref"])));for(let e in g)e in o&&delete g[e];return(0,r.cloneElement)(f,Object.assign({},o,g,h,{ref:s((v=f,r.version.split(".")[0]>="19"?v.props.ref:v.ref),h.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},m(c,["ref"]),a!==r.Fragment&&h,a!==r.Fragment&&g),f)}function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function d(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},i={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=i[t]||(i[t]=[]),i[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in i)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(i[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in i)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];for(let n of i[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function h(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function p(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87657:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,i=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,o=(e,t,n)=>(i(e,"symbol"!=typeof t?t+"":t,n),n);class s{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){o(this,"current",this.detect()),o(this,"handoffState","pending"),o(this,"currentId",0)}}let a=new s},87712:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},89925:(e,t,n)=>{n.d(t,{g:()=>s});var r,i=n(12115),o=n(87657);function s(){let e,t=(e="undefined"==typeof document,(0,(r||(r=n.t(i,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[s,a]=i.useState(o._.isHandoffComplete);return s&&!1===o._.isHandoffComplete&&a(!1),i.useEffect(()=>{!0!==s&&a(!0)},[s]),i.useEffect(()=>o._.handoff(),[]),!t&&s}},90869:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(12115).createContext)({})},91525:(e,t,n)=>{n.d(t,{$x:()=>l,El:()=>a,O_:()=>s,Uw:()=>o});var r=n(12115);let i=(0,r.createContext)(null);i.displayName="OpenClosedContext";var o=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(o||{});function s(){return(0,r.useContext)(i)}function a(e){let{value:t,children:n}=e;return r.createElement(i.Provider,{value:t},n)}function l(e){let{children:t}=e;return r.createElement(i.Provider,{value:null},t)}},97494:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(12115);let i=n(68972).B?r.useLayoutEffect:r.useEffect}}]);