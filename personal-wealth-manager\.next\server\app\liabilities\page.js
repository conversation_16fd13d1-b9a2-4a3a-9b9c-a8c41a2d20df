(()=>{var e={};e.id=990,e.ids=[990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29860:(e,a,t)=>{"use strict";t.d(a,{default:()=>g});var s=t(60687),r=t(43210),l=t(43649),i=t(96474),n=t(63143),d=t(88233),c=t(2643),o=t(28749),u=t(44547),x=t(99760),p=t(77970),m=t(86945),h=t(4780),j=t(51907),v=t(43949),y=t(70695),b=t(45225);function f({liability:e,onSuccess:a,onCancel:t}){let[l,i]=(0,r.useState)(!1),[n,d]=(0,r.useState)({name:e?.name||"",description:e?.description||"",principal_amount:e?.principal_amount||0,current_balance:e?.current_balance||0,interest_rate:e?.interest_rate||0,due_date:e?.due_date||"",liability_type:e?.liability_type||"loan_taken",status:e?.status||"active",currency:e?.currency||"LKR"}),[u,x]=(0,r.useState)("annual"),p=async t=>{t.preventDefault(),i(!0);try{let t;if((t=e?await (0,m.p9)(e.id,n):await (0,m.W2)(n)).error){console.error("Error saving liability:",t.error),alert("Error saving liability: "+t.error.message);return}a()}catch(e){console.error("Error saving liability:",e),alert("Error saving liability: "+e.message)}finally{i(!1)}},h=(e,a)=>{if(("principal_amount"===e||"current_balance"===e||"interest_rate"===e)&&"number"==typeof a&&a>0xe8d4a50fff)return void alert("Value is too large. Please enter a smaller amount.");d(t=>({...t,[e]:a}))};return(0,s.jsxs)(o.Card,{className:"w-full max-w-2xl",children:[(0,s.jsx)(o.CardHeader,{children:(0,s.jsx)(o.CardTitle,{children:e?"Edit Liability":"Add New Liability"})}),(0,s.jsx)(o.CardContent,{children:(0,s.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"name",children:"Liability Name *"}),(0,s.jsx)(j.p,{id:"name",value:n.name,onChange:e=>h("name",e.target.value),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"liability_type",children:"Type *"}),(0,s.jsxs)(y.l,{id:"liability_type",value:n.liability_type,onChange:e=>h("liability_type",e.target.value),required:!0,children:[(0,s.jsx)("option",{value:"loan_taken",children:"Loan Taken"}),(0,s.jsx)("option",{value:"credit_card",children:"Credit Card"}),(0,s.jsx)("option",{value:"mortgage",children:"Mortgage"}),(0,s.jsx)("option",{value:"insurance",children:"Insurance"}),(0,s.jsx)("option",{value:"utilities",children:"Utilities"}),(0,s.jsx)("option",{value:"taxes",children:"Taxes"}),(0,s.jsx)("option",{value:"subscription",children:"Subscription"}),(0,s.jsx)("option",{value:"rent",children:"Rent"}),(0,s.jsx)("option",{value:"medical",children:"Medical"}),(0,s.jsx)("option",{value:"education",children:"Education"}),(0,s.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)(b.T,{id:"description",value:n.description,onChange:e=>h("description",e.target.value),rows:3})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"principal_amount",children:"Principal Amount *"}),(0,s.jsx)(j.p,{id:"principal_amount",type:"number",step:"0.01",value:n.principal_amount,onChange:e=>h("principal_amount",parseFloat(e.target.value)||0),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"current_balance",children:"Current Balance *"}),(0,s.jsx)(j.p,{id:"current_balance",type:"number",step:"0.01",value:n.current_balance,onChange:e=>h("current_balance",parseFloat(e.target.value)||0),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"interest_rate",children:"Interest Rate (%) - Optional"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(j.p,{id:"interest_rate",type:"number",step:"0.01",min:"0",max:"100",value:n.interest_rate||"",onChange:e=>h("interest_rate",parseFloat(e.target.value)||0),placeholder:"0.00",className:"flex-1"}),(0,s.jsxs)(y.l,{value:u,onChange:e=>x(e.target.value),className:"w-32",children:[(0,s.jsx)("option",{value:"annual",children:"Annual"}),(0,s.jsx)("option",{value:"monthly",children:"Monthly"}),(0,s.jsx)("option",{value:"daily",children:"Daily"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"due_date",children:"Due Date - Optional"}),(0,s.jsx)(j.p,{id:"due_date",type:"date",value:n.due_date,onChange:e=>h("due_date",e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"status",children:"Status"}),(0,s.jsxs)(y.l,{id:"status",value:n.status,onChange:e=>h("status",e.target.value),children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"paid_off",children:"Paid Off"}),(0,s.jsx)("option",{value:"defaulted",children:"Defaulted"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(v.J,{htmlFor:"currency",children:"Currency"}),(0,s.jsxs)(y.l,{id:"currency",value:n.currency,onChange:e=>h("currency",e.target.value),children:[(0,s.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,s.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,s.jsx)(c.$,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,s.jsx)(c.$,{type:"submit",disabled:l,children:l?"Saving...":e?"Update Liability":"Create Liability"})]})]})})]})}function g(){let[e,a]=(0,r.useState)([]),[t,j]=(0,r.useState)(!0),[v,y]=(0,r.useState)(!1),[b,g]=(0,r.useState)(),[_,N]=(0,r.useState)(""),[w,C]=(0,r.useState)({liability_type:"",status:"",min_balance:"",max_balance:"",due_date_from:"",due_date_to:""}),k=async()=>{j(!0);let{data:e}=await (0,m.mz)();e&&a(e),j(!1)},A=async e=>{confirm("Are you sure you want to delete this liability?")&&(await (0,m.pq)(e),k())},D=e=>{g(e),y(!0)},q=(0,r.useMemo)(()=>e.filter(e=>{let a=""===_||e.name.toLowerCase().includes(_.toLowerCase())||e.description?.toLowerCase().includes(_.toLowerCase())||e.liability_type.toLowerCase().includes(_.toLowerCase()),t=""===w.liability_type||e.liability_type===w.liability_type,s=""===w.status||e.status===w.status,r=""===w.min_balance||e.current_balance>=parseFloat(w.min_balance),l=""===w.max_balance||e.current_balance<=parseFloat(w.max_balance),i=""===w.due_date_from||!e.due_date||new Date(e.due_date)>=new Date(w.due_date_from),n=""===w.due_date_to||!e.due_date||new Date(e.due_date)<=new Date(w.due_date_to);return a&&t&&s&&r&&l&&i&&n}),[e,_,w]),P=q.filter(e=>"active"===e.status).reduce((e,a)=>e+a.current_balance,0),L=q.filter(e=>"active"===e.status&&e.due_date&&new Date(e.due_date)<new Date),S=e=>{switch(e){case"active":return"text-green-600";case"paid_off":return"text-blue-600";case"defaulted":return"text-red-600";default:return"text-gray-600"}};return v?(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(f,{liability:b,onSuccess:()=>{y(!1),g(void 0),k()},onCancel:()=>{y(!1),g(void 0)}})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Liabilities"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Total Outstanding: ",(0,h.vv)(P),q.length!==e.length&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",q.length," of ",e.length," shown)"]})]}),L.length>0&&(0,s.jsxs)("p",{className:"text-red-600 flex items-center mt-1",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-1"}),L.length," overdue liability(ies)"]})]}),(0,s.jsxs)(c.$,{onClick:()=>y(!0),className:"w-full sm:w-auto",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Add Liability"]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)(x.A,{value:_,onChange:N,placeholder:"Search liabilities...",className:"flex-1"}),(0,s.jsx)(p.A,{filters:[{key:"liability_type",label:"Liability Type",type:"select",options:[{value:"loan_taken",label:"Loan Taken"},{value:"credit_card",label:"Credit Card"},{value:"mortgage",label:"Mortgage"},{value:"other",label:"Other"}]},{key:"status",label:"Status",type:"select",options:[{value:"active",label:"Active"},{value:"paid_off",label:"Paid Off"},{value:"defaulted",label:"Defaulted"}]},{key:"min_balance",label:"Minimum Balance",type:"number"},{key:"max_balance",label:"Maximum Balance",type:"number"},{key:"due_date_from",label:"Due Date From",type:"date"},{key:"due_date_to",label:"Due Date To",type:"date"}],values:w,onChange:(e,a)=>{C(t=>({...t,[e]:a}))},onClear:()=>{C({liability_type:"",status:"",min_balance:"",max_balance:"",due_date_from:"",due_date_to:""}),N("")}})]}),(0,s.jsxs)(o.Card,{children:[(0,s.jsx)(o.CardHeader,{children:(0,s.jsx)(o.CardTitle,{children:"Your Liabilities"})}),(0,s.jsxs)(o.CardContent,{children:[t?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===q.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:0===e.length?"No liabilities found. Add your first liability to get started.":"No liabilities match your search criteria."}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"hidden lg:block",children:(0,s.jsxs)(u.XI,{children:[(0,s.jsx)(u.A0,{children:(0,s.jsxs)(u.Hj,{children:[(0,s.jsx)(u.nd,{children:"Name"}),(0,s.jsx)(u.nd,{children:"Type"}),(0,s.jsx)(u.nd,{children:"Principal"}),(0,s.jsx)(u.nd,{children:"Current Balance"}),(0,s.jsx)(u.nd,{children:"Interest Rate"}),(0,s.jsx)(u.nd,{children:"Due Date"}),(0,s.jsx)(u.nd,{children:"Status"}),(0,s.jsx)(u.nd,{children:"Actions"})]})}),(0,s.jsx)(u.BF,{children:q.map(e=>{let a="active"===e.status&&e.due_date&&new Date(e.due_date)<new Date;return(0,s.jsxs)(u.Hj,{className:a?"bg-red-50":"",children:[(0,s.jsxs)(u.nA,{className:"font-medium",children:[e.name,a&&(0,s.jsx)(l.A,{className:"h-4 w-4 text-red-500 inline ml-2"})]}),(0,s.jsx)(u.nA,{className:"capitalize",children:e.liability_type.replace("_"," ")}),(0,s.jsx)(u.nA,{children:(0,h.vv)(e.principal_amount,e.currency)}),(0,s.jsx)(u.nA,{children:(0,h.vv)(e.current_balance,e.currency)}),(0,s.jsx)(u.nA,{children:e.interest_rate?`${e.interest_rate}%`:"-"}),(0,s.jsx)(u.nA,{children:e.due_date?(0,h.Yq)(e.due_date):"-"}),(0,s.jsx)(u.nA,{children:(0,s.jsx)("span",{className:`capitalize ${S(e.status)}`,children:e.status.replace("_"," ")})}),(0,s.jsx)(u.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),(0,s.jsx)("div",{className:"lg:hidden space-y-4",children:q.map(e=>{let a="active"===e.status&&e.due_date&&new Date(e.due_date)<new Date;return(0,s.jsxs)("div",{className:`bg-white border rounded-lg p-4 shadow-sm ${a?"border-red-200 bg-red-50":"border-gray-200"}`,children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-medium text-gray-900 flex items-center",children:[e.name,a&&(0,s.jsx)(l.A,{className:"h-4 w-4 text-red-500 ml-2"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 capitalize",children:e.liability_type.replace("_"," ")})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Principal:"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:(0,h.vv)(e.principal_amount,e.currency)})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Current Balance:"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:(0,h.vv)(e.current_balance,e.currency)})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Interest Rate:"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.interest_rate?`${e.interest_rate}%`:"-"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Due Date:"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.due_date?(0,h.Yq)(e.due_date):"-"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Status:"}),(0,s.jsx)("span",{className:`text-sm font-medium capitalize ${S(e.status)}`,children:e.status.replace("_"," ")})]})]})]},e.id)})})]}),")}"]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40137:(e,a,t)=>{Promise.resolve().then(t.bind(t,46655)),Promise.resolve().then(t.bind(t,41237))},41237:(e,a,t)=>{"use strict";t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\liabilities\\\\LiabilityList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\liabilities\\LiabilityList.tsx","default")},43649:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44547:(e,a,t)=>{"use strict";t.d(a,{A0:()=>n,BF:()=>d,Hj:()=>c,XI:()=>i,nA:()=>u,nd:()=>o});var s=t(60687),r=t(43210),l=t(4780);let i=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",e),...a})}));i.displayName="Table";let n=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",e),...a}));n.displayName="TableHeader";let d=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...a}));d.displayName="TableBody",(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("tfoot",{ref:t,className:(0,l.cn)("bg-gray-900/5 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let c=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",e),...a}));c.displayName="TableRow";let o=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",e),...a}));o.displayName="TableHead";let u=(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));u.displayName="TableCell",(0,r.forwardRef)(({className:e,...a},t)=>(0,s.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-gray-500",e),...a})).displayName="TableCaption"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},77970:(e,a,t)=>{"use strict";t.d(a,{A:()=>x});var s=t(60687),r=t(43210),l=t(62688);let i=(0,l.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),n=(0,l.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var d=t(2643),c=t(70695),o=t(51907),u=t(43949);function x({filters:e,values:a,onChange:t,onClear:l}){let[x,p]=(0,r.useState)(!1),m=Object.values(a).filter(e=>""!==e&&null!=e).length;return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(d.$,{variant:"outline",onClick:()=>p(!x),className:"flex items-center",children:[(0,s.jsx)(i,{className:"h-4 w-4 mr-2"}),"Filters",m>0&&(0,s.jsx)("span",{className:"ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5",children:m}),(0,s.jsx)(n,{className:"h-4 w-4 ml-2"})]}),x&&(0,s.jsx)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Filters"}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>{l(),p(!1)},children:"Clear All"})]}),e.map(e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(u.J,{htmlFor:e.key,children:e.label}),"select"===e.type&&e.options?(0,s.jsxs)(c.l,{id:e.key,value:a[e.key]||"",onChange:a=>t(e.key,a.target.value),children:[(0,s.jsx)("option",{value:"",children:"All"}),e.options.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)(o.p,{id:e.key,type:"date"===e.type?"date":"number"===e.type?"number":"text",value:a[e.key]||"",onChange:a=>t(e.key,a.target.value)})]},e.key)),(0,s.jsx)("div",{className:"flex justify-end pt-2",children:(0,s.jsx)(d.$,{size:"sm",onClick:()=>p(!1),children:"Apply Filters"})})]})})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81993:(e,a,t)=>{Promise.resolve().then(t.bind(t,63087)),Promise.resolve().then(t.bind(t,29860))},86547:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var s=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let c={children:["",{children:["liabilities",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96500)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\liabilities\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\liabilities\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/liabilities/page",pathname:"/liabilities",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88233:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96500:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var s=t(37413),r=t(46655),l=t(41237);function i(){return(0,s.jsx)(r.default,{children:(0,s.jsx)(l.default,{})})}},99760:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(60687);let r=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var l=t(11860),i=t(51907),n=t(2643);function d({value:e,onChange:a,placeholder:t="Search...",className:d}){return(0,s.jsxs)("div",{className:`relative ${d}`,children:[(0,s.jsx)(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(i.p,{type:"text",placeholder:t,value:e,onChange:e=>a(e.target.value),className:"pl-10 pr-10"}),e&&(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>a(""),children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]})}}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,145,79,814,435,235],()=>t(86547));module.exports=s})();