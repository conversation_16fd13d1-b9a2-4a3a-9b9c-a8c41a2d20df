(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{8197:(e,a,l)=>{Promise.resolve().then(l.bind(l,11905)),Promise.resolve().then(l.bind(l,13049))},11905:(e,a,l)=>{"use strict";l.d(a,{default:()=>b});var t=l(95155),s=l(12115),i=l(60760),r=l(76408),n=l(84616),c=l(43332),o=l(13717),d=l(62525),u=l(13741),x=l(17703),h=l(19816),m=l(93915),g=l(33985),y=l(30353);let p=[{value:"#EF4444",label:"Red",bg:"bg-red-500"},{value:"#F59E0B",label:"Orange",bg:"bg-orange-500"},{value:"#EAB308",label:"Yellow",bg:"bg-yellow-500"},{value:"#10B981",label:"Green",bg:"bg-green-500"},{value:"#3B82F6",label:"Blue",bg:"bg-blue-500"},{value:"#8B5CF6",label:"Purple",bg:"bg-purple-500"},{value:"#EC4899",label:"Pink",bg:"bg-pink-500"},{value:"#6B7280",label:"Gray",bg:"bg-gray-500"}];function v(e){let{category:a,onSuccess:l,onCancel:i}=e,[n,c]=(0,s.useState)(!1),[o,d]=(0,s.useState)({name:(null==a?void 0:a.name)||"",type:(null==a?void 0:a.type)||"expense",color:(null==a?void 0:a.color)||"#3B82F6"}),v=async e=>{e.preventDefault(),c(!0);try{a?await (0,h.st)(a.id,o):await (0,h.zZ)(o),l()}catch(e){console.error("Error saving category:",e)}finally{c(!1)}},b=(e,a)=>{d(l=>({...l,[e]:a}))};return(0,t.jsx)(r.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,t.jsxs)(x.Card,{className:"w-full max-w-md",children:[(0,t.jsx)(x.CardHeader,{children:(0,t.jsxs)(x.CardTitle,{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:o.color}}),a?"Edit Category":"Add New Category"]})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"name",children:"Category Name *"}),(0,t.jsx)(m.p,{id:"name",value:o.name,onChange:e=>b("name",e.target.value),placeholder:"Enter category name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"type",children:"Category Type *"}),(0,t.jsxs)(y.l,{id:"type",value:o.type,onChange:e=>b("type",e.target.value),required:!0,children:[(0,t.jsx)("option",{value:"income",children:"Income"}),(0,t.jsx)("option",{value:"expense",children:"Expense"}),(0,t.jsx)("option",{value:"asset",children:"Asset"}),(0,t.jsx)("option",{value:"liability",children:"Liability"}),(0,t.jsx)("option",{value:"receivable",children:"Receivable"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"color",children:"Color"}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-3 mt-2",children:p.map(e=>(0,t.jsx)("button",{type:"button",onClick:()=>b("color",e.value),className:"\n                      w-full h-12 rounded-xl ".concat(e.bg," flex items-center justify-center\n                      ").concat(o.color===e.value?"ring-4 ring-blue-500 ring-offset-2":"hover:scale-105","\n                      transition-all duration-200\n                    "),children:o.color===e.value&&(0,t.jsx)("svg",{className:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})},e.value))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(u.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,t.jsx)(u.$,{type:"submit",loading:n,children:a?"Update Category":"Create Category"})]})]})})]})})}function b(){let[e,a]=(0,s.useState)([]),[l,m]=(0,s.useState)(!0),[g,y]=(0,s.useState)(!1),[p,b]=(0,s.useState)(),[j,f]=(0,s.useState)("all");(0,s.useEffect)(()=>{N()},[]);let N=async()=>{m(!0);let{data:e}=await (0,h.bW)();e&&a(e),m(!1)},C=async e=>{confirm("Are you sure you want to delete this category?")&&(await (0,h.K7)(e),N())},w=e=>{b(e),y(!0)},k="all"===j?e:e.filter(e=>e.type===j),A=[{value:"all",label:"All Categories",count:e.length},{value:"income",label:"Income",count:e.filter(e=>"income"===e.type).length},{value:"expense",label:"Expense",count:e.filter(e=>"expense"===e.type).length},{value:"asset",label:"Asset",count:e.filter(e=>"asset"===e.type).length},{value:"liability",label:"Liability",count:e.filter(e=>"liability"===e.type).length},{value:"receivable",label:"Receivable",count:e.filter(e=>"receivable"===e.type).length}];return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Categories"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Organize your financial transactions with custom categories"})]}),(0,t.jsxs)(u.$,{onClick:()=>y(!0),variant:"gradient",children:[(0,t.jsx)(n.A,{className:"h-5 w-5 mr-2"}),"Add Category"]})]}),(0,t.jsx)(x.Card,{children:(0,t.jsx)(x.CardContent,{className:"p-6",children:(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:A.map(e=>(0,t.jsxs)("button",{onClick:()=>f(e.value),className:"\n                  px-4 py-2 rounded-xl font-medium transition-all duration-200\n                  ".concat(j===e.value?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200","\n                "),children:[e.label," (",e.count,")"]},e.value))})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,t.jsx)(i.N,{children:l?Array.from({length:6}).map((e,a)=>(0,t.jsx)(x.Card,{className:"animate-pulse",children:(0,t.jsxs)(x.CardContent,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 bg-gray-300 rounded-full"}),(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded flex-1"})]}),(0,t.jsx)("div",{className:"mt-4 h-3 bg-gray-300 rounded w-1/2"})]})},a)):0===k.length?(0,t.jsxs)("div",{className:"col-span-full text-center py-12",children:[(0,t.jsx)(c.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No categories found"}),(0,t.jsx)("p",{className:"text-gray-500 mb-6",children:"all"===j?"Create your first category to get started":"No ".concat(j," categories found")}),(0,t.jsxs)(u.$,{onClick:()=>y(!0),children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Add Category"]})]}):k.map((e,a)=>(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.1*a},children:(0,t.jsx)(x.Card,{className:"hover:shadow-xl transition-all duration-300 group",children:(0,t.jsxs)(x.CardContent,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.color}}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name})]}),(0,t.jsxs)("div",{className:"flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,t.jsx)(u.$,{size:"sm",variant:"outline",onClick:()=>w(e),children:(0,t.jsx)(o.A,{className:"h-4 w-4"})}),(0,t.jsx)(u.$,{size:"sm",variant:"destructive",onClick:()=>C(e.id),children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"text-sm text-gray-500 capitalize",children:e.type.replace("_"," ")})]})})},e.id))})}),(0,t.jsx)(i.N,{children:g&&(0,t.jsx)(v,{category:p,onSuccess:()=>{y(!1),b(void 0),N()},onCancel:()=>{y(!1),b(void 0)}})})]})}},13717:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},62525:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[271,874,556,49,441,684,358],()=>a(8197)),_N_E=e.O()}]);