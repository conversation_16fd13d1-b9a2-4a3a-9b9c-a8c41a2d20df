{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\n\nexport async function signUp(email: string, password: string, fullName: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        full_name: fullName,\n      },\n    },\n  })\n\n  return { data, error }\n}\n\nexport async function signIn(email: string, password: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  })\n\n  return { data, error }\n}\n\nexport async function signOut() {\n  const supabase = createClient()\n  \n  const { error } = await supabase.auth.signOut()\n  \n  return { error }\n}\n\nexport async function getUser() {\n  const supabase = createClient()\n\n  const { data: { user }, error } = await supabase.auth.getUser()\n\n  return { user, error }\n}\n\nexport async function getProfile() {\n  const supabase = createClient()\n\n  const { data: { user }, error: userError } = await supabase.auth.getUser()\n\n  if (userError || !user) {\n    return { profile: null, error: userError }\n  }\n\n  const { data: profile, error: profileError } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n\n  return { profile, error: profileError }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAgB;IAC5E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB;IAC1D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7C,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7D,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAM,OAAO;QAAU;IAC3C;IAEA,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,OAAO;QAAE;QAAS,OAAO;IAAa;AACxC", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'LKR'): string {\n  if (currency === 'LKR') {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'LKR',\n      currencyDisplay: 'symbol',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(amount).replace('LKR', 'Rs.')\n  }\n\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  })\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return 0\n  return ((current - previous) / previous) * 100\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,IAAI,aAAa,OAAO;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,QAAQ,OAAO,CAAC,OAAO;IACnC;IAEA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  LayoutDashboard,\n  TrendingUp,\n  CreditCard,\n  Users,\n  Tag,\n  Receipt,\n  Target,\n  BarChart3,\n  Settings,\n  LogOut\n} from 'lucide-react'\nimport { signOut } from '@/lib/auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Transactions', href: '/transactions', icon: Receipt },\n  { name: 'Budget', href: '/budget', icon: Target },\n  { name: 'Reports', href: '/reports', icon: BarChart3 },\n  { name: 'Assets', href: '/assets', icon: TrendingUp },\n  { name: 'Liabilities', href: '/liabilities', icon: CreditCard },\n  { name: 'Receivables', href: '/receivables', icon: Users },\n  { name: 'Categories', href: '/categories', icon: Tag },\n  { name: 'Settings', href: '/settings', icon: Settings },\n]\n\nexport default function Sidebar() {\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n    window.location.href = '/login'\n  }\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl\">\n      <div className=\"flex h-20 items-center px-6 border-b border-gray-200/50\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">W</span>\n          </div>\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\">\n            Wealth Manager\n          </h1>\n        </div>\n      </div>\n      \n      <nav className=\"flex-1 space-y-2 px-4 py-6\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 hover:scale-105 active:scale-95',\n                isActive\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25'\n                  : 'text-gray-700 hover:bg-white/60 hover:text-gray-900 hover:shadow-md'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n      \n      <div className=\"border-t border-gray-200 p-3\">\n        <button\n          onClick={handleSignOut}\n          className=\"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors\"\n        >\n          <LogOut className=\"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\" />\n          Sign out\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAjBA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC7D;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,sMAAA,CAAA,SAAM;IAAC;IAChD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kNAAA,CAAA,YAAS;IAAC;IACrD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,kNAAA,CAAA,aAAU;IAAC;IACpD;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC9D;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gMAAA,CAAA,MAAG;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACvD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAG,WAAU;sCAA6F;;;;;;;;;;;;;;;;;0BAM/G,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kIACA,WACI,yFACA;;0CAGN,8OAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,eAAe;;;;;;4BAG7B,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAuE;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/layout/MobileSidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Fragment } from 'react'\nimport { Dialog, Transition } from '@headlessui/react'\nimport {\n  LayoutDashboard,\n  TrendingUp,\n  CreditCard,\n  Users,\n  Tag,\n  Receipt,\n  Target,\n  BarChart3,\n  Settings,\n  LogOut,\n  X\n} from 'lucide-react'\nimport { signOut } from '@/lib/auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Transactions', href: '/transactions', icon: Receipt },\n  { name: 'Budget', href: '/budget', icon: Target },\n  { name: 'Reports', href: '/reports', icon: BarChart3 },\n  { name: 'Assets', href: '/assets', icon: TrendingUp },\n  { name: 'Liabilities', href: '/liabilities', icon: CreditCard },\n  { name: 'Receivables', href: '/receivables', icon: Users },\n  { name: 'Categories', href: '/categories', icon: Tag },\n  { name: 'Settings', href: '/settings', icon: Settings },\n]\n\ninterface MobileSidebarProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport default function MobileSidebar({ isOpen, onClose }: MobileSidebarProps) {\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n    window.location.href = '/login'\n  }\n\n  return (\n    <Transition.Root show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50 lg:hidden\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"transition-opacity ease-linear duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"transition-opacity ease-linear duration-300\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-gray-900/80\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 flex\">\n          <Transition.Child\n            as={Fragment}\n            enter=\"transition ease-in-out duration-300 transform\"\n            enterFrom=\"-translate-x-full\"\n            enterTo=\"translate-x-0\"\n            leave=\"transition ease-in-out duration-300 transform\"\n            leaveFrom=\"translate-x-0\"\n            leaveTo=\"-translate-x-full\"\n          >\n            <Dialog.Panel className=\"relative mr-16 flex w-full max-w-xs flex-1\">\n              <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white/95 backdrop-blur-xl px-6 pb-4 shadow-xl\">\n                <div className=\"flex h-16 shrink-0 items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">W</span>\n                    </div>\n                    <h1 className=\"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\">\n                      Wealth Manager\n                    </h1>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\"\n                    onClick={onClose}\n                  >\n                    <X className=\"h-6 w-6\" />\n                  </button>\n                </div>\n                \n                <nav className=\"flex flex-1 flex-col\">\n                  <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n                    <li>\n                      <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                        {navigation.map((item) => {\n                          const isActive = pathname === item.href\n                          return (\n                            <li key={item.name}>\n                              <Link\n                                href={item.href}\n                                onClick={onClose}\n                                className={cn(\n                                  'group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-semibold transition-all duration-200',\n                                  isActive\n                                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'\n                                )}\n                              >\n                                <item.icon\n                                  className={cn(\n                                    'h-6 w-6 shrink-0',\n                                    isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'\n                                  )}\n                                />\n                                {item.name}\n                              </Link>\n                            </li>\n                          )\n                        })}\n                      </ul>\n                    </li>\n                    <li className=\"mt-auto\">\n                      <button\n                        onClick={handleSignOut}\n                        className=\"group -mx-2 flex w-full gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors\"\n                      >\n                        <LogOut className=\"h-6 w-6 shrink-0 text-gray-400 group-hover:text-gray-600\" />\n                        Sign out\n                      </button>\n                    </li>\n                  </ul>\n                </nav>\n              </div>\n            </Dialog.Panel>\n          </Transition.Child>\n        </div>\n      </Dialog>\n    </Transition.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AApBA;;;;;;;;;AAsBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC7D;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,sMAAA,CAAA,SAAM;IAAC;IAChD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kNAAA,CAAA,YAAS;IAAC;IACrD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,kNAAA,CAAA,aAAU;IAAC;IACpD;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC9D;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gMAAA,CAAA,MAAG;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACvD;AAOc,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAsB;IAC3E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU,CAAC,IAAI;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBACzC,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAA0B,SAAS;;8BAC5D,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;wBACf,IAAI,qMAAA,CAAA,WAAQ;wBACZ,OAAM;wBACN,WAAU;wBACV,SAAQ;wBACR,OAAM;wBACN,WAAU;wBACV,SAAQ;kCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;sCACtB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;kEAEjD,8OAAC;wDAAG,WAAU;kEAA6F;;;;;;;;;;;;0DAI7G,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,MAAK;4CAAO,WAAU;;8DACxB,8OAAC;8DACC,cAAA,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,WAAW,GAAG,CAAC,CAAC;4DACf,MAAM,WAAW,aAAa,KAAK,IAAI;4DACvC,qBACE,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,SAAS;oEACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,WACI,sEACA;;sFAGN,8OAAC,KAAK,IAAI;4EACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oBACA,WAAW,eAAe;;;;;;wEAG7B,KAAK,IAAI;;;;;;;+DAjBL,KAAK,IAAI;;;;;wDAqBtB;;;;;;;;;;;8DAGJ,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavG", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport type { User } from '@supabase/supabase-js'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n\n      if (!user) {\n        router.push('/login')\n      }\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN') {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;YAEX,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,aAAa;gBAChC,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/GradientBackground.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\ninterface GradientBackgroundProps {\n  children: React.ReactNode\n  variant?: 'default' | 'dashboard' | 'auth'\n}\n\nfunction GradientBackground({\n  children,\n  variant = 'default'\n}: GradientBackgroundProps) {\n  const variants = {\n    default: 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100',\n    dashboard: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50',\n    auth: 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800'\n  }\n\n  return (\n    <div className={`min-h-screen ${variants[variant]} relative overflow-hidden`}>\n      {/* Static background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl\" />\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-full blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\nexport default GradientBackground\n"], "names": [], "mappings": ";;;;AAAA;;AASA,SAAS,mBAAmB,EAC1B,QAAQ,EACR,UAAU,SAAS,EACK;IACxB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,QAAQ,CAAC,QAAQ,CAAC,yBAAyB,CAAC;;0BAE1E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'gradient'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm hover:shadow-md active:scale-95'\n\n    const variants = {\n      primary: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-200',\n      secondary: 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300',\n      outline: 'border-2 border-blue-600 text-blue-600 bg-transparent hover:bg-blue-50',\n      ghost: 'text-gray-600 hover:bg-gray-100 hover:text-gray-900',\n      destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-200',\n      gradient: 'bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 text-white hover:from-purple-700 hover:via-blue-700 hover:to-teal-700'\n    }\n\n    const sizes = {\n      sm: 'h-9 px-4 text-sm',\n      md: 'h-11 px-6 py-2.5',\n      lg: 'h-13 px-8 text-lg'\n    }\n\n    return (\n      <motion.button\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        disabled={loading}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        )}\n        {children}\n      </motion.button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACL,UAAU;QACT,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;;kCACtG,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <motion.input\n        whileFocus={{ scale: 1.02 }}\n        type={type}\n        className={cn(\n          'flex h-12 w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-sm font-medium ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAIA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;QACX,YAAY;YAAE,OAAO;QAAK;QAC1B,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ubACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Label.tsx"], "sourcesContent": ["import { LabelHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Label = forwardRef<HTMLLabelElement, LabelHTMLAttributes<HTMLLabelElement>>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = 'Label'\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Select.tsx"], "sourcesContent": ["import { SelectHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <select\n        className={cn(\n          'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </select>\n    )\n  }\n)\nSelect.displayName = 'Select'\n\nexport { Select }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2PACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = 'Textarea'\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACxB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Card.tsx"], "sourcesContent": ["'use client'\n\nimport { HTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      ref={ref}\n      className={cn(\n        'rounded-2xl border border-gray-100 bg-white shadow-lg shadow-gray-100/50 backdrop-blur-sm hover:shadow-xl hover:shadow-gray-200/50 transition-all duration-300',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-2 p-8 pb-4', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-xl font-bold leading-none tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-gray-500 leading-relaxed', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('px-8 pb-8', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center px-8 pb-8 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0HAA0H;QACvI,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAa,GAAG,KAAK;;;;;;AAGnE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/assets.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Asset, Database } from '@/lib/types/database'\n\ntype AssetInsert = Database['public']['Tables']['assets']['Insert']\ntype AssetUpdate = Database['public']['Tables']['assets']['Update']\n\nexport async function getAssets() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getAsset(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createAsset(asset: AssetInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('assets')\n    .insert({\n      ...asset,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateAsset(id: string, updates: AssetUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteAsset(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('assets')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getAssetCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'asset')\n    .order('name')\n\n  return { data, error }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,SAAS,EAAU;IACvC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,KAAkB;IAClD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;QACN,GAAG,KAAK;QACR,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU,EAAE,OAAoB;IAChE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,UACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,SACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/liabilities.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Liability, Database } from '@/lib/types/database'\n\ntype LiabilityInsert = Database['public']['Tables']['liabilities']['Insert']\ntype LiabilityUpdate = Database['public']['Tables']['liabilities']['Update']\n\nexport async function getLiabilities() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getLiability(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createLiability(liability: LiabilityInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('liabilities')\n    .insert({\n      ...liability,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateLiability(id: string, updates: LiabilityUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteLiability(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('liabilities')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getLiabilityCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'liability')\n    .order('name')\n\n  return { data, error }\n}\n\n// Utility functions for loan calculations\nexport function calculateMonthlyPayment(\n  principal: number,\n  annualRate: number,\n  termInMonths: number\n): number {\n  if (annualRate === 0) return principal / termInMonths\n  \n  const monthlyRate = annualRate / 100 / 12\n  const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) / \n                  (Math.pow(1 + monthlyRate, termInMonths) - 1)\n  \n  return payment\n}\n\nexport function calculateInterestAccrued(\n  principal: number,\n  annualRate: number,\n  daysElapsed: number\n): number {\n  const dailyRate = annualRate / 100 / 365\n  return principal * dailyRate * daysElapsed\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,SAA0B;IAC9D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC;QACN,GAAG,SAAS;QACZ,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,EAAU,EAAE,OAAwB;IACxE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,EAAU;IAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,aACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,SAAS,wBACd,SAAiB,EACjB,UAAkB,EAClB,YAAoB;IAEpB,IAAI,eAAe,GAAG,OAAO,YAAY;IAEzC,MAAM,cAAc,aAAa,MAAM;IACvC,MAAM,UAAU,YAAY,CAAC,cAAc,KAAK,GAAG,CAAC,IAAI,aAAa,aAAa,IAClE,CAAC,KAAK,GAAG,CAAC,IAAI,aAAa,gBAAgB,CAAC;IAE5D,OAAO;AACT;AAEO,SAAS,yBACd,SAAiB,EACjB,UAAkB,EAClB,WAAmB;IAEnB,MAAM,YAAY,aAAa,MAAM;IACrC,OAAO,YAAY,YAAY;AACjC", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/receivables.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Receivable, Database } from '@/lib/types/database'\n\ntype ReceivableInsert = Database['public']['Tables']['receivables']['Insert']\ntype ReceivableUpdate = Database['public']['Tables']['receivables']['Update']\n\nexport async function getReceivables() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getReceivable(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createReceivable(receivable: ReceivableInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('receivables')\n    .insert({\n      ...receivable,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateReceivable(id: string, updates: ReceivableUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteReceivable(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('receivables')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getReceivableCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'receivable')\n    .order('name')\n\n  return { data, error }\n}\n\n// Utility functions for receivable calculations\nexport function calculateTotalWithInterest(\n  principal: number,\n  annualRate: number,\n  daysElapsed: number\n): number {\n  if (annualRate === 0) return principal\n  \n  const dailyRate = annualRate / 100 / 365\n  const interest = principal * dailyRate * daysElapsed\n  return principal + interest\n}\n\nexport function getDaysOverdue(dueDate: string): number {\n  const due = new Date(dueDate)\n  const today = new Date()\n  const diffTime = today.getTime() - due.getTime()\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return Math.max(0, diffDays)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,cAAc,EAAU;IAC5C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,UAA4B;IACjE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC;QACN,GAAG,UAAU;QACb,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,EAAU,EAAE,OAAyB;IAC1E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,EAAU;IAC/C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,cACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,SAAS,2BACd,SAAiB,EACjB,UAAkB,EAClB,WAAmB;IAEnB,IAAI,eAAe,GAAG,OAAO;IAE7B,MAAM,YAAY,aAAa,MAAM;IACrC,MAAM,WAAW,YAAY,YAAY;IACzC,OAAO,YAAY;AACrB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,QAAQ,IAAI;IAClB,MAAM,WAAW,MAAM,OAAO,KAAK,IAAI,OAAO;IAC9C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,KAAK,GAAG,CAAC,GAAG;AACrB", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/transactions.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport { createAsset, updateAsset } from './assets'\nimport { createLiability, updateLiability } from './liabilities'\nimport { createReceivable, updateReceivable } from './receivables'\n\nexport interface Transaction {\n  id: string\n  user_id: string\n  category_id: string | null\n  type: 'income' | 'expense' | 'transfer'\n  amount: number\n  description: string | null\n  transaction_date: string\n  currency: string\n  created_at: string\n  updated_at: string\n  categories?: {\n    id: string\n    name: string\n    color: string\n  }\n}\n\ntype TransactionInsert = {\n  category_id?: string | null\n  type: 'income' | 'expense' | 'transfer'\n  amount: number\n  description?: string | null\n  transaction_date: string\n  currency?: string\n}\n\nexport async function getTransactions() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('transactions')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('transaction_date', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function createTransaction(transaction: TransactionInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('transactions')\n    .insert({\n      ...transaction,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateTransaction(id: string, updates: Partial<TransactionInsert>) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('transactions')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteTransaction(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('transactions')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\n// Process transaction and update relevant financial records\nexport async function processTransaction(transactionData: TransactionInsert & {\n  updateAssets?: boolean\n  updateLiabilities?: boolean\n  updateReceivables?: boolean\n  assetName?: string\n  liabilityName?: string\n  receivableName?: string\n  debtorName?: string\n}) {\n  // Extract only the transaction fields for database insertion\n  const {\n    updateAssets,\n    updateLiabilities,\n    updateReceivables,\n    assetName,\n    liabilityName,\n    receivableName,\n    debtorName,\n    ...transactionFields\n  } = transactionData\n\n  const { data: transaction, error: transactionError } = await createTransaction(transactionFields)\n  \n  if (transactionError || !transaction) {\n    return { data: null, error: transactionError }\n  }\n\n  // If it's an income transaction and should update assets\n  if (transactionData.type === 'income' && transactionData.updateAssets && transactionData.assetName) {\n    await createAsset({\n      name: transactionData.assetName,\n      current_value: transactionData.amount,\n      asset_type: 'cash',\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Income'}`\n    })\n  }\n\n  // If it's an expense transaction and should update liabilities\n  if (transactionData.type === 'expense' && transactionData.updateLiabilities && transactionData.liabilityName) {\n    await createLiability({\n      name: transactionData.liabilityName,\n      principal_amount: transactionData.amount,\n      current_balance: transactionData.amount,\n      liability_type: 'other',\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Expense'}`\n    })\n  }\n\n  // If it's a transfer and should update receivables\n  if (transactionData.type === 'transfer' && transactionData.updateReceivables && transactionData.receivableName && transactionData.debtorName) {\n    await createReceivable({\n      debtor_name: transactionData.debtorName,\n      principal_amount: transactionData.amount,\n      current_balance: transactionData.amount,\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Transfer'}`\n    })\n  }\n\n  return { data: transaction, error: null }\n}\n\nexport async function getTransactionSummary(startDate?: string, endDate?: string) {\n  const supabase = createClient()\n  \n  let query = supabase\n    .from('transactions')\n    .select('type, amount, currency')\n\n  if (startDate) {\n    query = query.gte('transaction_date', startDate)\n  }\n  if (endDate) {\n    query = query.lte('transaction_date', endDate)\n  }\n\n  const { data, error } = await query\n\n  if (error || !data) {\n    return { data: null, error }\n  }\n\n  const summary = data.reduce((acc, transaction) => {\n    const { type, amount } = transaction\n    if (!acc[type]) {\n      acc[type] = 0\n    }\n    acc[type] += amount\n    return acc\n  }, {} as Record<string, number>)\n\n  return { \n    data: {\n      income: summary.income || 0,\n      expense: summary.expense || 0,\n      transfer: summary.transfer || 0,\n      netIncome: (summary.income || 0) - (summary.expense || 0)\n    }, \n    error: null \n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AA6BO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,oBAAoB;QAAE,WAAW;IAAM;IAEhD,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,WAA8B;IACpE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC;QACN,GAAG,WAAW;QACd,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,EAAU,EAAE,OAAmC;IACrF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,EAAU;IAChD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,gBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAGO,eAAe,mBAAmB,eAQxC;IACC,6DAA6D;IAC7D,MAAM,EACJ,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,GAAG,mBACJ,GAAG;IAEJ,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,kBAAkB;IAE/E,IAAI,oBAAoB,CAAC,aAAa;QACpC,OAAO;YAAE,MAAM;YAAM,OAAO;QAAiB;IAC/C;IAEA,yDAAyD;IACzD,IAAI,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,YAAY,IAAI,gBAAgB,SAAS,EAAE;QAClG,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;YAChB,MAAM,gBAAgB,SAAS;YAC/B,eAAe,gBAAgB,MAAM;YACrC,YAAY;YACZ,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,UAAU;QAC7E;IACF;IAEA,+DAA+D;IAC/D,IAAI,gBAAgB,IAAI,KAAK,aAAa,gBAAgB,iBAAiB,IAAI,gBAAgB,aAAa,EAAE;QAC5G,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;YACpB,MAAM,gBAAgB,aAAa;YACnC,kBAAkB,gBAAgB,MAAM;YACxC,iBAAiB,gBAAgB,MAAM;YACvC,gBAAgB;YAChB,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,WAAW;QAC9E;IACF;IAEA,mDAAmD;IACnD,IAAI,gBAAgB,IAAI,KAAK,cAAc,gBAAgB,iBAAiB,IAAI,gBAAgB,cAAc,IAAI,gBAAgB,UAAU,EAAE;QAC5I,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;YACrB,aAAa,gBAAgB,UAAU;YACvC,kBAAkB,gBAAgB,MAAM;YACxC,iBAAiB,gBAAgB,MAAM;YACvC,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,YAAY;QAC/E;IACF;IAEA,OAAO;QAAE,MAAM;QAAa,OAAO;IAAK;AAC1C;AAEO,eAAe,sBAAsB,SAAkB,EAAE,OAAgB;IAC9E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,WAAW;QACb,QAAQ,MAAM,GAAG,CAAC,oBAAoB;IACxC;IACA,IAAI,SAAS;QACX,QAAQ,MAAM,GAAG,CAAC,oBAAoB;IACxC;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,SAAS,CAAC,MAAM;QAClB,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,KAAK;QAChC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QACzB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACd,GAAG,CAAC,KAAK,GAAG;QACd;QACA,GAAG,CAAC,KAAK,IAAI;QACb,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QACL,MAAM;YACJ,QAAQ,QAAQ,MAAM,IAAI;YAC1B,SAAS,QAAQ,OAAO,IAAI;YAC5B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC;QAC1D;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/categories.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Category, Database } from '@/lib/types/database'\n\ntype CategoryInsert = Database['public']['Tables']['categories']['Insert']\ntype CategoryUpdate = Database['public']['Tables']['categories']['Update']\n\nexport async function getCategories(type?: string) {\n  const supabase = createClient()\n  \n  let query = supabase\n    .from('categories')\n    .select('*')\n    .order('name')\n\n  if (type) {\n    query = query.eq('type', type)\n  }\n\n  const { data, error } = await query\n\n  return { data, error }\n}\n\nexport async function getCategory(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createCategory(category: CategoryInsert) {\n  const supabase = createClient()\n  \n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) throw new Error('User not authenticated')\n\n  const { data, error } = await supabase\n    .from('categories')\n    .insert({\n      ...category,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateCategory(id: string, updates: CategoryUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteCategory(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('categories')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\n// Create default categories for new users\nexport async function createDefaultCategories() {\n  const defaultCategories = [\n    // Income categories\n    { name: 'Salary', type: 'income', color: '#10B981' },\n    { name: 'Business Income', type: 'income', color: '#059669' },\n    { name: 'Investment Returns', type: 'income', color: '#047857' },\n    { name: 'Other Income', type: 'income', color: '#065F46' },\n    \n    // Expense categories\n    { name: 'Food & Dining', type: 'expense', color: '#EF4444' },\n    { name: 'Transportation', type: 'expense', color: '#DC2626' },\n    { name: 'Shopping', type: 'expense', color: '#B91C1C' },\n    { name: 'Entertainment', type: 'expense', color: '#991B1B' },\n    { name: 'Bills & Utilities', type: 'expense', color: '#7F1D1D' },\n    { name: 'Healthcare', type: 'expense', color: '#F59E0B' },\n    { name: 'Education', type: 'expense', color: '#D97706' },\n    { name: 'Travel', type: 'expense', color: '#B45309' },\n    { name: 'Other Expenses', type: 'expense', color: '#92400E' },\n  ]\n\n  const results = []\n  for (const category of defaultCategories) {\n    const result = await createCategory(category as CategoryInsert)\n    results.push(result)\n  }\n\n  return results\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAMO,eAAe,cAAc,IAAa;IAC/C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,IAAI,MAAM;QACR,QAAQ,MAAM,EAAE,CAAC,QAAQ;IAC3B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,QAAwB;IAC3D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC;QACN,GAAG,QAAQ;QACX,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,EAAU,EAAE,OAAuB;IACtE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAGO,eAAe;IACpB,MAAM,oBAAoB;QACxB,oBAAoB;QACpB;YAAE,MAAM;YAAU,MAAM;YAAU,OAAO;QAAU;QACnD;YAAE,MAAM;YAAmB,MAAM;YAAU,OAAO;QAAU;QAC5D;YAAE,MAAM;YAAsB,MAAM;YAAU,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAgB,MAAM;YAAU,OAAO;QAAU;QAEzD,qBAAqB;QACrB;YAAE,MAAM;YAAiB,MAAM;YAAW,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAkB,MAAM;YAAW,OAAO;QAAU;QAC5D;YAAE,MAAM;YAAY,MAAM;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAiB,MAAM;YAAW,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAqB,MAAM;YAAW,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAc,MAAM;YAAW,OAAO;QAAU;QACxD;YAAE,MAAM;YAAa,MAAM;YAAW,OAAO;QAAU;QACvD;YAAE,MAAM;YAAU,MAAM;YAAW,OAAO;QAAU;QACpD;YAAE,MAAM;YAAkB,MAAM;YAAW,OAAO;QAAU;KAC7D;IAED,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,YAAY,kBAAmB;QACxC,MAAM,SAAS,MAAM,eAAe;QACpC,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/transactions/QuickTransactionForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Label } from '@/components/ui/Label'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { processTransaction } from '@/lib/api/transactions'\nimport { getCategories } from '@/lib/api/categories'\nimport type { Category } from '@/lib/types/database'\n\ninterface QuickTransactionFormProps {\n  onSuccess: () => void\n  onCancel: () => void\n  defaultType?: 'income' | 'expense' | 'transfer'\n}\n\nexport default function QuickTransactionForm({ \n  onSuccess, \n  onCancel, \n  defaultType = 'expense' \n}: QuickTransactionFormProps) {\n  const [loading, setLoading] = useState(false)\n  const [categories, setCategories] = useState<Category[]>([])\n  const [formData, setFormData] = useState({\n    type: defaultType,\n    amount: '',\n    description: '',\n    category_id: '',\n    transaction_date: new Date().toISOString().split('T')[0],\n    currency: 'LKR',\n    // Advanced options\n    updateAssets: false,\n    updateLiabilities: false,\n    updateReceivables: false,\n    assetName: '',\n    liabilityName: '',\n    receivableName: '',\n    debtorName: ''\n  })\n\n  useEffect(() => {\n    loadCategories()\n  }, [formData.type])\n\n  const loadCategories = async () => {\n    const { data } = await getCategories(formData.type)\n    if (data) setCategories(data)\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const result = await processTransaction({\n        type: formData.type as 'income' | 'expense' | 'transfer',\n        amount: parseFloat(formData.amount),\n        description: formData.description || null,\n        category_id: formData.category_id || null,\n        transaction_date: formData.transaction_date,\n        currency: formData.currency,\n        updateAssets: formData.updateAssets,\n        updateLiabilities: formData.updateLiabilities,\n        updateReceivables: formData.updateReceivables,\n        assetName: formData.assetName,\n        liabilityName: formData.liabilityName,\n        receivableName: formData.receivableName,\n        debtorName: formData.debtorName\n      })\n\n      if (result.error) {\n        console.error('Error creating transaction:', result.error)\n        alert('Error creating transaction: ' + result.error.message)\n        return\n      }\n\n      onSuccess()\n    } catch (error) {\n      console.error('Error creating transaction:', error)\n      alert('Error creating transaction: ' + (error as Error).message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const transactionTypes = [\n    { value: 'income', label: 'Income', icon: Plus, color: 'text-green-600', bg: 'bg-green-50' },\n    { value: 'expense', label: 'Expense', icon: Minus, color: 'text-red-600', bg: 'bg-red-50' },\n    { value: 'transfer', label: 'Transfer', icon: ArrowRightLeft, color: 'text-blue-600', bg: 'bg-blue-50' }\n  ]\n\n  const currentType = transactionTypes.find(t => t.value === formData.type)\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.95 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50\"\n    >\n      <Card className=\"w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto\">\n        <CardHeader className=\"flex flex-row items-center justify-between\">\n          <CardTitle className=\"flex items-center\">\n            {currentType && (\n              <div className={`p-2 rounded-xl ${currentType.bg} mr-3`}>\n                <currentType.icon className={`h-5 w-5 ${currentType.color}`} />\n              </div>\n            )}\n            Add New Transaction\n          </CardTitle>\n          <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Transaction Type */}\n            <div>\n              <Label>Transaction Type</Label>\n              <div className=\"grid grid-cols-3 gap-2 sm:gap-3 mt-2\">\n                {transactionTypes.map((type) => (\n                  <button\n                    key={type.value}\n                    type=\"button\"\n                    onClick={() => handleChange('type', type.value)}\n                    className={`\n                      p-3 sm:p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center space-y-1 sm:space-y-2\n                      ${formData.type === type.value\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                      }\n                    `}\n                  >\n                    <type.icon className={`h-5 w-5 sm:h-6 sm:w-6 ${type.color}`} />\n                    <span className=\"font-medium text-xs sm:text-sm\">{type.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\n              {/* Amount */}\n              <div>\n                <Label htmlFor=\"amount\">Amount *</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"amount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={formData.amount}\n                    onChange={(e) => handleChange('amount', e.target.value)}\n                    placeholder=\"0.00\"\n                    required\n                    className=\"pl-12\"\n                  />\n                  <span className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium\">\n                    {formData.currency === 'LKR' ? 'Rs.' : '$'}\n                  </span>\n                </div>\n              </div>\n\n              {/* Currency */}\n              <div>\n                <Label htmlFor=\"currency\">Currency</Label>\n                <Select\n                  id=\"currency\"\n                  value={formData.currency}\n                  onChange={(e) => handleChange('currency', e.target.value)}\n                >\n                  <option value=\"LKR\">LKR (Sri Lankan Rupee)</option>\n                  <option value=\"USD\">USD (US Dollar)</option>\n                </Select>\n              </div>\n            </div>\n\n            {/* Category */}\n            <div>\n              <Label htmlFor=\"category_id\">Category</Label>\n              <Select\n                id=\"category_id\"\n                value={formData.category_id}\n                onChange={(e) => handleChange('category_id', e.target.value)}\n              >\n                <option value=\"\">Select a category</option>\n                {categories.map((category) => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </Select>\n            </div>\n\n            {/* Date */}\n            <div>\n              <Label htmlFor=\"transaction_date\">Date</Label>\n              <Input\n                id=\"transaction_date\"\n                type=\"date\"\n                value={formData.transaction_date}\n                onChange={(e) => handleChange('transaction_date', e.target.value)}\n                required\n              />\n            </div>\n\n            {/* Description */}\n            <div>\n              <Label htmlFor=\"description\">Description</Label>\n              <Textarea\n                id=\"description\"\n                value={formData.description}\n                onChange={(e) => handleChange('description', e.target.value)}\n                placeholder=\"Add a note about this transaction...\"\n                rows={3}\n              />\n            </div>\n\n            {/* Advanced Options */}\n            <div className=\"border-t pt-6\">\n              <h3 className=\"font-semibold text-gray-900 mb-4\">Advanced Options</h3>\n              <div className=\"space-y-4\">\n                {formData.type === 'income' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"updateAssets\"\n                      checked={formData.updateAssets}\n                      onChange={(e) => handleChange('updateAssets', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                    <Label htmlFor=\"updateAssets\">Add to Assets</Label>\n                    {formData.updateAssets && (\n                      <Input\n                        placeholder=\"Asset name\"\n                        value={formData.assetName}\n                        onChange={(e) => handleChange('assetName', e.target.value)}\n                        className=\"flex-1\"\n                      />\n                    )}\n                  </div>\n                )}\n\n                {formData.type === 'expense' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"updateLiabilities\"\n                      checked={formData.updateLiabilities}\n                      onChange={(e) => handleChange('updateLiabilities', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                    <Label htmlFor=\"updateLiabilities\">Add to Liabilities</Label>\n                    {formData.updateLiabilities && (\n                      <Input\n                        placeholder=\"Liability name\"\n                        value={formData.liabilityName}\n                        onChange={(e) => handleChange('liabilityName', e.target.value)}\n                        className=\"flex-1\"\n                      />\n                    )}\n                  </div>\n                )}\n\n                {formData.type === 'transfer' && (\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        id=\"updateReceivables\"\n                        checked={formData.updateReceivables}\n                        onChange={(e) => handleChange('updateReceivables', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <Label htmlFor=\"updateReceivables\">Add to Receivables</Label>\n                    </div>\n                    {formData.updateReceivables && (\n                      <div className=\"grid grid-cols-2 gap-3\">\n                        <Input\n                          placeholder=\"Receivable name\"\n                          value={formData.receivableName}\n                          onChange={(e) => handleChange('receivableName', e.target.value)}\n                        />\n                        <Input\n                          placeholder=\"Debtor name\"\n                          value={formData.debtorName}\n                          onChange={(e) => handleChange('debtorName', e.target.value)}\n                        />\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-6\">\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" loading={loading}>\n                Add Transaction\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAqBe,SAAS,qBAAqB,EAC3C,SAAS,EACT,QAAQ,EACR,cAAc,SAAS,EACG;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;QACb,kBAAkB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,UAAU;QACV,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,mBAAmB;QACnB,WAAW;QACX,eAAe;QACf,gBAAgB;QAChB,YAAY;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,iBAAiB;QACrB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;QAClD,IAAI,MAAM,cAAc;IAC1B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBACtC,MAAM,SAAS,IAAI;gBACnB,QAAQ,WAAW,SAAS,MAAM;gBAClC,aAAa,SAAS,WAAW,IAAI;gBACrC,aAAa,SAAS,WAAW,IAAI;gBACrC,kBAAkB,SAAS,gBAAgB;gBAC3C,UAAU,SAAS,QAAQ;gBAC3B,cAAc,SAAS,YAAY;gBACnC,mBAAmB,SAAS,iBAAiB;gBAC7C,mBAAmB,SAAS,iBAAiB;gBAC7C,WAAW,SAAS,SAAS;gBAC7B,eAAe,SAAS,aAAa;gBACrC,gBAAgB,SAAS,cAAc;gBACvC,YAAY,SAAS,UAAU;YACjC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,+BAA+B,OAAO,KAAK;gBACzD,MAAM,iCAAiC,OAAO,KAAK,CAAC,OAAO;gBAC3D;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,iCAAiC,AAAC,MAAgB,OAAO;QACjE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAkB,IAAI;QAAc;QAC3F;YAAE,OAAO;YAAW,OAAO;YAAW,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAgB,IAAI;QAAY;QAC1F;YAAE,OAAO;YAAY,OAAO;YAAY,MAAM,8NAAA,CAAA,iBAAc;YAAE,OAAO;YAAiB,IAAI;QAAa;KACxG;IAED,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,IAAI;IAExE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAK;QAChC,WAAU;kBAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;gCAClB,6BACC,8OAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC;8CACrD,cAAA,8OAAC,YAAY,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE;;;;;;;;;;;gCAE7D;;;;;;;sCAGJ,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGjB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,QAAQ,KAAK,KAAK;gDAC9C,WAAW,CAAC;;sBAEV,EAAE,SAAS,IAAI,KAAK,KAAK,KAAK,GAC1B,+BACA,wCACH;oBACH,CAAC;;kEAED,8OAAC,KAAK,IAAI;wDAAC,WAAW,CAAC,sBAAsB,EAAE,KAAK,KAAK,EAAE;;;;;;kEAC3D,8OAAC;wDAAK,WAAU;kEAAkC,KAAK,KAAK;;;;;;;+CAZvD,KAAK,KAAK;;;;;;;;;;;;;;;;0CAkBvB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,MAAM;wDACtB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;wDACtD,aAAY;wDACZ,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ,KAAK,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,SAAM;gDACL,IAAG;gDACH,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;;kEAExD,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;;0DAE3D,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oDAAyB,OAAO,SAAS,EAAE;8DACzC,SAAS,IAAI;mDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;0CAQ9B,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;kDAClC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,gBAAgB;wCAChC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAChE,QAAQ;;;;;;;;;;;;0CAKZ,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC3D,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,IAAI,KAAK,0BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,YAAY;wDAC9B,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,OAAO;wDAC9D,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;oDAC7B,SAAS,YAAY,kBACpB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,SAAS;wDACzB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;wDACzD,WAAU;;;;;;;;;;;;4CAMjB,SAAS,IAAI,KAAK,2BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,iBAAiB;wDACnC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,OAAO;wDACnE,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;oDAClC,SAAS,iBAAiB,kBACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAU;;;;;;;;;;;;4CAMjB,SAAS,IAAI,KAAK,4BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS,SAAS,iBAAiB;gEACnC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,OAAO;gEACnE,WAAU;;;;;;0EAEZ,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;;;;;;;oDAEpC,SAAS,iBAAiB,kBACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;0EAEhE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD", "debugId": null}}, {"offset": {"line": 2436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/QuickActionButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'\nimport { Button } from './Button'\nimport QuickTransactionForm from '@/components/transactions/QuickTransactionForm'\n\nexport default function QuickActionButton() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [showForm, setShowForm] = useState(false)\n  const [selectedType, setSelectedType] = useState<'income' | 'expense' | 'transfer'>('expense')\n\n  const quickActions = [\n    { type: 'income' as const, icon: Plus, label: 'Income', color: 'bg-green-500 hover:bg-green-600' },\n    { type: 'expense' as const, icon: Minus, label: 'Expense', color: 'bg-red-500 hover:bg-red-600' },\n    { type: 'transfer' as const, icon: ArrowRightLeft, label: 'Transfer', color: 'bg-blue-500 hover:bg-blue-600' },\n  ]\n\n  const handleActionClick = (type: 'income' | 'expense' | 'transfer') => {\n    setSelectedType(type)\n    setShowForm(true)\n    setIsOpen(false)\n  }\n\n  const handleFormSuccess = () => {\n    setShowForm(false)\n    // Optionally trigger a refresh of the current page data\n    window.location.reload()\n  }\n\n  return (\n    <>\n      <div className=\"fixed bottom-6 right-6 z-40\">\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              className=\"absolute bottom-16 right-0 space-y-3\"\n            >\n              {quickActions.map((action, index) => (\n                <motion.button\n                  key={action.type}\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  transition={{ delay: index * 0.1 }}\n                  onClick={() => handleActionClick(action.type)}\n                  className={`\n                    flex items-center space-x-3 px-4 py-3 rounded-xl text-white font-medium\n                    shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\n                    ${action.color}\n                  `}\n                >\n                  <action.icon className=\"h-5 w-5\" />\n                  <span className=\"whitespace-nowrap\">{action.label}</span>\n                </motion.button>\n              ))}\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        <motion.button\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          onClick={() => setIsOpen(!isOpen)}\n          className={`\n            w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200\n            flex items-center justify-center text-white font-bold text-xl\n            ${isOpen \n              ? 'bg-gray-500 hover:bg-gray-600' \n              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'\n            }\n          `}\n        >\n          <motion.div\n            animate={{ rotate: isOpen ? 45 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            {isOpen ? <X className=\"h-6 w-6\" /> : <Plus className=\"h-6 w-6\" />}\n          </motion.div>\n        </motion.button>\n      </div>\n\n      <AnimatePresence>\n        {showForm && (\n          <QuickTransactionForm\n            defaultType={selectedType}\n            onSuccess={handleFormSuccess}\n            onCancel={() => setShowForm(false)}\n          />\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAEpF,MAAM,eAAe;QACnB;YAAE,MAAM;YAAmB,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAU,OAAO;QAAkC;QACjG;YAAE,MAAM;YAAoB,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAW,OAAO;QAA8B;QAChG;YAAE,MAAM;YAAqB,MAAM,8NAAA,CAAA,iBAAc;YAAE,OAAO;YAAY,OAAO;QAAgC;KAC9G;IAED,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,wDAAwD;QACxD,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;sCAET,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,IAAM,kBAAkB,OAAO,IAAI;oCAC5C,WAAW,CAAC;;;oBAGV,EAAE,OAAO,KAAK,CAAC;kBACjB,CAAC;;sDAED,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAqB,OAAO,KAAK;;;;;;;mCAb5C,OAAO,IAAI;;;;;;;;;;;;;;;kCAoB1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS,IAAM,UAAU,CAAC;wBAC1B,WAAW,CAAC;;;YAGV,EAAE,SACE,kCACA,uFACH;UACH,CAAC;kCAED,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ,SAAS,KAAK;4BAAE;4BACnC,YAAY;gCAAE,UAAU;4BAAI;sCAE3B,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAK5D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,0BACC,8OAAC,0JAAA,CAAA,UAAoB;oBACnB,aAAa;oBACb,WAAW;oBACX,UAAU,IAAM,YAAY;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 2635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport MobileSidebar from './MobileSidebar'\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\nimport GradientBackground from '@/components/ui/GradientBackground'\nimport QuickActionButton from '@/components/ui/QuickActionButton'\nimport { Menu } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <ProtectedRoute>\n      <GradientBackground variant=\"dashboard\">\n        <div className=\"flex h-screen\">\n          {/* Desktop Sidebar */}\n          <div className=\"hidden lg:block\">\n            <Sidebar />\n          </div>\n\n          {/* Mobile Sidebar */}\n          <MobileSidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n\n          {/* Main Content */}\n          <main className=\"flex-1 overflow-auto\">\n            {/* Mobile Header */}\n            <div className=\"lg:hidden bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 py-3 flex items-center justify-between\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\"\n              >\n                <Menu className=\"h-6 w-6\" />\n              </button>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">W</span>\n                </div>\n                <h1 className=\"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\">\n                  Wealth Manager\n                </h1>\n              </div>\n            </div>\n\n            {/* Page Content */}\n            <div className=\"p-4 sm:p-6 lg:p-8\">\n              {children}\n            </div>\n          </main>\n\n          {/* Quick Action Button - Hidden on mobile */}\n          <div className=\"hidden sm:block\">\n            <QuickActionButton />\n          </div>\n        </div>\n      </GradientBackground>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC,4IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,UAAkB;YAAC,SAAQ;sBAC1B,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;kCAIV,8OAAC,6IAAA,CAAA,UAAa;wBAAC,QAAQ;wBAAa,SAAS,IAAM,eAAe;;;;;;kCAGlE,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAG,WAAU;0DAA6F;;;;;;;;;;;;;;;;;;0CAO/G,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6IAAA,CAAA,UAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 2791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/categories/CategoryForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Label } from '@/components/ui/Label'\nimport { Select } from '@/components/ui/Select'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { createCategory, updateCategory } from '@/lib/api/categories'\nimport type { Category } from '@/lib/types/database'\n\ninterface CategoryFormProps {\n  category?: Category\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nconst colorOptions = [\n  { value: '#EF4444', label: 'Red', bg: 'bg-red-500' },\n  { value: '#F59E0B', label: 'Orange', bg: 'bg-orange-500' },\n  { value: '#EAB308', label: 'Yellow', bg: 'bg-yellow-500' },\n  { value: '#10B981', label: 'Green', bg: 'bg-green-500' },\n  { value: '#3B82F6', label: 'Blue', bg: 'bg-blue-500' },\n  { value: '#8B5CF6', label: 'Purple', bg: 'bg-purple-500' },\n  { value: '#EC4899', label: 'Pink', bg: 'bg-pink-500' },\n  { value: '#6B7280', label: 'Gray', bg: 'bg-gray-500' },\n]\n\nexport default function CategoryForm({ category, onSuccess, onCancel }: CategoryFormProps) {\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: category?.name || '',\n    type: category?.type || 'expense' as const,\n    color: category?.color || '#3B82F6',\n  })\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      if (category) {\n        await updateCategory(category.id, formData)\n      } else {\n        await createCategory(formData)\n      }\n      onSuccess()\n    } catch (error) {\n      console.error('Error saving category:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.95 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\"\n    >\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <div \n              className=\"w-4 h-4 rounded-full mr-3\"\n              style={{ backgroundColor: formData.color }}\n            />\n            {category ? 'Edit Category' : 'Add New Category'}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <Label htmlFor=\"name\">Category Name *</Label>\n              <Input\n                id=\"name\"\n                value={formData.name}\n                onChange={(e) => handleChange('name', e.target.value)}\n                placeholder=\"Enter category name\"\n                required\n              />\n            </div>\n            \n            <div>\n              <Label htmlFor=\"type\">Category Type *</Label>\n              <Select\n                id=\"type\"\n                value={formData.type}\n                onChange={(e) => handleChange('type', e.target.value)}\n                required\n              >\n                <option value=\"income\">Income</option>\n                <option value=\"expense\">Expense</option>\n                <option value=\"asset\">Asset</option>\n                <option value=\"liability\">Liability</option>\n                <option value=\"receivable\">Receivable</option>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"color\">Color</Label>\n              <div className=\"grid grid-cols-4 gap-3 mt-2\">\n                {colorOptions.map((color) => (\n                  <button\n                    key={color.value}\n                    type=\"button\"\n                    onClick={() => handleChange('color', color.value)}\n                    className={`\n                      w-full h-12 rounded-xl ${color.bg} flex items-center justify-center\n                      ${formData.color === color.value \n                        ? 'ring-4 ring-blue-500 ring-offset-2' \n                        : 'hover:scale-105'\n                      }\n                      transition-all duration-200\n                    `}\n                  >\n                    {formData.color === color.value && (\n                      <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    )}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" loading={loading}>\n                {category ? 'Update Category' : 'Create Category'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAkBA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAW,OAAO;QAAO,IAAI;IAAa;IACnD;QAAE,OAAO;QAAW,OAAO;QAAU,IAAI;IAAgB;IACzD;QAAE,OAAO;QAAW,OAAO;QAAU,IAAI;IAAgB;IACzD;QAAE,OAAO;QAAW,OAAO;QAAS,IAAI;IAAe;IACvD;QAAE,OAAO;QAAW,OAAO;QAAQ,IAAI;IAAc;IACrD;QAAE,OAAO;QAAW,OAAO;QAAU,IAAI;IAAgB;IACzD;QAAE,OAAO;QAAW,OAAO;QAAQ,IAAI;IAAc;IACrD;QAAE,OAAO;QAAW,OAAO;QAAQ,IAAI;IAAc;CACtD;AAEc,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAqB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,UAAU,QAAQ;QACxB,MAAM,UAAU,QAAQ;QACxB,OAAO,UAAU,SAAS;IAC5B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,EAAE,EAAE;YACpC,OAAO;gBACL,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;YACvB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAK;QAChC,WAAU;kBAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;;;;;;4BAE1C,WAAW,kBAAkB;;;;;;;;;;;;8BAGlC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,aAAa,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACpD,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,aAAa,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACpD,QAAQ;;0DAER,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;;;;;;;;;;;;;0CAI/B,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,SAAS,MAAM,KAAK;gDAChD,WAAW,CAAC;6CACa,EAAE,MAAM,EAAE,CAAC;sBAClC,EAAE,SAAS,KAAK,KAAK,MAAM,KAAK,GAC5B,uCACA,kBACH;;oBAEH,CAAC;0DAEA,SAAS,KAAK,KAAK,MAAM,KAAK,kBAC7B,8OAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAe,SAAQ;8DAC9D,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;+CAdxJ,MAAM,KAAK;;;;;;;;;;;;;;;;0CAsBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAC5B,WAAW,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 3138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/categories/CategoryList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Edit, Trash2, Plus, Tag } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { getCategories, deleteCategory } from '@/lib/api/categories'\nimport CategoryForm from './CategoryForm'\nimport type { Category } from '@/lib/types/database'\n\nexport default function CategoryList() {\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [editingCategory, setEditingCategory] = useState<Category | undefined>()\n  const [selectedType, setSelectedType] = useState<string>('all')\n\n  useEffect(() => {\n    loadCategories()\n  }, [])\n\n  const loadCategories = async () => {\n    setLoading(true)\n    const { data } = await getCategories()\n    if (data) setCategories(data)\n    setLoading(false)\n  }\n\n  const handleDelete = async (id: string) => {\n    if (confirm('Are you sure you want to delete this category?')) {\n      await deleteCategory(id)\n      loadCategories()\n    }\n  }\n\n  const handleEdit = (category: Category) => {\n    setEditingCategory(category)\n    setShowForm(true)\n  }\n\n  const handleFormSuccess = () => {\n    setShowForm(false)\n    setEditingCategory(undefined)\n    loadCategories()\n  }\n\n  const handleFormCancel = () => {\n    setShowForm(false)\n    setEditingCategory(undefined)\n  }\n\n  const filteredCategories = selectedType === 'all' \n    ? categories \n    : categories.filter(cat => cat.type === selectedType)\n\n  const categoryTypes = [\n    { value: 'all', label: 'All Categories', count: categories.length },\n    { value: 'income', label: 'Income', count: categories.filter(c => c.type === 'income').length },\n    { value: 'expense', label: 'Expense', count: categories.filter(c => c.type === 'expense').length },\n    { value: 'asset', label: 'Asset', count: categories.filter(c => c.type === 'asset').length },\n    { value: 'liability', label: 'Liability', count: categories.filter(c => c.type === 'liability').length },\n    { value: 'receivable', label: 'Receivable', count: categories.filter(c => c.type === 'receivable').length },\n  ]\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\">\n            Categories\n          </h1>\n          <p className=\"text-gray-600 mt-2\">Organize your financial transactions with custom categories</p>\n        </div>\n        <Button onClick={() => setShowForm(true)} variant=\"gradient\">\n          <Plus className=\"h-5 w-5 mr-2\" />\n          Add Category\n        </Button>\n      </div>\n\n      {/* Category Type Filter */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-wrap gap-3\">\n            {categoryTypes.map((type) => (\n              <button\n                key={type.value}\n                onClick={() => setSelectedType(type.value)}\n                className={`\n                  px-4 py-2 rounded-xl font-medium transition-all duration-200\n                  ${selectedType === type.value\n                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }\n                `}\n              >\n                {type.label} ({type.count})\n              </button>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Categories Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <AnimatePresence>\n          {loading ? (\n            Array.from({ length: 6 }).map((_, i) => (\n              <Card key={i} className=\"animate-pulse\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-4 h-4 bg-gray-300 rounded-full\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded flex-1\"></div>\n                  </div>\n                  <div className=\"mt-4 h-3 bg-gray-300 rounded w-1/2\"></div>\n                </CardContent>\n              </Card>\n            ))\n          ) : filteredCategories.length === 0 ? (\n            <div className=\"col-span-full text-center py-12\">\n              <Tag className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No categories found</h3>\n              <p className=\"text-gray-500 mb-6\">\n                {selectedType === 'all' \n                  ? 'Create your first category to get started'\n                  : `No ${selectedType} categories found`\n                }\n              </p>\n              <Button onClick={() => setShowForm(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Category\n              </Button>\n            </div>\n          ) : (\n            filteredCategories.map((category, index) => (\n              <motion.div\n                key={category.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: -20 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Card className=\"hover:shadow-xl transition-all duration-300 group\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div \n                          className=\"w-4 h-4 rounded-full\"\n                          style={{ backgroundColor: category.color }}\n                        />\n                        <h3 className=\"font-semibold text-gray-900\">{category.name}</h3>\n                      </div>\n                      <div className=\"flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleEdit(category)}\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDelete(category.id)}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-500 capitalize\">\n                      {category.type.replace('_', ' ')}\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))\n          )}\n        </AnimatePresence>\n      </div>\n\n      {/* Category Form Modal */}\n      <AnimatePresence>\n        {showForm && (\n          <CategoryForm\n            category={editingCategory}\n            onSuccess={handleFormSuccess}\n            onCancel={handleFormCancel}\n          />\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,WAAW;QACX,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;QACnC,IAAI,MAAM,cAAc;QACxB,WAAW;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,mDAAmD;YAC7D,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE;YACrB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,mBAAmB;QACnB;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,iBAAiB,QACxC,aACA,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;IAE1C,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;YAAkB,OAAO,WAAW,MAAM;QAAC;QAClE;YAAE,OAAO;YAAU,OAAO;YAAU,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;QAAC;QAC9F;YAAE,OAAO;YAAW,OAAO;YAAW,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;QAAC;QACjG;YAAE,OAAO;YAAS,OAAO;YAAS,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;QAAC;QAC3F;YAAE,OAAO;YAAa,OAAO;YAAa,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QAAC;QACvG;YAAE,OAAO;YAAc,OAAO;YAAc,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,MAAM;QAAC;KAC3G;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA8F;;;;;;0CAG5G,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,YAAY;wBAAO,SAAQ;;0CAChD,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAEC,SAAS,IAAM,gBAAgB,KAAK,KAAK;gCACzC,WAAW,CAAC;;kBAEV,EAAE,iBAAiB,KAAK,KAAK,GACzB,sEACA,8CACH;gBACH,CAAC;;oCAEA,KAAK,KAAK;oCAAC;oCAAG,KAAK,KAAK;oCAAC;;+BAVrB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;0BAkBzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;8BACb,UACC,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;sCACtB,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BANR;;;;oCAUX,mBAAmB,MAAM,KAAK,kBAChC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,QACd,8CACA,CAAC,GAAG,EAAE,aAAa,iBAAiB,CAAC;;;;;;0CAG3C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,YAAY;;kDACjC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;+BAKrC,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;sCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,SAAS,KAAK;4DAAC;;;;;;sEAE3C,8OAAC;4DAAG,WAAU;sEAA+B,SAAS,IAAI;;;;;;;;;;;;8DAE5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,WAAW;sEAE1B,cAAA,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,aAAa,SAAS,EAAE;sEAEvC,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;sDACZ,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;2BAlC7B,SAAS,EAAE;;;;;;;;;;;;;;;0BA6C1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,0BACC,8OAAC,gJAAA,CAAA,UAAY;oBACX,UAAU;oBACV,WAAW;oBACX,UAAU;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}]}