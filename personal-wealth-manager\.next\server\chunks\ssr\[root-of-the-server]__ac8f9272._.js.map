{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\n\nexport async function signUp(email: string, password: string, fullName: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        full_name: fullName,\n      },\n    },\n  })\n\n  return { data, error }\n}\n\nexport async function signIn(email: string, password: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  })\n\n  return { data, error }\n}\n\nexport async function signOut() {\n  const supabase = createClient()\n  \n  const { error } = await supabase.auth.signOut()\n  \n  return { error }\n}\n\nexport async function getUser() {\n  const supabase = createClient()\n\n  const { data: { user }, error } = await supabase.auth.getUser()\n\n  return { user, error }\n}\n\nexport async function getProfile() {\n  const supabase = createClient()\n\n  const { data: { user }, error: userError } = await supabase.auth.getUser()\n\n  if (userError || !user) {\n    return { profile: null, error: userError }\n  }\n\n  const { data: profile, error: profileError } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n\n  return { profile, error: profileError }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAgB;IAC5E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB;IAC1D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7C,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7D,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAM,OAAO;QAAU;IAC3C;IAEA,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,OAAO;QAAE;QAAS,OAAO;IAAa;AACxC", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'LKR'): string {\n  if (currency === 'LKR') {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'LKR',\n      currencyDisplay: 'symbol',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(amount).replace('LKR', 'Rs.')\n  }\n\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  })\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return 0\n  return ((current - previous) / previous) * 100\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,IAAI,aAAa,OAAO;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,QAAQ,OAAO,CAAC,OAAO;IACnC;IAEA,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  LayoutDashboard,\n  TrendingUp,\n  CreditCard,\n  Users,\n  Tag,\n  Receipt,\n  Target,\n  BarChart3,\n  Settings,\n  LogOut\n} from 'lucide-react'\nimport { signOut } from '@/lib/auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Transactions', href: '/transactions', icon: Receipt },\n  { name: 'Budget', href: '/budget', icon: Target },\n  { name: 'Reports', href: '/reports', icon: BarChart3 },\n  { name: 'Assets', href: '/assets', icon: TrendingUp },\n  { name: 'Liabilities', href: '/liabilities', icon: CreditCard },\n  { name: 'Receivables', href: '/receivables', icon: Users },\n  { name: 'Categories', href: '/categories', icon: Tag },\n  { name: 'Settings', href: '/settings', icon: Settings },\n]\n\nexport default function Sidebar() {\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n    window.location.href = '/login'\n  }\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl\">\n      <div className=\"flex h-20 items-center px-6 border-b border-gray-200/50\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">W</span>\n          </div>\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\">\n            Wealth Manager\n          </h1>\n        </div>\n      </div>\n      \n      <nav className=\"flex-1 space-y-2 px-4 py-6\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 hover:scale-105 active:scale-95',\n                isActive\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25'\n                  : 'text-gray-700 hover:bg-white/60 hover:text-gray-900 hover:shadow-md'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n      \n      <div className=\"border-t border-gray-200 p-3\">\n        <button\n          onClick={handleSignOut}\n          className=\"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors\"\n        >\n          <LogOut className=\"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\" />\n          Sign out\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAjBA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC7D;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,sMAAA,CAAA,SAAM;IAAC;IAChD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kNAAA,CAAA,YAAS;IAAC;IACrD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,kNAAA,CAAA,aAAU;IAAC;IACpD;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC9D;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAc,MAAM;QAAe,MAAM,gMAAA,CAAA,MAAG;IAAC;IACrD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACvD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAG,WAAU;sCAA6F;;;;;;;;;;;;;;;;;0BAM/G,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kIACA,WACI,yFACA;;0CAGN,8OAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,eAAe;;;;;;4BAG7B,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAuE;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport type { User } from '@supabase/supabase-js'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n\n      if (!user) {\n        router.push('/login')\n      }\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN') {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;YAEX,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,aAAa;gBAChC,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/GradientBackground.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\ninterface GradientBackgroundProps {\n  children: React.ReactNode\n  variant?: 'default' | 'dashboard' | 'auth'\n}\n\nfunction GradientBackground({\n  children,\n  variant = 'default'\n}: GradientBackgroundProps) {\n  const variants = {\n    default: 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100',\n    dashboard: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50',\n    auth: 'bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800'\n  }\n\n  return (\n    <div className={`min-h-screen ${variants[variant]} relative overflow-hidden`}>\n      {/* Static background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl\" />\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-full blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl\" />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\nexport default GradientBackground\n"], "names": [], "mappings": ";;;;AAAA;;AASA,SAAS,mBAAmB,EAC1B,QAAQ,EACR,UAAU,SAAS,EACK;IACxB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,QAAQ,CAAC,QAAQ,CAAC,yBAAyB,CAAC;;0BAE1E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'gradient'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading = false, children, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm hover:shadow-md active:scale-95'\n\n    const variants = {\n      primary: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-200',\n      secondary: 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300',\n      outline: 'border-2 border-blue-600 text-blue-600 bg-transparent hover:bg-blue-50',\n      ghost: 'text-gray-600 hover:bg-gray-100 hover:text-gray-900',\n      destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-200',\n      gradient: 'bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 text-white hover:from-purple-700 hover:via-blue-700 hover:to-teal-700'\n    }\n\n    const sizes = {\n      sm: 'h-9 px-4 text-sm',\n      md: 'h-11 px-6 py-2.5',\n      lg: 'h-13 px-8 text-lg'\n    }\n\n    return (\n      <motion.button\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        disabled={loading}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        )}\n        {children}\n      </motion.button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACrF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACL,UAAU;QACT,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,OAAM;gBAA6B,MAAK;gBAAO,SAAQ;;kCACtG,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <motion.input\n        whileFocus={{ scale: 1.02 }}\n        type={type}\n        className={cn(\n          'flex h-12 w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-sm font-medium ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAIA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;QACX,YAAY;YAAE,OAAO;QAAK;QAC1B,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ubACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Label.tsx"], "sourcesContent": ["import { LabelHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Label = forwardRef<HTMLLabelElement, LabelHTMLAttributes<HTMLLabelElement>>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = 'Label'\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Select.tsx"], "sourcesContent": ["import { SelectHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface SelectProps extends SelectHTMLAttributes<HTMLSelectElement> {}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <select\n        className={cn(\n          'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </select>\n    )\n  }\n)\nSelect.displayName = 'Select'\n\nexport { Select }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2PACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = 'Textarea'\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACxB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Card.tsx"], "sourcesContent": ["'use client'\n\nimport { HTMLAttributes, forwardRef } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      ref={ref}\n      className={cn(\n        'rounded-2xl border border-gray-100 bg-white shadow-lg shadow-gray-100/50 backdrop-blur-sm hover:shadow-xl hover:shadow-gray-200/50 transition-all duration-300',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-2 p-8 pb-4', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-xl font-bold leading-none tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-gray-500 leading-relaxed', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('px-8 pb-8', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center px-8 pb-8 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0HAA0H;QACvI,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAa,GAAG,KAAK;;;;;;AAGnE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/assets.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Asset, Database } from '@/lib/types/database'\n\ntype AssetInsert = Database['public']['Tables']['assets']['Insert']\ntype AssetUpdate = Database['public']['Tables']['assets']['Update']\n\nexport async function getAssets() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getAsset(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createAsset(asset: AssetInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('assets')\n    .insert({\n      ...asset,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateAsset(id: string, updates: AssetUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('assets')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteAsset(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('assets')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getAssetCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'asset')\n    .order('name')\n\n  return { data, error }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,SAAS,EAAU;IACvC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,KAAkB;IAClD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;QACN,GAAG,KAAK;QACR,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU,EAAE,OAAoB;IAChE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,UACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,SACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/liabilities.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Liability, Database } from '@/lib/types/database'\n\ntype LiabilityInsert = Database['public']['Tables']['liabilities']['Insert']\ntype LiabilityUpdate = Database['public']['Tables']['liabilities']['Update']\n\nexport async function getLiabilities() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getLiability(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createLiability(liability: LiabilityInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('liabilities')\n    .insert({\n      ...liability,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateLiability(id: string, updates: LiabilityUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('liabilities')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteLiability(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('liabilities')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getLiabilityCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'liability')\n    .order('name')\n\n  return { data, error }\n}\n\n// Utility functions for loan calculations\nexport function calculateMonthlyPayment(\n  principal: number,\n  annualRate: number,\n  termInMonths: number\n): number {\n  if (annualRate === 0) return principal / termInMonths\n  \n  const monthlyRate = annualRate / 100 / 12\n  const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) / \n                  (Math.pow(1 + monthlyRate, termInMonths) - 1)\n  \n  return payment\n}\n\nexport function calculateInterestAccrued(\n  principal: number,\n  annualRate: number,\n  daysElapsed: number\n): number {\n  const dailyRate = annualRate / 100 / 365\n  return principal * dailyRate * daysElapsed\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,SAA0B;IAC9D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC;QACN,GAAG,SAAS;QACZ,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,EAAU,EAAE,OAAwB;IACxE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,gBAAgB,EAAU;IAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,aACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,SAAS,wBACd,SAAiB,EACjB,UAAkB,EAClB,YAAoB;IAEpB,IAAI,eAAe,GAAG,OAAO,YAAY;IAEzC,MAAM,cAAc,aAAa,MAAM;IACvC,MAAM,UAAU,YAAY,CAAC,cAAc,KAAK,GAAG,CAAC,IAAI,aAAa,aAAa,IAClE,CAAC,KAAK,GAAG,CAAC,IAAI,aAAa,gBAAgB,CAAC;IAE5D,OAAO;AACT;AAEO,SAAS,yBACd,SAAiB,EACjB,UAAkB,EAClB,WAAmB;IAEnB,MAAM,YAAY,aAAa,MAAM;IACrC,OAAO,YAAY,YAAY;AACjC", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/receivables.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Receivable, Database } from '@/lib/types/database'\n\ntype ReceivableInsert = Database['public']['Tables']['receivables']['Insert']\ntype ReceivableUpdate = Database['public']['Tables']['receivables']['Update']\n\nexport async function getReceivables() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('created_at', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function getReceivable(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createReceivable(receivable: ReceivableInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('receivables')\n    .insert({\n      ...receivable,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateReceivable(id: string, updates: ReceivableUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('receivables')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteReceivable(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('receivables')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\nexport async function getReceivableCategories() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('type', 'receivable')\n    .order('name')\n\n  return { data, error }\n}\n\n// Utility functions for receivable calculations\nexport function calculateTotalWithInterest(\n  principal: number,\n  annualRate: number,\n  daysElapsed: number\n): number {\n  if (annualRate === 0) return principal\n  \n  const dailyRate = annualRate / 100 / 365\n  const interest = principal * dailyRate * daysElapsed\n  return principal + interest\n}\n\nexport function getDaysOverdue(dueDate: string): number {\n  const due = new Date(dueDate)\n  const today = new Date()\n  const diffTime = today.getTime() - due.getTime()\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return Math.max(0, diffDays)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAMO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,cAAc,EAAU;IAC5C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,UAA4B;IACjE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC;QACN,GAAG,UAAU;QACb,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,EAAU,EAAE,OAAyB;IAC1E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,iBAAiB,EAAU;IAC/C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,cACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,SAAS,2BACd,SAAiB,EACjB,UAAkB,EAClB,WAAmB;IAEnB,IAAI,eAAe,GAAG,OAAO;IAE7B,MAAM,YAAY,aAAa,MAAM;IACrC,MAAM,WAAW,YAAY,YAAY;IACzC,OAAO,YAAY;AACrB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,QAAQ,IAAI;IAClB,MAAM,WAAW,MAAM,OAAO,KAAK,IAAI,OAAO;IAC9C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAC1D,OAAO,KAAK,GAAG,CAAC,GAAG;AACrB", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/transactions.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport { createAsset, updateAsset } from './assets'\nimport { createLiability, updateLiability } from './liabilities'\nimport { createReceivable, updateReceivable } from './receivables'\n\nexport interface Transaction {\n  id: string\n  user_id: string\n  category_id: string | null\n  type: 'income' | 'expense' | 'transfer'\n  amount: number\n  description: string | null\n  transaction_date: string\n  currency: string\n  created_at: string\n  updated_at: string\n  categories?: {\n    id: string\n    name: string\n    color: string\n  }\n}\n\ntype TransactionInsert = {\n  category_id?: string | null\n  type: 'income' | 'expense' | 'transfer'\n  amount: number\n  description?: string | null\n  transaction_date: string\n  currency?: string\n}\n\nexport async function getTransactions() {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('transactions')\n    .select(`\n      *,\n      categories (\n        id,\n        name,\n        color\n      )\n    `)\n    .order('transaction_date', { ascending: false })\n\n  return { data, error }\n}\n\nexport async function createTransaction(transaction: TransactionInsert) {\n  const supabase = createClient()\n\n  const { data: { user }, error: authError } = await supabase.auth.getUser()\n  if (authError || !user) {\n    return { data: null, error: authError || new Error('User not authenticated') }\n  }\n\n  const { data, error } = await supabase\n    .from('transactions')\n    .insert({\n      ...transaction,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateTransaction(id: string, updates: Partial<TransactionInsert>) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('transactions')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteTransaction(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('transactions')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\n// Process transaction and update relevant financial records\nexport async function processTransaction(transactionData: TransactionInsert & {\n  updateAssets?: boolean\n  updateLiabilities?: boolean\n  updateReceivables?: boolean\n  assetName?: string\n  liabilityName?: string\n  receivableName?: string\n  debtorName?: string\n}) {\n  // Extract only the transaction fields for database insertion\n  const {\n    updateAssets,\n    updateLiabilities,\n    updateReceivables,\n    assetName,\n    liabilityName,\n    receivableName,\n    debtorName,\n    ...transactionFields\n  } = transactionData\n\n  const { data: transaction, error: transactionError } = await createTransaction(transactionFields)\n  \n  if (transactionError || !transaction) {\n    return { data: null, error: transactionError }\n  }\n\n  // If it's an income transaction and should update assets\n  if (transactionData.type === 'income' && transactionData.updateAssets && transactionData.assetName) {\n    await createAsset({\n      name: transactionData.assetName,\n      current_value: transactionData.amount,\n      asset_type: 'cash',\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Income'}`\n    })\n  }\n\n  // If it's an expense transaction and should update liabilities\n  if (transactionData.type === 'expense' && transactionData.updateLiabilities && transactionData.liabilityName) {\n    await createLiability({\n      name: transactionData.liabilityName,\n      principal_amount: transactionData.amount,\n      current_balance: transactionData.amount,\n      liability_type: 'other',\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Expense'}`\n    })\n  }\n\n  // If it's a transfer and should update receivables\n  if (transactionData.type === 'transfer' && transactionData.updateReceivables && transactionData.receivableName && transactionData.debtorName) {\n    await createReceivable({\n      debtor_name: transactionData.debtorName,\n      principal_amount: transactionData.amount,\n      current_balance: transactionData.amount,\n      currency: transactionData.currency || 'LKR',\n      description: `From transaction: ${transactionData.description || 'Transfer'}`\n    })\n  }\n\n  return { data: transaction, error: null }\n}\n\nexport async function getTransactionSummary(startDate?: string, endDate?: string) {\n  const supabase = createClient()\n  \n  let query = supabase\n    .from('transactions')\n    .select('type, amount, currency')\n\n  if (startDate) {\n    query = query.gte('transaction_date', startDate)\n  }\n  if (endDate) {\n    query = query.lte('transaction_date', endDate)\n  }\n\n  const { data, error } = await query\n\n  if (error || !data) {\n    return { data: null, error }\n  }\n\n  const summary = data.reduce((acc, transaction) => {\n    const { type, amount } = transaction\n    if (!acc[type]) {\n      acc[type] = 0\n    }\n    acc[type] += amount\n    return acc\n  }, {} as Record<string, number>)\n\n  return { \n    data: {\n      income: summary.income || 0,\n      expense: summary.expense || 0,\n      transfer: summary.transfer || 0,\n      netIncome: (summary.income || 0) - (summary.expense || 0)\n    }, \n    error: null \n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AA6BO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;;IAOT,CAAC,EACA,KAAK,CAAC,oBAAoB;QAAE,WAAW;IAAM;IAEhD,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,WAA8B;IACpE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,MAAM;YAAM,OAAO,aAAa,IAAI,MAAM;QAA0B;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC;QACN,GAAG,WAAW;QACd,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,EAAU,EAAE,OAAmC;IACrF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,kBAAkB,EAAU;IAChD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,gBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAGO,eAAe,mBAAmB,eAQxC;IACC,6DAA6D;IAC7D,MAAM,EACJ,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,GAAG,mBACJ,GAAG;IAEJ,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,kBAAkB;IAE/E,IAAI,oBAAoB,CAAC,aAAa;QACpC,OAAO;YAAE,MAAM;YAAM,OAAO;QAAiB;IAC/C;IAEA,yDAAyD;IACzD,IAAI,gBAAgB,IAAI,KAAK,YAAY,gBAAgB,YAAY,IAAI,gBAAgB,SAAS,EAAE;QAClG,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;YAChB,MAAM,gBAAgB,SAAS;YAC/B,eAAe,gBAAgB,MAAM;YACrC,YAAY;YACZ,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,UAAU;QAC7E;IACF;IAEA,+DAA+D;IAC/D,IAAI,gBAAgB,IAAI,KAAK,aAAa,gBAAgB,iBAAiB,IAAI,gBAAgB,aAAa,EAAE;QAC5G,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;YACpB,MAAM,gBAAgB,aAAa;YACnC,kBAAkB,gBAAgB,MAAM;YACxC,iBAAiB,gBAAgB,MAAM;YACvC,gBAAgB;YAChB,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,WAAW;QAC9E;IACF;IAEA,mDAAmD;IACnD,IAAI,gBAAgB,IAAI,KAAK,cAAc,gBAAgB,iBAAiB,IAAI,gBAAgB,cAAc,IAAI,gBAAgB,UAAU,EAAE;QAC5I,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;YACrB,aAAa,gBAAgB,UAAU;YACvC,kBAAkB,gBAAgB,MAAM;YACxC,iBAAiB,gBAAgB,MAAM;YACvC,UAAU,gBAAgB,QAAQ,IAAI;YACtC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,WAAW,IAAI,YAAY;QAC/E;IACF;IAEA,OAAO;QAAE,MAAM;QAAa,OAAO;IAAK;AAC1C;AAEO,eAAe,sBAAsB,SAAkB,EAAE,OAAgB;IAC9E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,WAAW;QACb,QAAQ,MAAM,GAAG,CAAC,oBAAoB;IACxC;IACA,IAAI,SAAS;QACX,QAAQ,MAAM,GAAG,CAAC,oBAAoB;IACxC;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,SAAS,CAAC,MAAM;QAClB,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,KAAK;QAChC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;QACzB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACd,GAAG,CAAC,KAAK,GAAG;QACd;QACA,GAAG,CAAC,KAAK,IAAI;QACb,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QACL,MAAM;YACJ,QAAQ,QAAQ,MAAM,IAAI;YAC1B,SAAS,QAAQ,OAAO,IAAI;YAC5B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC;QAC1D;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/categories.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\nimport type { Category, Database } from '@/lib/types/database'\n\ntype CategoryInsert = Database['public']['Tables']['categories']['Insert']\ntype CategoryUpdate = Database['public']['Tables']['categories']['Update']\n\nexport async function getCategories(type?: string) {\n  const supabase = createClient()\n  \n  let query = supabase\n    .from('categories')\n    .select('*')\n    .order('name')\n\n  if (type) {\n    query = query.eq('type', type)\n  }\n\n  const { data, error } = await query\n\n  return { data, error }\n}\n\nexport async function getCategory(id: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .eq('id', id)\n    .single()\n\n  return { data, error }\n}\n\nexport async function createCategory(category: CategoryInsert) {\n  const supabase = createClient()\n  \n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) throw new Error('User not authenticated')\n\n  const { data, error } = await supabase\n    .from('categories')\n    .insert({\n      ...category,\n      user_id: user.id,\n    })\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function updateCategory(id: string, updates: CategoryUpdate) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase\n    .from('categories')\n    .update(updates)\n    .eq('id', id)\n    .select()\n    .single()\n\n  return { data, error }\n}\n\nexport async function deleteCategory(id: string) {\n  const supabase = createClient()\n  \n  const { error } = await supabase\n    .from('categories')\n    .delete()\n    .eq('id', id)\n\n  return { error }\n}\n\n// Create default categories for new users\nexport async function createDefaultCategories() {\n  const defaultCategories = [\n    // Income categories\n    { name: 'Salary', type: 'income', color: '#10B981' },\n    { name: 'Business Income', type: 'income', color: '#059669' },\n    { name: 'Investment Returns', type: 'income', color: '#047857' },\n    { name: 'Other Income', type: 'income', color: '#065F46' },\n    \n    // Expense categories\n    { name: 'Food & Dining', type: 'expense', color: '#EF4444' },\n    { name: 'Transportation', type: 'expense', color: '#DC2626' },\n    { name: 'Shopping', type: 'expense', color: '#B91C1C' },\n    { name: 'Entertainment', type: 'expense', color: '#991B1B' },\n    { name: 'Bills & Utilities', type: 'expense', color: '#7F1D1D' },\n    { name: 'Healthcare', type: 'expense', color: '#F59E0B' },\n    { name: 'Education', type: 'expense', color: '#D97706' },\n    { name: 'Travel', type: 'expense', color: '#B45309' },\n    { name: 'Other Expenses', type: 'expense', color: '#92400E' },\n  ]\n\n  const results = []\n  for (const category of defaultCategories) {\n    const result = await createCategory(category as CategoryInsert)\n    results.push(result)\n  }\n\n  return results\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAMO,eAAe,cAAc,IAAa;IAC/C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,QAAQ,SACT,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,IAAI,MAAM;QACR,QAAQ,MAAM,EAAE,CAAC,QAAQ;IAC3B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,QAAwB;IAC3D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC;QACN,GAAG,QAAQ;QACX,SAAS,KAAK,EAAE;IAClB,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,EAAU,EAAE,OAAuB;IACtE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;IAAM;AACjB;AAGO,eAAe;IACpB,MAAM,oBAAoB;QACxB,oBAAoB;QACpB;YAAE,MAAM;YAAU,MAAM;YAAU,OAAO;QAAU;QACnD;YAAE,MAAM;YAAmB,MAAM;YAAU,OAAO;QAAU;QAC5D;YAAE,MAAM;YAAsB,MAAM;YAAU,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAgB,MAAM;YAAU,OAAO;QAAU;QAEzD,qBAAqB;QACrB;YAAE,MAAM;YAAiB,MAAM;YAAW,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAkB,MAAM;YAAW,OAAO;QAAU;QAC5D;YAAE,MAAM;YAAY,MAAM;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAiB,MAAM;YAAW,OAAO;QAAU;QAC3D;YAAE,MAAM;YAAqB,MAAM;YAAW,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAc,MAAM;YAAW,OAAO;QAAU;QACxD;YAAE,MAAM;YAAa,MAAM;YAAW,OAAO;QAAU;QACvD;YAAE,MAAM;YAAU,MAAM;YAAW,OAAO;QAAU;QACpD;YAAE,MAAM;YAAkB,MAAM;YAAW,OAAO;QAAU;KAC7D;IAED,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,YAAY,kBAAmB;QACxC,MAAM,SAAS,MAAM,eAAe;QACpC,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/transactions/QuickTransactionForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion } from 'framer-motion'\nimport { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Label } from '@/components/ui/Label'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { processTransaction } from '@/lib/api/transactions'\nimport { getCategories } from '@/lib/api/categories'\nimport type { Category } from '@/lib/types/database'\n\ninterface QuickTransactionFormProps {\n  onSuccess: () => void\n  onCancel: () => void\n  defaultType?: 'income' | 'expense' | 'transfer'\n}\n\nexport default function QuickTransactionForm({ \n  onSuccess, \n  onCancel, \n  defaultType = 'expense' \n}: QuickTransactionFormProps) {\n  const [loading, setLoading] = useState(false)\n  const [categories, setCategories] = useState<Category[]>([])\n  const [formData, setFormData] = useState({\n    type: defaultType,\n    amount: '',\n    description: '',\n    category_id: '',\n    transaction_date: new Date().toISOString().split('T')[0],\n    currency: 'LKR',\n    // Advanced options\n    updateAssets: false,\n    updateLiabilities: false,\n    updateReceivables: false,\n    assetName: '',\n    liabilityName: '',\n    receivableName: '',\n    debtorName: ''\n  })\n\n  useEffect(() => {\n    loadCategories()\n  }, [formData.type])\n\n  const loadCategories = async () => {\n    const { data } = await getCategories(formData.type)\n    if (data) setCategories(data)\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      const result = await processTransaction({\n        type: formData.type as 'income' | 'expense' | 'transfer',\n        amount: parseFloat(formData.amount),\n        description: formData.description || null,\n        category_id: formData.category_id || null,\n        transaction_date: formData.transaction_date,\n        currency: formData.currency,\n        updateAssets: formData.updateAssets,\n        updateLiabilities: formData.updateLiabilities,\n        updateReceivables: formData.updateReceivables,\n        assetName: formData.assetName,\n        liabilityName: formData.liabilityName,\n        receivableName: formData.receivableName,\n        debtorName: formData.debtorName\n      })\n\n      if (result.error) {\n        console.error('Error creating transaction:', result.error)\n        alert('Error creating transaction: ' + result.error.message)\n        return\n      }\n\n      onSuccess()\n    } catch (error) {\n      console.error('Error creating transaction:', error)\n      alert('Error creating transaction: ' + (error as Error).message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const transactionTypes = [\n    { value: 'income', label: 'Income', icon: Plus, color: 'text-green-600', bg: 'bg-green-50' },\n    { value: 'expense', label: 'Expense', icon: Minus, color: 'text-red-600', bg: 'bg-red-50' },\n    { value: 'transfer', label: 'Transfer', icon: ArrowRightLeft, color: 'text-blue-600', bg: 'bg-blue-50' }\n  ]\n\n  const currentType = transactionTypes.find(t => t.value === formData.type)\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.95 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\"\n    >\n      <Card className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <CardHeader className=\"flex flex-row items-center justify-between\">\n          <CardTitle className=\"flex items-center\">\n            {currentType && (\n              <div className={`p-2 rounded-xl ${currentType.bg} mr-3`}>\n                <currentType.icon className={`h-5 w-5 ${currentType.color}`} />\n              </div>\n            )}\n            Add New Transaction\n          </CardTitle>\n          <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Transaction Type */}\n            <div>\n              <Label>Transaction Type</Label>\n              <div className=\"grid grid-cols-3 gap-3 mt-2\">\n                {transactionTypes.map((type) => (\n                  <button\n                    key={type.value}\n                    type=\"button\"\n                    onClick={() => handleChange('type', type.value)}\n                    className={`\n                      p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center space-y-2\n                      ${formData.type === type.value\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                      }\n                    `}\n                  >\n                    <type.icon className={`h-6 w-6 ${type.color}`} />\n                    <span className=\"font-medium text-sm\">{type.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Amount */}\n              <div>\n                <Label htmlFor=\"amount\">Amount *</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"amount\"\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={formData.amount}\n                    onChange={(e) => handleChange('amount', e.target.value)}\n                    placeholder=\"0.00\"\n                    required\n                    className=\"pl-12\"\n                  />\n                  <span className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium\">\n                    {formData.currency === 'LKR' ? 'Rs.' : '$'}\n                  </span>\n                </div>\n              </div>\n\n              {/* Currency */}\n              <div>\n                <Label htmlFor=\"currency\">Currency</Label>\n                <Select\n                  id=\"currency\"\n                  value={formData.currency}\n                  onChange={(e) => handleChange('currency', e.target.value)}\n                >\n                  <option value=\"LKR\">LKR (Sri Lankan Rupee)</option>\n                  <option value=\"USD\">USD (US Dollar)</option>\n                </Select>\n              </div>\n            </div>\n\n            {/* Category */}\n            <div>\n              <Label htmlFor=\"category_id\">Category</Label>\n              <Select\n                id=\"category_id\"\n                value={formData.category_id}\n                onChange={(e) => handleChange('category_id', e.target.value)}\n              >\n                <option value=\"\">Select a category</option>\n                {categories.map((category) => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </Select>\n            </div>\n\n            {/* Date */}\n            <div>\n              <Label htmlFor=\"transaction_date\">Date</Label>\n              <Input\n                id=\"transaction_date\"\n                type=\"date\"\n                value={formData.transaction_date}\n                onChange={(e) => handleChange('transaction_date', e.target.value)}\n                required\n              />\n            </div>\n\n            {/* Description */}\n            <div>\n              <Label htmlFor=\"description\">Description</Label>\n              <Textarea\n                id=\"description\"\n                value={formData.description}\n                onChange={(e) => handleChange('description', e.target.value)}\n                placeholder=\"Add a note about this transaction...\"\n                rows={3}\n              />\n            </div>\n\n            {/* Advanced Options */}\n            <div className=\"border-t pt-6\">\n              <h3 className=\"font-semibold text-gray-900 mb-4\">Advanced Options</h3>\n              <div className=\"space-y-4\">\n                {formData.type === 'income' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"updateAssets\"\n                      checked={formData.updateAssets}\n                      onChange={(e) => handleChange('updateAssets', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                    <Label htmlFor=\"updateAssets\">Add to Assets</Label>\n                    {formData.updateAssets && (\n                      <Input\n                        placeholder=\"Asset name\"\n                        value={formData.assetName}\n                        onChange={(e) => handleChange('assetName', e.target.value)}\n                        className=\"flex-1\"\n                      />\n                    )}\n                  </div>\n                )}\n\n                {formData.type === 'expense' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"updateLiabilities\"\n                      checked={formData.updateLiabilities}\n                      onChange={(e) => handleChange('updateLiabilities', e.target.checked)}\n                      className=\"rounded\"\n                    />\n                    <Label htmlFor=\"updateLiabilities\">Add to Liabilities</Label>\n                    {formData.updateLiabilities && (\n                      <Input\n                        placeholder=\"Liability name\"\n                        value={formData.liabilityName}\n                        onChange={(e) => handleChange('liabilityName', e.target.value)}\n                        className=\"flex-1\"\n                      />\n                    )}\n                  </div>\n                )}\n\n                {formData.type === 'transfer' && (\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        id=\"updateReceivables\"\n                        checked={formData.updateReceivables}\n                        onChange={(e) => handleChange('updateReceivables', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <Label htmlFor=\"updateReceivables\">Add to Receivables</Label>\n                    </div>\n                    {formData.updateReceivables && (\n                      <div className=\"grid grid-cols-2 gap-3\">\n                        <Input\n                          placeholder=\"Receivable name\"\n                          value={formData.receivableName}\n                          onChange={(e) => handleChange('receivableName', e.target.value)}\n                        />\n                        <Input\n                          placeholder=\"Debtor name\"\n                          value={formData.debtorName}\n                          onChange={(e) => handleChange('debtorName', e.target.value)}\n                        />\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-6\">\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n                Cancel\n              </Button>\n              <Button type=\"submit\" loading={loading}>\n                Add Transaction\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAqBe,SAAS,qBAAqB,EAC3C,SAAS,EACT,QAAQ,EACR,cAAc,SAAS,EACG;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;QACb,kBAAkB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,UAAU;QACV,mBAAmB;QACnB,cAAc;QACd,mBAAmB;QACnB,mBAAmB;QACnB,WAAW;QACX,eAAe;QACf,gBAAgB;QAChB,YAAY;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,iBAAiB;QACrB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;QAClD,IAAI,MAAM,cAAc;IAC1B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;gBACtC,MAAM,SAAS,IAAI;gBACnB,QAAQ,WAAW,SAAS,MAAM;gBAClC,aAAa,SAAS,WAAW,IAAI;gBACrC,aAAa,SAAS,WAAW,IAAI;gBACrC,kBAAkB,SAAS,gBAAgB;gBAC3C,UAAU,SAAS,QAAQ;gBAC3B,cAAc,SAAS,YAAY;gBACnC,mBAAmB,SAAS,iBAAiB;gBAC7C,mBAAmB,SAAS,iBAAiB;gBAC7C,WAAW,SAAS,SAAS;gBAC7B,eAAe,SAAS,aAAa;gBACrC,gBAAgB,SAAS,cAAc;gBACvC,YAAY,SAAS,UAAU;YACjC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,+BAA+B,OAAO,KAAK;gBACzD,MAAM,iCAAiC,OAAO,KAAK,CAAC,OAAO;gBAC3D;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,iCAAiC,AAAC,MAAgB,OAAO;QACjE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAkB,IAAI;QAAc;QAC3F;YAAE,OAAO;YAAW,OAAO;YAAW,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAgB,IAAI;QAAY;QAC1F;YAAE,OAAO;YAAY,OAAO;YAAY,MAAM,8NAAA,CAAA,iBAAc;YAAE,OAAO;YAAiB,IAAI;QAAa;KACxG;IAED,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,IAAI;IAExE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAK;QAChC,WAAU;kBAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;gCAClB,6BACC,8OAAC;oCAAI,WAAW,CAAC,eAAe,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC;8CACrD,cAAA,8OAAC,YAAY,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE;;;;;;;;;;;gCAE7D;;;;;;;sCAGJ,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGjB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,QAAQ,KAAK,KAAK;gDAC9C,WAAW,CAAC;;sBAEV,EAAE,SAAS,IAAI,KAAK,KAAK,KAAK,GAC1B,+BACA,wCACH;oBACH,CAAC;;kEAED,8OAAC,KAAK,IAAI;wDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;kEAC7C,8OAAC;wDAAK,WAAU;kEAAuB,KAAK,KAAK;;;;;;;+CAZ5C,KAAK,KAAK;;;;;;;;;;;;;;;;0CAkBvB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,MAAM;wDACtB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;wDACtD,aAAY;wDACZ,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ,KAAK,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,SAAM;gDACL,IAAG;gDACH,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;;kEAExD,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;;0DAE3D,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oDAAyB,OAAO,SAAS,EAAE;8DACzC,SAAS,IAAI;mDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;0CAQ9B,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;kDAClC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,gBAAgB;wCAChC,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAChE,QAAQ;;;;;;;;;;;;0CAKZ,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC3D,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,IAAI,KAAK,0BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,YAAY;wDAC9B,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,OAAO;wDAC9D,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAe;;;;;;oDAC7B,SAAS,YAAY,kBACpB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,SAAS;wDACzB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;wDACzD,WAAU;;;;;;;;;;;;4CAMjB,SAAS,IAAI,KAAK,2BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,iBAAiB;wDACnC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,OAAO;wDACnE,WAAU;;;;;;kEAEZ,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;oDAClC,SAAS,iBAAiB,kBACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAU;;;;;;;;;;;;4CAMjB,SAAS,IAAI,KAAK,4BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS,SAAS,iBAAiB;gEACnC,UAAU,CAAC,IAAM,aAAa,qBAAqB,EAAE,MAAM,CAAC,OAAO;gEACnE,WAAU;;;;;;0EAEZ,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;;;;;;;oDAEpC,SAAS,iBAAiB,kBACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;0EAEhE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD", "debugId": null}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/QuickActionButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'\nimport { Button } from './Button'\nimport QuickTransactionForm from '@/components/transactions/QuickTransactionForm'\n\nexport default function QuickActionButton() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [showForm, setShowForm] = useState(false)\n  const [selectedType, setSelectedType] = useState<'income' | 'expense' | 'transfer'>('expense')\n\n  const quickActions = [\n    { type: 'income' as const, icon: Plus, label: 'Income', color: 'bg-green-500 hover:bg-green-600' },\n    { type: 'expense' as const, icon: Minus, label: 'Expense', color: 'bg-red-500 hover:bg-red-600' },\n    { type: 'transfer' as const, icon: ArrowRightLeft, label: 'Transfer', color: 'bg-blue-500 hover:bg-blue-600' },\n  ]\n\n  const handleActionClick = (type: 'income' | 'expense' | 'transfer') => {\n    setSelectedType(type)\n    setShowForm(true)\n    setIsOpen(false)\n  }\n\n  const handleFormSuccess = () => {\n    setShowForm(false)\n    // Optionally trigger a refresh of the current page data\n    window.location.reload()\n  }\n\n  return (\n    <>\n      <div className=\"fixed bottom-6 right-6 z-40\">\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              className=\"absolute bottom-16 right-0 space-y-3\"\n            >\n              {quickActions.map((action, index) => (\n                <motion.button\n                  key={action.type}\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  transition={{ delay: index * 0.1 }}\n                  onClick={() => handleActionClick(action.type)}\n                  className={`\n                    flex items-center space-x-3 px-4 py-3 rounded-xl text-white font-medium\n                    shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\n                    ${action.color}\n                  `}\n                >\n                  <action.icon className=\"h-5 w-5\" />\n                  <span className=\"whitespace-nowrap\">{action.label}</span>\n                </motion.button>\n              ))}\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        <motion.button\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          onClick={() => setIsOpen(!isOpen)}\n          className={`\n            w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200\n            flex items-center justify-center text-white font-bold text-xl\n            ${isOpen \n              ? 'bg-gray-500 hover:bg-gray-600' \n              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'\n            }\n          `}\n        >\n          <motion.div\n            animate={{ rotate: isOpen ? 45 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            {isOpen ? <X className=\"h-6 w-6\" /> : <Plus className=\"h-6 w-6\" />}\n          </motion.div>\n        </motion.button>\n      </div>\n\n      <AnimatePresence>\n        {showForm && (\n          <QuickTransactionForm\n            defaultType={selectedType}\n            onSuccess={handleFormSuccess}\n            onCancel={() => setShowForm(false)}\n          />\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAEpF,MAAM,eAAe;QACnB;YAAE,MAAM;YAAmB,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAU,OAAO;QAAkC;QACjG;YAAE,MAAM;YAAoB,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAW,OAAO;QAA8B;QAChG;YAAE,MAAM;YAAqB,MAAM,8NAAA,CAAA,iBAAc;YAAE,OAAO;YAAY,OAAO;QAAgC;KAC9G;IAED,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,YAAY;QACZ,UAAU;IACZ;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,wDAAwD;QACxD,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;sCAET,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,IAAM,kBAAkB,OAAO,IAAI;oCAC5C,WAAW,CAAC;;;oBAGV,EAAE,OAAO,KAAK,CAAC;kBACjB,CAAC;;sDAED,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAqB,OAAO,KAAK;;;;;;;mCAb5C,OAAO,IAAI;;;;;;;;;;;;;;;kCAoB1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;wBACvB,SAAS,IAAM,UAAU,CAAC;wBAC1B,WAAW,CAAC;;;YAGV,EAAE,SACE,kCACA,uFACH;UACH,CAAC;kCAED,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ,SAAS,KAAK;4BAAE;4BACnC,YAAY;gCAAE,UAAU;4BAAI;sCAE3B,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAK5D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,0BACC,8OAAC,0JAAA,CAAA,UAAoB;oBACnB,aAAa;oBACb,WAAW;oBACX,UAAU,IAAM,YAAY;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Table.tsx"], "sourcesContent": ["import { HTMLAttributes, TdHTMLAttributes, ThHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Table = forwardRef<HTMLTableElement, HTMLAttributes<HTMLTableElement>>(\n  ({ className, ...props }, ref) => (\n    <div className=\"relative w-full overflow-auto\">\n      <table\n        ref={ref}\n        className={cn('w-full caption-bottom text-sm', className)}\n        {...props}\n      />\n    </div>\n  )\n)\nTable.displayName = 'Table'\n\nconst TableHeader = forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <thead ref={ref} className={cn('[&_tr]:border-b', className)} {...props} />\n  )\n)\nTableHeader.displayName = 'TableHeader'\n\nconst TableBody = forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <tbody\n      ref={ref}\n      className={cn('[&_tr:last-child]:border-0', className)}\n      {...props}\n    />\n  )\n)\nTableBody.displayName = 'TableBody'\n\nconst TableFooter = forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(\n  ({ className, ...props }, ref) => (\n    <tfoot\n      ref={ref}\n      className={cn('bg-gray-900/5 font-medium [&>tr]:last:border-b-0', className)}\n      {...props}\n    />\n  )\n)\nTableFooter.displayName = 'TableFooter'\n\nconst TableRow = forwardRef<HTMLTableRowElement, HTMLAttributes<HTMLTableRowElement>>(\n  ({ className, ...props }, ref) => (\n    <tr\n      ref={ref}\n      className={cn(\n        'border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableRow.displayName = 'TableRow'\n\nconst TableHead = forwardRef<HTMLTableCellElement, ThHTMLAttributes<HTMLTableCellElement>>(\n  ({ className, ...props }, ref) => (\n    <th\n      ref={ref}\n      className={cn(\n        'h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nTableHead.displayName = 'TableHead'\n\nconst TableCell = forwardRef<HTMLTableCellElement, TdHTMLAttributes<HTMLTableCellElement>>(\n  ({ className, ...props }, ref) => (\n    <td\n      ref={ref}\n      className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\n      {...props}\n    />\n  )\n)\nTableCell.displayName = 'TableCell'\n\nconst TableCaption = forwardRef<HTMLTableCaptionElement, HTMLAttributes<HTMLTableCaptionElement>>(\n  ({ className, ...props }, ref) => (\n    <caption\n      ref={ref}\n      className={cn('mt-4 text-sm text-gray-500', className)}\n      {...props}\n    />\n  )\n)\nTableCaption.displayName = 'TableCaption'\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAKjB,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAG3E,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oDAAoD;QACjE,GAAG,KAAK;;;;;;AAIf,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACxB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;QAED,GAAG,KAAK;;;;;;AAIf,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIf,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/SearchInput.tsx"], "sourcesContent": ["import { Search, X } from 'lucide-react'\nimport { Input } from './Input'\nimport { But<PERSON> } from './Button'\n\ninterface SearchInputProps {\n  value: string\n  onChange: (value: string) => void\n  placeholder?: string\n  className?: string\n}\n\nexport default function SearchInput({ \n  value, \n  onChange, \n  placeholder = \"Search...\",\n  className \n}: SearchInputProps) {\n  return (\n    <div className={`relative ${className}`}>\n      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n      <Input\n        type=\"text\"\n        placeholder={placeholder}\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        className=\"pl-10 pr-10\"\n      />\n      {value && (\n        <Button\n          type=\"button\"\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n          onClick={() => onChange('')}\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;AASe,SAAS,YAAY,EAClC,KAAK,EACL,QAAQ,EACR,cAAc,WAAW,EACzB,SAAS,EACQ;IACjB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,8OAAC,iIAAA,CAAA,QAAK;gBACJ,MAAK;gBACL,aAAa;gBACb,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,WAAU;;;;;;YAEX,uBACC,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS;0BAExB,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 2503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/FilterDropdown.tsx"], "sourcesContent": ["import { useState } from 'react'\nimport { ChevronDown, Filter } from 'lucide-react'\nimport { Button } from './Button'\nimport { Select } from './Select'\nimport { Input } from './Input'\nimport { Label } from './Label'\n\ninterface FilterOption {\n  key: string\n  label: string\n  type: 'select' | 'date' | 'number'\n  options?: { value: string; label: string }[]\n}\n\ninterface FilterDropdownProps {\n  filters: FilterOption[]\n  values: Record<string, any>\n  onChange: (key: string, value: any) => void\n  onClear: () => void\n}\n\nexport default function FilterDropdown({ \n  filters, \n  values, \n  onChange, \n  onClear \n}: FilterDropdownProps) {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const activeFiltersCount = Object.values(values).filter(v => v !== '' && v !== null && v !== undefined).length\n\n  return (\n    <div className=\"relative\">\n      <Button\n        variant=\"outline\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center\"\n      >\n        <Filter className=\"h-4 w-4 mr-2\" />\n        Filters\n        {activeFiltersCount > 0 && (\n          <span className=\"ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5\">\n            {activeFiltersCount}\n          </span>\n        )}\n        <ChevronDown className=\"h-4 w-4 ml-2\" />\n      </Button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n          <div className=\"p-4 space-y-4\">\n            <div className=\"flex justify-between items-center\">\n              <h3 className=\"font-medium\">Filters</h3>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  onClear()\n                  setIsOpen(false)\n                }}\n              >\n                Clear All\n              </Button>\n            </div>\n\n            {filters.map((filter) => (\n              <div key={filter.key}>\n                <Label htmlFor={filter.key}>{filter.label}</Label>\n                {filter.type === 'select' && filter.options ? (\n                  <Select\n                    id={filter.key}\n                    value={values[filter.key] || ''}\n                    onChange={(e) => onChange(filter.key, e.target.value)}\n                  >\n                    <option value=\"\">All</option>\n                    {filter.options.map((option) => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </Select>\n                ) : (\n                  <Input\n                    id={filter.key}\n                    type={filter.type === 'date' ? 'date' : filter.type === 'number' ? 'number' : 'text'}\n                    value={values[filter.key] || ''}\n                    onChange={(e) => onChange(filter.key, e.target.value)}\n                  />\n                )}\n              </div>\n            ))}\n\n            <div className=\"flex justify-end pt-2\">\n              <Button\n                size=\"sm\"\n                onClick={() => setIsOpen(false)}\n              >\n                Apply Filters\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAgBe,SAAS,eAAe,EACrC,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACa;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,qBAAqB,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,MAAM,MAAM,MAAM,QAAQ,MAAM,WAAW,MAAM;IAE9G,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;oBAElC,qBAAqB,mBACpB,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAGL,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAGxB,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP;wCACA,UAAU;oCACZ;8CACD;;;;;;;;;;;;wBAKF,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAS,OAAO,GAAG;kDAAG,OAAO,KAAK;;;;;;oCACxC,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,iBACzC,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAI,OAAO,GAAG;wCACd,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,IAAI;wCAC7B,UAAU,CAAC,IAAM,SAAS,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;;0DAEpD,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;6DAM7B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAI,OAAO,GAAG;wCACd,MAAM,OAAO,IAAI,KAAK,SAAS,SAAS,OAAO,IAAI,KAAK,WAAW,WAAW;wCAC9E,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,IAAI;wCAC7B,UAAU,CAAC,IAAM,SAAS,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;;;;;;;+BApBhD,OAAO,GAAG;;;;;sCA0BtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/assets/AssetForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Label } from '@/components/ui/Label'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { createAsset, updateAsset } from '@/lib/api/assets'\nimport { createLiability } from '@/lib/api/liabilities'\nimport type { Asset } from '@/lib/types/database'\n\ninterface AssetFormProps {\n  asset?: Asset\n  onSuccess: () => void\n  onCancel: () => void\n}\n\nexport default function AssetForm({ asset, onSuccess, onCancel }: AssetFormProps) {\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: asset?.name || '',\n    description: asset?.description || '',\n    current_value: asset?.current_value || 0,\n    purchase_value: asset?.purchase_value || 0,\n    purchase_date: asset?.purchase_date || '',\n    asset_type: asset?.asset_type || 'investment' as const,\n    currency: asset?.currency || 'LKR',\n  })\n\n  const [hasLoan, setHasLoan] = useState(false)\n  const [loanData, setLoanData] = useState({\n    name: '',\n    principal_amount: 0,\n    current_balance: 0,\n    interest_rate: 0,\n    due_date: '',\n    description: ''\n  })\n\n\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n\n    try {\n      let result\n      if (asset) {\n        result = await updateAsset(asset.id, formData)\n      } else {\n        result = await createAsset(formData)\n      }\n\n      if (result.error) {\n        console.error('Error saving asset:', result.error)\n        alert('Error saving asset: ' + result.error.message)\n        return\n      }\n\n      // If this is a real estate or vehicle with a loan, create the liability\n      if (hasLoan && (formData.asset_type === 'real_estate' || formData.asset_type === 'vehicle') && !asset) {\n        const loanResult = await createLiability({\n          name: loanData.name || `${formData.name} Loan`,\n          liability_type: formData.asset_type === 'real_estate' ? 'mortgage' : 'loan_taken',\n          principal_amount: loanData.principal_amount,\n          current_balance: loanData.current_balance || loanData.principal_amount,\n          interest_rate: loanData.interest_rate,\n          due_date: loanData.due_date,\n          currency: formData.currency,\n          description: loanData.description || `Loan for ${formData.name}`\n        })\n\n        if (loanResult.error) {\n          console.error('Error creating loan:', loanResult.error)\n          alert('Asset created but failed to create loan: ' + loanResult.error.message)\n        }\n      }\n\n      onSuccess()\n    } catch (error) {\n      console.error('Error saving asset:', error)\n      alert('Error saving asset: ' + (error as Error).message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleChange = (field: string, value: any) => {\n    // Validate numeric fields to prevent overflow\n    if ((field === 'current_value' || field === 'purchase_value') && typeof value === 'number') {\n      // Limit to reasonable values to prevent database overflow\n      if (value > 999999999999) {\n        alert('Value is too large. Please enter a smaller amount.')\n        return\n      }\n    }\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl\">\n      <CardHeader>\n        <CardTitle>{asset ? 'Edit Asset' : 'Add New Asset'}</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"name\">Asset Name *</Label>\n              <Input\n                id=\"name\"\n                value={formData.name}\n                onChange={(e) => handleChange('name', e.target.value)}\n                required\n              />\n            </div>\n            \n            <div>\n              <Label htmlFor=\"asset_type\">Asset Type *</Label>\n              <Select\n                id=\"asset_type\"\n                value={formData.asset_type}\n                onChange={(e) => handleChange('asset_type', e.target.value)}\n                required\n              >\n                <option value=\"investment\">Investment</option>\n                <option value=\"real_estate\">Real Estate</option>\n                <option value=\"vehicle\">Vehicle</option>\n                <option value=\"cash\">Cash</option>\n                <option value=\"savings\">Savings Account</option>\n                <option value=\"fixed_deposit\">Fixed Deposit</option>\n                <option value=\"stocks\">Stocks</option>\n                <option value=\"bonds\">Bonds</option>\n                <option value=\"mutual_funds\">Mutual Funds</option>\n                <option value=\"cryptocurrency\">Cryptocurrency</option>\n                <option value=\"jewelry\">Jewelry</option>\n                <option value=\"art_collectibles\">Art & Collectibles</option>\n                <option value=\"business\">Business</option>\n                <option value=\"other\">Other</option>\n              </Select>\n            </div>\n          </div>\n\n          <div>\n            <Label htmlFor=\"description\">Description</Label>\n            <Textarea\n              id=\"description\"\n              value={formData.description}\n              onChange={(e) => handleChange('description', e.target.value)}\n              rows={3}\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <Label htmlFor=\"current_value\">Current Value *</Label>\n              <Input\n                id=\"current_value\"\n                type=\"number\"\n                step=\"0.01\"\n                value={formData.current_value}\n                onChange={(e) => handleChange('current_value', parseFloat(e.target.value) || 0)}\n                required\n              />\n            </div>\n            \n            <div>\n              <Label htmlFor=\"purchase_value\">Purchase Value</Label>\n              <Input\n                id=\"purchase_value\"\n                type=\"number\"\n                step=\"0.01\"\n                value={formData.purchase_value}\n                onChange={(e) => handleChange('purchase_value', parseFloat(e.target.value) || 0)}\n              />\n            </div>\n            \n            <div>\n              <Label htmlFor=\"currency\">Currency</Label>\n              <Select\n                id=\"currency\"\n                value={formData.currency}\n                onChange={(e) => handleChange('currency', e.target.value)}\n              >\n                <option value=\"LKR\">LKR (Sri Lankan Rupee)</option>\n                <option value=\"USD\">USD (US Dollar)</option>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"purchase_date\">Purchase Date</Label>\n              <Input\n                id=\"purchase_date\"\n                type=\"date\"\n                value={formData.purchase_date}\n                onChange={(e) => handleChange('purchase_date', e.target.value)}\n              />\n            </div>\n            \n\n          </div>\n\n          {/* Loan/Mortgage Section for Real Estate and Vehicle */}\n          {(formData.asset_type === 'real_estate' || formData.asset_type === 'vehicle') && !asset && (\n            <div className=\"border-t pt-6\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <input\n                  type=\"checkbox\"\n                  id=\"hasLoan\"\n                  checked={hasLoan}\n                  onChange={(e) => setHasLoan(e.target.checked)}\n                  className=\"rounded border-gray-300\"\n                />\n                <Label htmlFor=\"hasLoan\">\n                  This {formData.asset_type === 'real_estate' ? 'property has a mortgage' : 'vehicle has a loan'}\n                </Label>\n              </div>\n\n              {hasLoan && (\n                <div className=\"space-y-4 bg-gray-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-lg\">\n                    {formData.asset_type === 'real_estate' ? 'Mortgage' : 'Vehicle Loan'} Details\n                  </h3>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"loanName\">Loan Name</Label>\n                      <Input\n                        id=\"loanName\"\n                        value={loanData.name}\n                        onChange={(e) => setLoanData(prev => ({ ...prev, name: e.target.value }))}\n                        placeholder={`${formData.name} ${formData.asset_type === 'real_estate' ? 'Mortgage' : 'Loan'}`}\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"principalAmount\">Principal Amount *</Label>\n                      <Input\n                        id=\"principalAmount\"\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={loanData.principal_amount}\n                        onChange={(e) => setLoanData(prev => ({ ...prev, principal_amount: parseFloat(e.target.value) || 0 }))}\n                        required={hasLoan}\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"currentBalance\">Current Balance</Label>\n                      <Input\n                        id=\"currentBalance\"\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={loanData.current_balance}\n                        onChange={(e) => setLoanData(prev => ({ ...prev, current_balance: parseFloat(e.target.value) || 0 }))}\n                        placeholder=\"Leave empty to use principal amount\"\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"interestRate\">Interest Rate (%)</Label>\n                      <Input\n                        id=\"interestRate\"\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={loanData.interest_rate}\n                        onChange={(e) => setLoanData(prev => ({ ...prev, interest_rate: parseFloat(e.target.value) || 0 }))}\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"loanDueDate\">Due Date</Label>\n                      <Input\n                        id=\"loanDueDate\"\n                        type=\"date\"\n                        value={loanData.due_date}\n                        onChange={(e) => setLoanData(prev => ({ ...prev, due_date: e.target.value }))}\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"loanDescription\">Loan Description</Label>\n                    <Textarea\n                      id=\"loanDescription\"\n                      value={loanData.description}\n                      onChange={(e) => setLoanData(prev => ({ ...prev, description: e.target.value }))}\n                      rows={2}\n                      placeholder=\"Additional details about the loan...\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          <div>\n            <Label htmlFor=\"description\">Asset Description</Label>\n            <Textarea\n              id=\"description\"\n              value={formData.description}\n              onChange={(e) => handleChange('description', e.target.value)}\n              rows={3}\n            />\n          </div>\n\n          <div className=\"flex justify-end space-x-2 pt-4\">\n            <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? 'Saving...' : asset ? 'Update Asset' : 'Create Asset'}\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBe,SAAS,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAkB;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,OAAO,QAAQ;QACrB,aAAa,OAAO,eAAe;QACnC,eAAe,OAAO,iBAAiB;QACvC,gBAAgB,OAAO,kBAAkB;QACzC,eAAe,OAAO,iBAAiB;QACvC,YAAY,OAAO,cAAc;QACjC,UAAU,OAAO,YAAY;IAC/B;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,UAAU;QACV,aAAa;IACf;IAIA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,IAAI;YACJ,IAAI,OAAO;gBACT,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE,EAAE;YACvC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;YAC7B;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,uBAAuB,OAAO,KAAK;gBACjD,MAAM,yBAAyB,OAAO,KAAK,CAAC,OAAO;gBACnD;YACF;YAEA,wEAAwE;YACxE,IAAI,WAAW,CAAC,SAAS,UAAU,KAAK,iBAAiB,SAAS,UAAU,KAAK,SAAS,KAAK,CAAC,OAAO;gBACrG,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;oBACvC,MAAM,SAAS,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;oBAC9C,gBAAgB,SAAS,UAAU,KAAK,gBAAgB,aAAa;oBACrE,kBAAkB,SAAS,gBAAgB;oBAC3C,iBAAiB,SAAS,eAAe,IAAI,SAAS,gBAAgB;oBACtE,eAAe,SAAS,aAAa;oBACrC,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW,IAAI,CAAC,SAAS,EAAE,SAAS,IAAI,EAAE;gBAClE;gBAEA,IAAI,WAAW,KAAK,EAAE;oBACpB,QAAQ,KAAK,CAAC,wBAAwB,WAAW,KAAK;oBACtD,MAAM,8CAA8C,WAAW,KAAK,CAAC,OAAO;gBAC9E;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,yBAAyB,AAAC,MAAgB,OAAO;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,8CAA8C;QAC9C,IAAI,CAAC,UAAU,mBAAmB,UAAU,gBAAgB,KAAK,OAAO,UAAU,UAAU;YAC1F,0DAA0D;YAC1D,IAAI,QAAQ,cAAc;gBACxB,MAAM;gBACN;YACF;QACF;QACA,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8BAAE,QAAQ,eAAe;;;;;;;;;;;0BAErC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,aAAa,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACpD,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa;;;;;;sDAC5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,IAAG;4CACH,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC1D,QAAQ;;8DAER,8OAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAmB;;;;;;8DACjC,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAK5B,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC3D,MAAM;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC7E,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,aAAa,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;8CAIlF,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,IAAG;4CACH,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;;8DAExD,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAK1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;kDAC/B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;wBAQlE,CAAC,SAAS,UAAU,KAAK,iBAAiB,SAAS,UAAU,KAAK,SAAS,KAAK,CAAC,uBAChF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS;4CACT,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,OAAO;4CAC5C,WAAU;;;;;;sDAEZ,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAU;gDACjB,SAAS,UAAU,KAAK,gBAAgB,4BAA4B;;;;;;;;;;;;;gCAI7E,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDACX,SAAS,UAAU,KAAK,gBAAgB,aAAa;gDAAe;;;;;;;sDAGvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,aAAa,GAAG,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,UAAU,KAAK,gBAAgB,aAAa,QAAQ;;;;;;;;;;;;8DAIlG,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAkB;;;;;;sEACjC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,gBAAgB;4DAChC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE,CAAC;4DACpG,UAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,eAAe;4DAC/B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE,CAAC;4DACnG,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,aAAa;4DAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE,CAAC;;;;;;;;;;;;8DAIrG,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;sEAC7B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;;;;;;;;;;;;;;;;;;sDAKjF,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC9E,MAAM;oDACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAQxB,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC3D,MAAM;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAAU;;;;;;8CAG3D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,UAAU,cAAc,QAAQ,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE", "debugId": null}}, {"offset": {"line": 3475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/assets/AssetList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport { Edit, Trash2, Plus } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'\nimport SearchInput from '@/components/ui/SearchInput'\nimport FilterDropdown from '@/components/ui/FilterDropdown'\nimport { getAssets, deleteAsset } from '@/lib/api/assets'\nimport { formatCurrency, formatDate } from '@/lib/utils'\nimport AssetForm from './AssetForm'\nimport type { Asset } from '@/lib/types/database'\n\nexport default function AssetList() {\n  const [assets, setAssets] = useState<Asset[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [editingAsset, setEditingAsset] = useState<Asset | undefined>()\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filters, setFilters] = useState({\n    asset_type: '',\n    min_value: '',\n    max_value: '',\n    purchase_date_from: '',\n    purchase_date_to: ''\n  })\n\n  useEffect(() => {\n    loadAssets()\n  }, [])\n\n  const loadAssets = async () => {\n    setLoading(true)\n    const { data } = await getAssets()\n    if (data) setAssets(data)\n    setLoading(false)\n  }\n\n  const handleDelete = async (id: string) => {\n    if (confirm('Are you sure you want to delete this asset?')) {\n      await deleteAsset(id)\n      loadAssets()\n    }\n  }\n\n  const handleEdit = (asset: Asset) => {\n    setEditingAsset(asset)\n    setShowForm(true)\n  }\n\n  const handleFormSuccess = () => {\n    setShowForm(false)\n    setEditingAsset(undefined)\n    loadAssets()\n  }\n\n  const handleFormCancel = () => {\n    setShowForm(false)\n    setEditingAsset(undefined)\n  }\n\n  // Filter and search assets\n  const filteredAssets = useMemo(() => {\n    return assets.filter(asset => {\n      // Search filter\n      const matchesSearch = searchTerm === '' ||\n        asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        asset.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        asset.asset_type.toLowerCase().includes(searchTerm.toLowerCase())\n\n      // Type filter\n      const matchesType = filters.asset_type === '' || asset.asset_type === filters.asset_type\n\n      // Value filters\n      const matchesMinValue = filters.min_value === '' || asset.current_value >= parseFloat(filters.min_value)\n      const matchesMaxValue = filters.max_value === '' || asset.current_value <= parseFloat(filters.max_value)\n\n      // Date filters\n      const matchesFromDate = filters.purchase_date_from === '' ||\n        !asset.purchase_date ||\n        new Date(asset.purchase_date) >= new Date(filters.purchase_date_from)\n      const matchesToDate = filters.purchase_date_to === '' ||\n        !asset.purchase_date ||\n        new Date(asset.purchase_date) <= new Date(filters.purchase_date_to)\n\n      return matchesSearch && matchesType && matchesMinValue && matchesMaxValue && matchesFromDate && matchesToDate\n    })\n  }, [assets, searchTerm, filters])\n\n  const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.current_value, 0)\n\n  const filterOptions = [\n    {\n      key: 'asset_type',\n      label: 'Asset Type',\n      type: 'select' as const,\n      options: [\n        { value: 'investment', label: 'Investment' },\n        { value: 'real_estate', label: 'Real Estate' },\n        { value: 'vehicle', label: 'Vehicle' },\n        { value: 'cash', label: 'Cash' },\n        { value: 'other', label: 'Other' }\n      ]\n    },\n    {\n      key: 'min_value',\n      label: 'Minimum Value',\n      type: 'number' as const\n    },\n    {\n      key: 'max_value',\n      label: 'Maximum Value',\n      type: 'number' as const\n    },\n    {\n      key: 'purchase_date_from',\n      label: 'Purchase Date From',\n      type: 'date' as const\n    },\n    {\n      key: 'purchase_date_to',\n      label: 'Purchase Date To',\n      type: 'date' as const\n    }\n  ]\n\n  const handleFilterChange = (key: string, value: any) => {\n    setFilters(prev => ({ ...prev, [key]: value }))\n  }\n\n  const handleClearFilters = () => {\n    setFilters({\n      asset_type: '',\n      min_value: '',\n      max_value: '',\n      purchase_date_from: '',\n      purchase_date_to: ''\n    })\n    setSearchTerm('')\n  }\n\n  if (showForm) {\n    return (\n      <div className=\"flex justify-center\">\n        <AssetForm\n          asset={editingAsset}\n          onSuccess={handleFormSuccess}\n          onCancel={handleFormCancel}\n        />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Assets</h1>\n          <p className=\"text-gray-600\">\n            Total Value: {formatCurrency(totalValue)}\n            {filteredAssets.length !== assets.length && (\n              <span className=\"text-sm text-gray-500 ml-2\">\n                ({filteredAssets.length} of {assets.length} shown)\n              </span>\n            )}\n          </p>\n        </div>\n        <Button onClick={() => setShowForm(true)}>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Asset\n        </Button>\n      </div>\n\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <SearchInput\n          value={searchTerm}\n          onChange={setSearchTerm}\n          placeholder=\"Search assets...\"\n          className=\"flex-1\"\n        />\n        <FilterDropdown\n          filters={filterOptions}\n          values={filters}\n          onChange={handleFilterChange}\n          onClear={handleClearFilters}\n        />\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Your Assets</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"flex justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          ) : filteredAssets.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-500\">\n              {assets.length === 0\n                ? \"No assets found. Add your first asset to get started.\"\n                : \"No assets match your search criteria.\"\n              }\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Name</TableHead>\n                  <TableHead>Type</TableHead>\n                  <TableHead>Current Value</TableHead>\n                  <TableHead>Purchase Value</TableHead>\n                  <TableHead>Purchase Date</TableHead>\n                  <TableHead>Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {filteredAssets.map((asset) => (\n                  <TableRow key={asset.id}>\n                    <TableCell className=\"font-medium\">{asset.name}</TableCell>\n                    <TableCell className=\"capitalize\">{asset.asset_type.replace('_', ' ')}</TableCell>\n                    <TableCell>{formatCurrency(asset.current_value, asset.currency)}</TableCell>\n                    <TableCell>\n                      {asset.purchase_value ? formatCurrency(asset.purchase_value, asset.currency) : '-'}\n                    </TableCell>\n                    <TableCell>\n                      {asset.purchase_date ? formatDate(asset.purchase_date) : '-'}\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex space-x-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleEdit(asset)}\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDelete(asset.id)}\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,YAAY;QACZ,WAAW;QACX,WAAW;QACX,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,WAAW;QACX,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD;QAC/B,IAAI,MAAM,UAAU;QACpB,WAAW;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,gDAAgD;YAC1D,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;YAClB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB;QAChB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,gBAAgB;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,gBAAgB;IAClB;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,gBAAgB;YAChB,MAAM,gBAAgB,eAAe,MACnC,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,MAAM,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAEhE,cAAc;YACd,MAAM,cAAc,QAAQ,UAAU,KAAK,MAAM,MAAM,UAAU,KAAK,QAAQ,UAAU;YAExF,gBAAgB;YAChB,MAAM,kBAAkB,QAAQ,SAAS,KAAK,MAAM,MAAM,aAAa,IAAI,WAAW,QAAQ,SAAS;YACvG,MAAM,kBAAkB,QAAQ,SAAS,KAAK,MAAM,MAAM,aAAa,IAAI,WAAW,QAAQ,SAAS;YAEvG,eAAe;YACf,MAAM,kBAAkB,QAAQ,kBAAkB,KAAK,MACrD,CAAC,MAAM,aAAa,IACpB,IAAI,KAAK,MAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,kBAAkB;YACtE,MAAM,gBAAgB,QAAQ,gBAAgB,KAAK,MACjD,CAAC,MAAM,aAAa,IACpB,IAAI,KAAK,MAAM,aAAa,KAAK,IAAI,KAAK,QAAQ,gBAAgB;YAEpE,OAAO,iBAAiB,eAAe,mBAAmB,mBAAmB,mBAAmB;QAClG;IACF,GAAG;QAAC;QAAQ;QAAY;KAAQ;IAEhC,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,aAAa,EAAE;IAEpF,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,MAAM;YACN,SAAS;gBACP;oBAAE,OAAO;oBAAc,OAAO;gBAAa;gBAC3C;oBAAE,OAAO;oBAAe,OAAO;gBAAc;gBAC7C;oBAAE,OAAO;oBAAW,OAAO;gBAAU;gBACrC;oBAAE,OAAO;oBAAQ,OAAO;gBAAO;gBAC/B;oBAAE,OAAO;oBAAS,OAAO;gBAAQ;aAClC;QACH;QACA;YACE,KAAK;YACL,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,qBAAqB,CAAC,KAAa;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC/C;IAEA,MAAM,qBAAqB;QACzB,WAAW;YACT,YAAY;YACZ,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,kBAAkB;QACpB;QACA,cAAc;IAChB;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;gBACR,OAAO;gBACP,WAAW;gBACX,UAAU;;;;;;;;;;;IAIlB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAgB;oCACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;oCAC5B,eAAe,MAAM,KAAK,OAAO,MAAM,kBACtC,8OAAC;wCAAK,WAAU;;4CAA6B;4CACzC,eAAe,MAAM;4CAAC;4CAAK,OAAO,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAKnD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,YAAY;;0CACjC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAW;wBACV,OAAO;wBACP,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC,0IAAA,CAAA,UAAc;wBACb,SAAS;wBACT,QAAQ;wBACR,UAAU;wBACV,SAAS;;;;;;;;;;;;0BAIb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACT,wBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,eAAe,MAAM,KAAK,kBAC5B,8OAAC;4BAAI,WAAU;sCACZ,OAAO,MAAM,KAAK,IACf,0DACA;;;;;iDAIN,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,MAAM,IAAI;;;;;;8DAC9C,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAc,MAAM,UAAU,CAAC,OAAO,CAAC,KAAK;;;;;;8DACjE,8OAAC,iIAAA,CAAA,YAAS;8DAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa,EAAE,MAAM,QAAQ;;;;;;8DAC9D,8OAAC,iIAAA,CAAA,YAAS;8DACP,MAAM,cAAc,GAAG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,cAAc,EAAE,MAAM,QAAQ,IAAI;;;;;;8DAEjF,8OAAC,iIAAA,CAAA,YAAS;8DACP,MAAM,aAAa,GAAG,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,aAAa,IAAI;;;;;;8DAE3D,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,WAAW;0EAE1B,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,aAAa,MAAM,EAAE;0EAEpC,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAxBX,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCzC", "debugId": null}}]}