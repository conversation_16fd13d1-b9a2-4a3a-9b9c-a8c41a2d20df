(()=>{var e={};e.id=22,e.ids=[22],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9254:(e,s,t)=>{Promise.resolve().then(t.bind(t,63087)),Promise.resolve().then(t.bind(t,68962))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12640:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38363:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75900)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\reports\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60108:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\reports\\\\FinancialReports.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\reports\\FinancialReports.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68962:(e,s,t)=>{"use strict";t.d(s,{default:()=>u});var r=t(60687),a=t(43210),n=t(26001);let l=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var i=t(25541),d=t(12640),o=t(53411),c=t(40228),m=t(2643),x=t(28749),p=t(70695),h=t(4780);function u(){let[e,s]=(0,a.useState)(!0),[t,u]=(0,a.useState)("monthly"),[g,j]=(0,a.useState)([]),[v,f]=(0,a.useState)([]),[N,y]=(0,a.useState)([]),[b,w]=(0,a.useState)({start:new Date(new Date().getFullYear(),new Date().getMonth(),1).toISOString().split("T")[0],end:new Date().toISOString().split("T")[0]}),C=()=>g[g.length-1]||{period:"",income:0,expenses:0,netIncome:0,assets:0,liabilities:0,netWorth:0},k=(e,s)=>0===s?0:(e-s)/s*100;if(e)return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-300 rounded"}),(0,r.jsx)("div",{className:"h-32 bg-gray-300 rounded"}),(0,r.jsx)("div",{className:"h-32 bg-gray-300 rounded"})]})]})});let P=C(),_=g[g.length-2]||C();return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Financial Reports"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Analyze your financial performance and trends"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 items-start sm:items-center",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,r.jsx)("label",{className:"text-sm font-medium whitespace-nowrap",children:"From:"}),(0,r.jsx)("input",{type:"date",value:b.start,onChange:e=>w(s=>({...s,start:e.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 items-start sm:items-center",children:[(0,r.jsx)("label",{className:"text-sm font-medium whitespace-nowrap",children:"To:"}),(0,r.jsx)("input",{type:"date",value:b.end,onChange:e=>w(s=>({...s,end:e.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm w-full sm:w-auto"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,r.jsxs)(p.l,{value:t,onChange:e=>{u(e.target.value);let s=new Date;if("weekly"===e.target.value){let e=new Date(s.setDate(s.getDate()-s.getDay())),t=new Date(s.setDate(e.getDate()+6));w({start:e.toISOString().split("T")[0],end:t.toISOString().split("T")[0]})}else"monthly"===e.target.value&&w({start:new Date(s.getFullYear(),s.getMonth(),1).toISOString().split("T")[0],end:new Date(s.getFullYear(),s.getMonth()+1,0).toISOString().split("T")[0]})},className:"w-full sm:w-auto",children:[(0,r.jsx)("option",{value:"custom",children:"Custom Range"}),(0,r.jsx)("option",{value:"weekly",children:"This Week"}),(0,r.jsx)("option",{value:"monthly",children:"This Month"}),(0,r.jsx)("option",{value:"quarterly",children:"This Quarter"}),(0,r.jsx)("option",{value:"yearly",children:"This Year"})]}),(0,r.jsxs)(m.$,{onClick:()=>{console.log("Exporting report...")},variant:"outline",className:"w-full sm:w-auto",children:[(0,r.jsx)(l,{className:"h-4 w-4 mr-2"}),"Export"]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6",children:[(0,r.jsx)(x.Card,{className:"bg-gradient-to-br from-green-50 to-emerald-100 border-green-200",children:(0,r.jsx)(x.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Net Income"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-700",children:(0,h.vv)(P.netIncome,"LKR")}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[k(P.netIncome,_.netIncome)>=0?(0,r.jsx)(i.A,{className:"h-4 w-4 text-green-600 mr-1"}):(0,r.jsx)(d.A,{className:"h-4 w-4 text-red-600 mr-1"}),(0,r.jsxs)("span",{className:`text-sm ${k(P.netIncome,_.netIncome)>=0?"text-green-600":"text-red-600"}`,children:[Math.abs(k(P.netIncome,_.netIncome)).toFixed(1),"%"]})]})]}),(0,r.jsx)("div",{className:"p-3 bg-green-200 rounded-xl",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-green-700"})})]})})}),(0,r.jsx)(x.Card,{className:"bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200",children:(0,r.jsx)(x.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Net Worth"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-blue-700",children:(0,h.vv)(P.netWorth,"LKR")}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[k(P.netWorth,_.netWorth)>=0?(0,r.jsx)(i.A,{className:"h-4 w-4 text-blue-600 mr-1"}):(0,r.jsx)(d.A,{className:"h-4 w-4 text-red-600 mr-1"}),(0,r.jsxs)("span",{className:`text-sm ${k(P.netWorth,_.netWorth)>=0?"text-blue-600":"text-red-600"}`,children:[Math.abs(k(P.netWorth,_.netWorth)).toFixed(1),"%"]})]})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-200 rounded-xl",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-blue-700"})})]})})}),(0,r.jsx)(x.Card,{className:"bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200",children:(0,r.jsx)(x.CardContent,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-purple-600 text-sm font-medium",children:"Savings Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-purple-700",children:[P.income>0?(P.netIncome/P.income*100).toFixed(1):0,"%"]}),(0,r.jsxs)("div",{className:"flex items-center mt-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 text-purple-600 mr-1"}),(0,r.jsx)("span",{className:"text-sm text-purple-600",children:"This period"})]})]}),(0,r.jsx)("div",{className:"p-3 bg-purple-200 rounded-xl",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-purple-700"})})]})})})]}),(0,r.jsxs)(x.Card,{children:[(0,r.jsx)(x.CardHeader,{children:(0,r.jsx)(x.CardTitle,{children:"Income vs Expenses Trend"})}),(0,r.jsx)(x.CardContent,{children:(0,r.jsx)("div",{className:"space-y-4",children:g.map((e,s)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*s},className:"flex items-center justify-between p-4 border rounded-xl",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold",children:e.period}),(0,r.jsxs)("div",{className:"flex space-x-6 mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),(0,r.jsxs)("span",{className:"text-sm",children:["Income: ",(0,h.vv)(e.income,"LKR")]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-2"}),(0,r.jsxs)("span",{className:"text-sm",children:["Expenses: ",(0,h.vv)(e.expenses,"LKR")]})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"font-semibold text-lg",children:(0,h.vv)(e.netIncome,"LKR")}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Net Income"})]})]},e.period))})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[(0,r.jsxs)(x.Card,{children:[(0,r.jsx)(x.CardHeader,{children:(0,r.jsx)(x.CardTitle,{children:"Income Breakdown"})}),(0,r.jsx)(x.CardContent,{children:(0,r.jsx)("div",{className:"space-y-4",children:v.map((e,s)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:"font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"font-semibold",children:(0,h.vv)(e.amount,"LKR")}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.percentage,"%"]})]})]},e.category))})})]}),(0,r.jsxs)(x.Card,{children:[(0,r.jsx)(x.CardHeader,{children:(0,r.jsx)(x.CardTitle,{children:"Expense Breakdown"})}),(0,r.jsx)(x.CardContent,{children:(0,r.jsx)("div",{className:"space-y-4",children:N.map((e,s)=>(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,r.jsx)("span",{className:"font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"font-semibold",children:(0,h.vv)(e.amount,"LKR")}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.percentage,"%"]})]})]},e.category))})})]})]})]})}t(99537),t(71769),t(86945),t(83709),t(42308)},69086:(e,s,t)=>{Promise.resolve().then(t.bind(t,46655)),Promise.resolve().then(t.bind(t,60108))},74075:e=>{"use strict";e.exports=require("zlib")},75900:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),a=t(46655),n=t(60108);function l(){return(0,r.jsx)(a.default,{children:(0,r.jsx)(n.default,{})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,145,79,814,435,235],()=>t(38363));module.exports=r})();