'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createReceivable, updateReceivable, getReceivableCategories } from '@/lib/api/receivables'
import type { Receivable, Category } from '@/lib/types/database'

interface ReceivableFormProps {
  receivable?: Receivable
  onSuccess: () => void
  onCancel: () => void
}

export default function ReceivableForm({ receivable, onSuccess, onCancel }: ReceivableFormProps) {
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState({
    debtor_name: receivable?.debtor_name || '',
    debtor_contact: receivable?.debtor_contact || '',
    description: receivable?.description || '',
    principal_amount: receivable?.principal_amount || 0,
    current_balance: receivable?.current_balance || 0,
    interest_rate: receivable?.interest_rate || 0,
    due_date: receivable?.due_date || '',
    status: receivable?.status || 'active' as const,
    category_id: receivable?.category_id || '',
    currency: receivable?.currency || 'LKR',
  })

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    // Auto-set current balance to principal amount for new receivables
    if (!receivable && formData.principal_amount > 0 && formData.current_balance === 0) {
      setFormData(prev => ({ ...prev, current_balance: prev.principal_amount }))
    }
  }, [formData.principal_amount, receivable])

  const loadCategories = async () => {
    const { data } = await getReceivableCategories()
    if (data) setCategories(data)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let result
      if (receivable) {
        result = await updateReceivable(receivable.id, formData)
      } else {
        result = await createReceivable(formData)
      }

      if (result.error) {
        console.error('Error saving receivable:', result.error)
        alert('Error saving receivable: ' + result.error.message)
        return
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving receivable:', error)
      alert('Error saving receivable: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{receivable ? 'Edit Receivable' : 'Add New Receivable'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="debtor_name">Debtor Name *</Label>
              <Input
                id="debtor_name"
                value={formData.debtor_name}
                onChange={(e) => handleChange('debtor_name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="debtor_contact">Contact Information</Label>
              <Input
                id="debtor_contact"
                value={formData.debtor_contact}
                onChange={(e) => handleChange('debtor_contact', e.target.value)}
                placeholder="Phone, email, or address"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              placeholder="What was this money lent for?"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="principal_amount">Principal Amount *</Label>
              <Input
                id="principal_amount"
                type="number"
                step="0.01"
                value={formData.principal_amount}
                onChange={(e) => handleChange('principal_amount', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="current_balance">Current Balance *</Label>
              <Input
                id="current_balance"
                type="number"
                step="0.01"
                value={formData.current_balance}
                onChange={(e) => handleChange('current_balance', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="interest_rate">Interest Rate (%)</Label>
              <Input
                id="interest_rate"
                type="number"
                step="0.01"
                value={formData.interest_rate}
                onChange={(e) => handleChange('interest_rate', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => handleChange('due_date', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                id="status"
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
              >
                <option value="active">Active</option>
                <option value="paid">Paid</option>
                <option value="written_off">Written Off</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
              >
                <option value="LKR">LKR (Sri Lankan Rupee)</option>
                <option value="USD">USD (US Dollar)</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="category_id">Category</Label>
            <Select
              id="category_id"
              value={formData.category_id}
              onChange={(e) => handleChange('category_id', e.target.value)}
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </Select>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : receivable ? 'Update Receivable' : 'Create Receivable'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
