(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3761:(e,r,t)=>{"use strict";t.d(r,{default:()=>m});var s=t(60687),n=t(43210),a=t(26001),i=t(62688);let o=(0,i.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),l=(0,i.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var d=t(28749),c=t(79481);async function p(e){let r=(0,c.U)(),{data:{user:t}}=await r.auth.getUser();if(!t)throw Error("User not authenticated");let{data:s,error:n}=await r.from("profiles").update(e).eq("id",t.id).select().single();return{data:s,error:n}}async function u(e){return p({preferred_currency:e})}let h={LKR:{code:"LKR",name:"Sri Lankan Rupee",symbol:"Rs.",locale:"si-LK"},USD:{code:"USD",name:"US Dollar",symbol:"$",locale:"en-US"}};function m(){let[e,r]=(0,n.useState)(null),[t,i]=(0,n.useState)(!0),[c,p]=(0,n.useState)(!1),m=async e=>{p(!0);try{let{data:t}=await u(e);t&&(r(t),window.location.reload())}catch(e){console.error("Error updating currency:",e)}finally{p(!1)}};return t?(0,s.jsx)(d.Card,{children:(0,s.jsx)(d.CardContent,{className:"p-8",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"h-16 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-16 bg-gray-300 rounded"})]})]})})}):(0,s.jsxs)(d.Card,{children:[(0,s.jsx)(d.CardHeader,{children:(0,s.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,s.jsx)(o,{className:"h-5 w-5 mr-2"}),"Currency Settings"]})}),(0,s.jsx)(d.CardContent,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"Choose your preferred currency for displaying amounts throughout the application."}),(0,s.jsx)("div",{className:"grid gap-4",children:Object.entries(h).map(([r,t])=>{let n=e?.preferred_currency===r;return(0,s.jsx)(a.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>m(r),disabled:c||n,className:`
                    p-4 rounded-xl border-2 transition-all duration-200 text-left
                    ${n?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}
                    ${c?"opacity-50 cursor-not-allowed":""}
                  `,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:`
                        w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg
                        ${n?"bg-blue-500 text-white":"bg-gray-100 text-gray-600"}
                      `,children:t.symbol}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:t.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:r})]})]}),n&&(0,s.jsx)(a.P.div,{initial:{scale:0},animate:{scale:1},className:"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(l,{className:"h-4 w-4 text-white"})})]})},r)})}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-xl",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Currency Information"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• LKR (Sri Lankan Rupee) is the default currency"}),(0,s.jsx)("li",{children:"• USD (US Dollar) is also supported"}),(0,s.jsx)("li",{children:"• You can change your currency preference at any time"}),(0,s.jsx)("li",{children:"• Existing data will be displayed in your selected currency"})]})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23049:(e,r,t)=>{Promise.resolve().then(t.bind(t,46655)),Promise.resolve().then(t.bind(t,46336)),Promise.resolve().then(t.bind(t,71091))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},46336:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\settings\\\\CurrencySettings.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\settings\\CurrencySettings.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64905:(e,r,t)=>{Promise.resolve().then(t.bind(t,63087)),Promise.resolve().then(t.bind(t,3761)),Promise.resolve().then(t.bind(t,28749))},71091:(e,r,t)=>{"use strict";t.d(r,{Card:()=>n,CardContent:()=>o,CardHeader:()=>a,CardTitle:()=>i});var s=t(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","Card"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","CardHeader");(0,s.registerClientReference)(function(){throw Error("Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","CardFooter");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","CardTitle");(0,s.registerClientReference)(function(){throw Error("Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","CardDescription");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\ui\\Card.tsx","CardContent")},74075:e=>{"use strict";e.exports=require("zlib")},74198:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(46655),a=t(71091),i=t(46336);function o(){return(0,s.jsx)(n.default,{children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Settings"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your account and application preferences"})]}),(0,s.jsx)(i.default,{}),(0,s.jsxs)(a.Card,{children:[(0,s.jsx)(a.CardHeader,{children:(0,s.jsx)(a.CardTitle,{children:"Account Settings"})}),(0,s.jsx)(a.CardContent,{children:(0,s.jsx)("p",{className:"text-gray-600",children:"Additional account settings will be implemented in future updates."})})]}),(0,s.jsxs)(a.Card,{children:[(0,s.jsx)(a.CardHeader,{children:(0,s.jsx)(a.CardTitle,{children:"Application Info"})}),(0,s.jsx)(a.CardContent,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Version:"})," 2.0.0"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Built with:"})," Next.js, Supabase, TypeScript, Tailwind CSS, Framer Motion"]}),(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Features:"})}),(0,s.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,s.jsx)("li",{children:"Transaction Management"}),(0,s.jsx)("li",{children:"Asset Management"}),(0,s.jsx)("li",{children:"Liability Tracking"}),(0,s.jsx)("li",{children:"Receivables Management"}),(0,s.jsx)("li",{children:"Category Management"}),(0,s.jsx)("li",{children:"Net Worth Calculation"}),(0,s.jsx)("li",{children:"Multi-Currency Support (LKR/USD)"}),(0,s.jsx)("li",{children:"Search and Filtering"}),(0,s.jsx)("li",{children:"Premium UI with Animations"}),(0,s.jsx)("li",{children:"Responsive Design"}),(0,s.jsx)("li",{children:"Secure Authentication"})]})]})})]})]})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86051:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74198)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\settings\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,145,79,814,435,235],()=>t(86051));module.exports=s})();