(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[942],{4633:(e,a,s)=>{"use strict";s.d(a,{default:()=>v});var t=s(95155),r=s(12115),l=s(76408),n=s(19946);let d=(0,n.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),i=(0,n.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var c=s(84616),o=s(16785),u=s(33109),x=s(13741),h=s(17703),m=s(93915),g=s(33985),j=s(30353),y=s(59434);function v(){let[e,a]=(0,r.useState)([]),[s,n]=(0,r.useState)([]),[v,p]=(0,r.useState)(!1),[f,b]=(0,r.useState)(!1),[N,C]=(0,r.useState)(!0),[w,_]=(0,r.useState)({category:"",budgeted_amount:0,period:"monthly",currency:"LKR"}),[k,S]=(0,r.useState)({name:"",target_amount:0,target_date:"",currency:"LKR"});(0,r.useEffect)(()=>{A()},[]);let A=async()=>{C(!0);try{a([]),n([])}catch(e){console.error("Error loading budget data:",e)}finally{C(!1)}},F=e=>{let a=e.spent_amount/e.budgeted_amount*100;return a>100?{status:"over",color:"text-red-600 bg-red-50",icon:d}:a>80?{status:"warning",color:"text-yellow-600 bg-yellow-50",icon:d}:{status:"good",color:"text-green-600 bg-green-50",icon:i}},D=e=>e.current_amount/e.target_amount*100;return N?(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-300 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6",children:[(0,t.jsx)("div",{className:"h-64 bg-gray-300 rounded"}),(0,t.jsx)("div",{className:"h-64 bg-gray-300 rounded"})]})]})}):(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Budget Planner"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Track your spending and achieve your financial goals"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsxs)(x.$,{onClick:()=>p(!0),variant:"outline",className:"w-full sm:w-auto",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Budget"]}),(0,t.jsxs)(x.$,{onClick:()=>b(!0),variant:"gradient",className:"w-full sm:w-auto",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Goal"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Monthly Budgets"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"space-y-4",children:e.map(e=>{let a=F(e),s=Math.min(e.spent_amount/e.budgeted_amount*100,100),r=a.icon;return(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"p-4 border rounded-xl hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.category}),(0,t.jsxs)("div",{className:"flex items-center px-2 py-1 rounded-full text-xs ".concat(a.color),children:[(0,t.jsx)(r,{className:"h-3 w-3 mr-1"}),a.status]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{children:["Spent: ",(0,y.vv)(e.spent_amount,e.currency)]}),(0,t.jsxs)("span",{children:["Budget: ",(0,y.vv)(e.budgeted_amount,e.currency)]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(s>100?"bg-red-500":s>80?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(s,100),"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[s.toFixed(1),"% used"]})]})]},e.id)})})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Financial Goals"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"space-y-4",children:s.map(e=>{let a=D(e),s=Math.ceil((new Date(e.target_date).getTime()-new Date().getTime())/864e5);return(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"p-4 border rounded-xl hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center text-blue-600 text-xs",children:[(0,t.jsx)(u.A,{className:"h-3 w-3 mr-1"}),s>0?"".concat(s," days left"):"Overdue"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{children:["Saved: ",(0,y.vv)(e.current_amount,e.currency)]}),(0,t.jsxs)("span",{children:["Target: ",(0,y.vv)(e.target_amount,e.currency)]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 bg-blue-500 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(a,100),"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[a.toFixed(1),"% complete"]})]})]},e.id)})})})]})]}),v&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)(h.Card,{className:"w-full max-w-md",children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Add New Budget"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(!1),_({category:"",budgeted_amount:0,period:"monthly",currency:"LKR"})},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"category",children:"Category"}),(0,t.jsx)(m.p,{id:"category",value:w.category,onChange:e=>_(a=>({...a,category:e.target.value})),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"amount",children:"Budget Amount"}),(0,t.jsx)(m.p,{id:"amount",type:"number",step:"0.01",value:w.budgeted_amount,onChange:e=>_(a=>({...a,budgeted_amount:parseFloat(e.target.value)||0})),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"period",children:"Period"}),(0,t.jsxs)(j.l,{id:"period",value:w.period,onChange:e=>_(a=>({...a,period:e.target.value})),children:[(0,t.jsx)("option",{value:"weekly",children:"Weekly"}),(0,t.jsx)("option",{value:"monthly",children:"Monthly"}),(0,t.jsx)("option",{value:"yearly",children:"Yearly"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"currency",children:"Currency"}),(0,t.jsxs)(j.l,{id:"currency",value:w.currency,onChange:e=>_(a=>({...a,currency:e.target.value})),children:[(0,t.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,t.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>p(!1),children:"Cancel"}),(0,t.jsx)(x.$,{type:"submit",children:"Add Budget"})]})]})})]})}),f&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)(h.Card,{className:"w-full max-w-md",children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Add Financial Goal"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),b(!1),S({name:"",target_amount:0,target_date:"",currency:"LKR"})},className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"goalName",children:"Goal Name"}),(0,t.jsx)(m.p,{id:"goalName",value:k.name,onChange:e=>S(a=>({...a,name:e.target.value})),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"targetAmount",children:"Target Amount"}),(0,t.jsx)(m.p,{id:"targetAmount",type:"number",step:"0.01",value:k.target_amount,onChange:e=>S(a=>({...a,target_amount:parseFloat(e.target.value)||0})),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"targetDate",children:"Target Date"}),(0,t.jsx)(m.p,{id:"targetDate",type:"date",value:k.target_date,onChange:e=>S(a=>({...a,target_date:e.target.value})),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(g.J,{htmlFor:"goalCurrency",children:"Currency"}),(0,t.jsxs)(j.l,{id:"goalCurrency",value:k.currency,onChange:e=>S(a=>({...a,currency:e.target.value})),children:[(0,t.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,t.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>b(!1),children:"Cancel"}),(0,t.jsx)(x.$,{type:"submit",children:"Add Goal"})]})]})})]})})]})}},14037:(e,a,s)=>{Promise.resolve().then(s.bind(s,4633)),Promise.resolve().then(s.bind(s,13049))}},e=>{var a=a=>e(e.s=a);e.O(0,[271,874,556,49,441,684,358],()=>a(14037)),_N_E=e.O()}]);