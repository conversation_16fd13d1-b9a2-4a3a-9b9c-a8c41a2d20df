(()=>{var e={};e.id=830,e.ids=[830],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4671:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>h,tree:()=>d});var t=a(65239),r=a(48088),l=a(88170),n=a.n(l),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let d={children:["",{children:["assets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,81334)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\assets\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\assets\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/assets/page",pathname:"/assets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19472:(e,s,a)=>{"use strict";a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\assets\\\\AssetList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\assets\\AssetList.tsx","default")},27844:(e,s,a)=>{Promise.resolve().then(a.bind(a,19472)),Promise.resolve().then(a.bind(a,46655))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36053:(e,s,a)=>{"use strict";a.d(s,{default:()=>f});var t=a(60687),r=a(43210),l=a(96474),n=a(63143),i=a(88233),c=a(2643),d=a(28749),o=a(44547),u=a(99760),h=a(77970),p=a(71769),m=a(4780),x=a(51907),v=a(43949),j=a(70695),y=a(45225),g=a(86945);function _({asset:e,onSuccess:s,onCancel:a}){let[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)({name:e?.name||"",description:e?.description||"",current_value:e?.current_value||0,purchase_value:e?.purchase_value||0,purchase_date:e?.purchase_date||"",asset_type:e?.asset_type||"investment",currency:e?.currency||"LKR"}),[u,h]=(0,r.useState)(!1),[m,_]=(0,r.useState)({name:"",principal_amount:0,current_balance:0,interest_rate:0,due_date:"",description:""}),f=async a=>{a.preventDefault(),n(!0);try{let a;if((a=e?await (0,p.gT)(e.id,i):await (0,p.$o)(i)).error){console.error("Error saving asset:",a.error),alert("Error saving asset: "+a.error.message);return}if(u&&("real_estate"===i.asset_type||"vehicle"===i.asset_type)&&!e){let e=await (0,g.W2)({name:m.name||`${i.name} Loan`,liability_type:"real_estate"===i.asset_type?"mortgage":"loan_taken",principal_amount:m.principal_amount,current_balance:m.current_balance||m.principal_amount,interest_rate:m.interest_rate,due_date:m.due_date,currency:i.currency,description:m.description||`Loan for ${i.name}`});e.error&&(console.error("Error creating loan:",e.error),alert("Asset created but failed to create loan: "+e.error.message))}s()}catch(e){console.error("Error saving asset:",e),alert("Error saving asset: "+e.message)}finally{n(!1)}},b=(e,s)=>{if(("current_value"===e||"purchase_value"===e)&&"number"==typeof s&&s>0xe8d4a50fff)return void alert("Value is too large. Please enter a smaller amount.");o(a=>({...a,[e]:s}))};return(0,t.jsxs)(d.Card,{className:"w-full max-w-2xl",children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsx)(d.CardTitle,{children:e?"Edit Asset":"Add New Asset"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"name",children:"Asset Name *"}),(0,t.jsx)(x.p,{id:"name",value:i.name,onChange:e=>b("name",e.target.value),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"asset_type",children:"Asset Type *"}),(0,t.jsxs)(j.l,{id:"asset_type",value:i.asset_type,onChange:e=>b("asset_type",e.target.value),required:!0,children:[(0,t.jsx)("option",{value:"investment",children:"Investment"}),(0,t.jsx)("option",{value:"real_estate",children:"Real Estate"}),(0,t.jsx)("option",{value:"vehicle",children:"Vehicle"}),(0,t.jsx)("option",{value:"cash",children:"Cash"}),(0,t.jsx)("option",{value:"savings",children:"Savings Account"}),(0,t.jsx)("option",{value:"fixed_deposit",children:"Fixed Deposit"}),(0,t.jsx)("option",{value:"stocks",children:"Stocks"}),(0,t.jsx)("option",{value:"bonds",children:"Bonds"}),(0,t.jsx)("option",{value:"mutual_funds",children:"Mutual Funds"}),(0,t.jsx)("option",{value:"cryptocurrency",children:"Cryptocurrency"}),(0,t.jsx)("option",{value:"jewelry",children:"Jewelry"}),(0,t.jsx)("option",{value:"art_collectibles",children:"Art & Collectibles"}),(0,t.jsx)("option",{value:"business",children:"Business"}),(0,t.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"description",children:"Description"}),(0,t.jsx)(y.T,{id:"description",value:i.description,onChange:e=>b("description",e.target.value),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"current_value",children:"Current Value *"}),(0,t.jsx)(x.p,{id:"current_value",type:"number",step:"0.01",value:i.current_value,onChange:e=>b("current_value",parseFloat(e.target.value)||0),required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"purchase_value",children:"Purchase Value"}),(0,t.jsx)(x.p,{id:"purchase_value",type:"number",step:"0.01",value:i.purchase_value,onChange:e=>b("purchase_value",parseFloat(e.target.value)||0)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"currency",children:"Currency"}),(0,t.jsxs)(j.l,{id:"currency",value:i.currency,onChange:e=>b("currency",e.target.value),children:[(0,t.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,t.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"purchase_date",children:"Purchase Date"}),(0,t.jsx)(x.p,{id:"purchase_date",type:"date",value:i.purchase_date,onChange:e=>b("purchase_date",e.target.value)})]})}),("real_estate"===i.asset_type||"vehicle"===i.asset_type)&&!e&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)("input",{type:"checkbox",id:"hasLoan",checked:u,onChange:e=>h(e.target.checked),className:"rounded border-gray-300"}),(0,t.jsxs)(v.J,{htmlFor:"hasLoan",children:["This ","real_estate"===i.asset_type?"property has a mortgage":"vehicle has a loan"]})]}),u&&(0,t.jsxs)("div",{className:"space-y-4 bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h3",{className:"font-semibold text-lg",children:["real_estate"===i.asset_type?"Mortgage":"Vehicle Loan"," Details"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"loanName",children:"Loan Name"}),(0,t.jsx)(x.p,{id:"loanName",value:m.name,onChange:e=>_(s=>({...s,name:e.target.value})),placeholder:`${i.name} ${"real_estate"===i.asset_type?"Mortgage":"Loan"}`})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"principalAmount",children:"Principal Amount *"}),(0,t.jsx)(x.p,{id:"principalAmount",type:"number",step:"0.01",value:m.principal_amount,onChange:e=>_(s=>({...s,principal_amount:parseFloat(e.target.value)||0})),required:u})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"currentBalance",children:"Current Balance"}),(0,t.jsx)(x.p,{id:"currentBalance",type:"number",step:"0.01",value:m.current_balance,onChange:e=>_(s=>({...s,current_balance:parseFloat(e.target.value)||0})),placeholder:"Leave empty to use principal amount"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"interestRate",children:"Interest Rate (%)"}),(0,t.jsx)(x.p,{id:"interestRate",type:"number",step:"0.01",value:m.interest_rate,onChange:e=>_(s=>({...s,interest_rate:parseFloat(e.target.value)||0}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"loanDueDate",children:"Due Date"}),(0,t.jsx)(x.p,{id:"loanDueDate",type:"date",value:m.due_date,onChange:e=>_(s=>({...s,due_date:e.target.value}))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"loanDescription",children:"Loan Description"}),(0,t.jsx)(y.T,{id:"loanDescription",value:m.description,onChange:e=>_(s=>({...s,description:e.target.value})),rows:2,placeholder:"Additional details about the loan..."})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"description",children:"Asset Description"}),(0,t.jsx)(y.T,{id:"description",value:i.description,onChange:e=>b("description",e.target.value),rows:3})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsx)(c.$,{type:"submit",disabled:l,children:l?"Saving...":e?"Update Asset":"Create Asset"})]})]})})]})}function f(){let[e,s]=(0,r.useState)([]),[a,x]=(0,r.useState)(!0),[v,j]=(0,r.useState)(!1),[y,g]=(0,r.useState)(),[f,b]=(0,r.useState)(""),[N,w]=(0,r.useState)({asset_type:"",min_value:"",max_value:"",purchase_date_from:"",purchase_date_to:""}),C=async()=>{x(!0);let{data:e}=await (0,p.Y)();e&&s(e),x(!1)},A=async e=>{confirm("Are you sure you want to delete this asset?")&&(await (0,p.AO)(e),C())},k=e=>{g(e),j(!0)},P=(0,r.useMemo)(()=>e.filter(e=>{let s=""===f||e.name.toLowerCase().includes(f.toLowerCase())||e.description?.toLowerCase().includes(f.toLowerCase())||e.asset_type.toLowerCase().includes(f.toLowerCase()),a=""===N.asset_type||e.asset_type===N.asset_type,t=""===N.min_value||e.current_value>=parseFloat(N.min_value),r=""===N.max_value||e.current_value<=parseFloat(N.max_value),l=""===N.purchase_date_from||!e.purchase_date||new Date(e.purchase_date)>=new Date(N.purchase_date_from),n=""===N.purchase_date_to||!e.purchase_date||new Date(e.purchase_date)<=new Date(N.purchase_date_to);return s&&a&&t&&r&&l&&n}),[e,f,N]),F=P.reduce((e,s)=>e+s.current_value,0);return v?(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(_,{asset:y,onSuccess:()=>{j(!1),g(void 0),C()},onCancel:()=>{j(!1),g(void 0)}})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Assets"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Total Value: ",(0,m.vv)(F),P.length!==e.length&&(0,t.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",P.length," of ",e.length," shown)"]})]})]}),(0,t.jsxs)(c.$,{onClick:()=>j(!0),className:"w-full sm:w-auto",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Add Asset"]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)(u.A,{value:f,onChange:b,placeholder:"Search assets...",className:"flex-1"}),(0,t.jsx)(h.A,{filters:[{key:"asset_type",label:"Asset Type",type:"select",options:[{value:"investment",label:"Investment"},{value:"real_estate",label:"Real Estate"},{value:"vehicle",label:"Vehicle"},{value:"cash",label:"Cash"},{value:"other",label:"Other"}]},{key:"min_value",label:"Minimum Value",type:"number"},{key:"max_value",label:"Maximum Value",type:"number"},{key:"purchase_date_from",label:"Purchase Date From",type:"date"},{key:"purchase_date_to",label:"Purchase Date To",type:"date"}],values:N,onChange:(e,s)=>{w(a=>({...a,[e]:s}))},onClear:()=>{w({asset_type:"",min_value:"",max_value:"",purchase_date_from:"",purchase_date_to:""}),b("")}})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsx)(d.CardTitle,{children:"Your Assets"})}),(0,t.jsx)(d.CardContent,{children:a?(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===P.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:0===e.length?"No assets found. Add your first asset to get started.":"No assets match your search criteria."}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"hidden lg:block",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"Name"}),(0,t.jsx)(o.nd,{children:"Type"}),(0,t.jsx)(o.nd,{children:"Current Value"}),(0,t.jsx)(o.nd,{children:"Purchase Value"}),(0,t.jsx)(o.nd,{children:"Purchase Date"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:P.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{className:"font-medium",children:e.name}),(0,t.jsx)(o.nA,{className:"capitalize",children:e.asset_type.replace("_"," ")}),(0,t.jsx)(o.nA,{children:(0,m.vv)(e.current_value,e.currency)}),(0,t.jsx)(o.nA,{children:e.purchase_value?(0,m.vv)(e.purchase_value,e.currency):"-"}),(0,t.jsx)(o.nA,{children:e.purchase_date?(0,m.Yq)(e.purchase_date):"-"}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>k(e),children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),(0,t.jsx)("div",{className:"lg:hidden space-y-4",children:P.map(e=>(0,t.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500 capitalize",children:e.asset_type.replace("_"," ")})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>k(e),children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>A(e.id),children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Current Value:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:(0,m.vv)(e.current_value,e.currency)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Purchase Value:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.purchase_value?(0,m.vv)(e.purchase_value,e.currency):"-"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Purchase Date:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.purchase_date?(0,m.Yq)(e.purchase_date):"-"})]})]})]},e.id))})]})})]})]})}},44547:(e,s,a)=>{"use strict";a.d(s,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>n,nA:()=>u,nd:()=>o});var t=a(60687),r=a(43210),l=a(4780);let n=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let i=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let c=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));c.displayName="TableBody",(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("bg-gray-900/5 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",e),...s}));d.displayName="TableRow";let o=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let u=(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));u.displayName="TableCell",(0,r.forwardRef)(({className:e,...s},a)=>(0,t.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-gray-500",e),...s})).displayName="TableCaption"},51812:(e,s,a)=>{Promise.resolve().then(a.bind(a,36053)),Promise.resolve().then(a.bind(a,63087))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},77970:(e,s,a)=>{"use strict";a.d(s,{A:()=>h});var t=a(60687),r=a(43210),l=a(62688);let n=(0,l.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),i=(0,l.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var c=a(2643),d=a(70695),o=a(51907),u=a(43949);function h({filters:e,values:s,onChange:a,onClear:l}){let[h,p]=(0,r.useState)(!1),m=Object.values(s).filter(e=>""!==e&&null!=e).length;return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>p(!h),className:"flex items-center",children:[(0,t.jsx)(n,{className:"h-4 w-4 mr-2"}),"Filters",m>0&&(0,t.jsx)("span",{className:"ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5",children:m}),(0,t.jsx)(i,{className:"h-4 w-4 ml-2"})]}),h&&(0,t.jsx)("div",{className:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50",children:(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Filters"}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{l(),p(!1)},children:"Clear All"})]}),e.map(e=>(0,t.jsxs)("div",{children:[(0,t.jsx)(u.J,{htmlFor:e.key,children:e.label}),"select"===e.type&&e.options?(0,t.jsxs)(d.l,{id:e.key,value:s[e.key]||"",onChange:s=>a(e.key,s.target.value),children:[(0,t.jsx)("option",{value:"",children:"All"}),e.options.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,t.jsx)(o.p,{id:e.key,type:"date"===e.type?"date":"number"===e.type?"number":"text",value:s[e.key]||"",onChange:s=>a(e.key,s.target.value)})]},e.key)),(0,t.jsx)("div",{className:"flex justify-end pt-2",children:(0,t.jsx)(c.$,{size:"sm",onClick:()=>p(!1),children:"Apply Filters"})})]})})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81334:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var t=a(37413),r=a(46655),l=a(19472);function n(){return(0,t.jsx)(r.default,{children:(0,t.jsx)(l.default,{})})}},81630:e=>{"use strict";e.exports=require("http")},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99760:(e,s,a)=>{"use strict";a.d(s,{A:()=>c});var t=a(60687);let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var l=a(11860),n=a(51907),i=a(2643);function c({value:e,onChange:s,placeholder:a="Search...",className:c}){return(0,t.jsxs)("div",{className:`relative ${c}`,children:[(0,t.jsx)(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{type:"text",placeholder:a,value:e,onChange:e=>s(e.target.value),className:"pl-10 pr-10"}),e&&(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>s(""),children:(0,t.jsx)(l.A,{className:"h-4 w-4"})})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,145,79,814,435,235],()=>a(4671));module.exports=t})();