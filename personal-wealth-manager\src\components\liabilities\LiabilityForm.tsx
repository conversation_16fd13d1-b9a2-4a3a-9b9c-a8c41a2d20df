'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createLiability, updateLiability, getLiabilityCategories } from '@/lib/api/liabilities'
import type { Liability, Category } from '@/lib/types/database'

interface LiabilityFormProps {
  liability?: Liability
  onSuccess: () => void
  onCancel: () => void
}

export default function LiabilityForm({ liability, onSuccess, onCancel }: LiabilityFormProps) {
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState({
    name: liability?.name || '',
    description: liability?.description || '',
    principal_amount: liability?.principal_amount || 0,
    current_balance: liability?.current_balance || 0,
    interest_rate: liability?.interest_rate || 0,
    due_date: liability?.due_date || '',
    liability_type: liability?.liability_type || 'personal_loan' as const,
    status: liability?.status || 'active' as const,

    currency: liability?.currency || 'LKR',
  })

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    // Auto-set current balance to principal amount for new liabilities
    if (!liability && formData.principal_amount > 0 && formData.current_balance === 0) {
      setFormData(prev => ({ ...prev, current_balance: prev.principal_amount }))
    }
  }, [formData.principal_amount, liability])

  const loadCategories = async () => {
    const { data } = await getLiabilityCategories()
    if (data) setCategories(data)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let result
      if (liability) {
        result = await updateLiability(liability.id, formData)
      } else {
        result = await createLiability(formData)
      }

      if (result.error) {
        console.error('Error saving liability:', result.error)
        alert('Error saving liability: ' + result.error.message)
        return
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving liability:', error)
      alert('Error saving liability: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{liability ? 'Edit Liability' : 'Add New Liability'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Liability Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="liability_type">Type *</Label>
              <Select
                id="liability_type"
                value={formData.liability_type}
                onChange={(e) => handleChange('liability_type', e.target.value)}
                required
              >
                <option value="credit_card">Credit Card</option>
                <option value="personal_loan">Personal Loan</option>
                <option value="mortgage">Mortgage</option>
                <option value="vehicle_loan">Vehicle Loan</option>
                <option value="student_loan">Student Loan</option>
                <option value="business_loan">Business Loan</option>
                <option value="line_of_credit">Line of Credit</option>
                <option value="overdraft">Bank Overdraft</option>
                <option value="tax_debt">Tax Debt</option>
                <option value="medical_debt">Medical Debt</option>
                <option value="utility_bill">Utility Bill</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="principal_amount">Principal Amount *</Label>
              <Input
                id="principal_amount"
                type="number"
                step="0.01"
                value={formData.principal_amount}
                onChange={(e) => handleChange('principal_amount', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="current_balance">Current Balance *</Label>
              <Input
                id="current_balance"
                type="number"
                step="0.01"
                value={formData.current_balance}
                onChange={(e) => handleChange('current_balance', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="interest_rate">Interest Rate (%)</Label>
              <Input
                id="interest_rate"
                type="number"
                step="0.01"
                value={formData.interest_rate}
                onChange={(e) => handleChange('interest_rate', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="due_date">Due Date</Label>
              <Input
                id="due_date"
                type="date"
                value={formData.due_date}
                onChange={(e) => handleChange('due_date', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                id="status"
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
              >
                <option value="active">Active</option>
                <option value="paid_off">Paid Off</option>
                <option value="defaulted">Defaulted</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
              >
                <option value="LKR">LKR (Sri Lankan Rupee)</option>
                <option value="USD">USD (US Dollar)</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="category_id">Category</Label>
            <Select
              id="category_id"
              value={formData.category_id}
              onChange={(e) => handleChange('category_id', e.target.value)}
            >
              <option value="">Select a category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </Select>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : liability ? 'Update Liability' : 'Create Liability'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
