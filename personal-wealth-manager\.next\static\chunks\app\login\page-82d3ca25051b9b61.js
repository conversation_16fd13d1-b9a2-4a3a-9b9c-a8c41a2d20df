(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{9690:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(95155),r=s(12115),l=s(35695),i=s(6874),n=s.n(i),o=s(79323);function d(){let[e,t]=(0,r.useState)(""),[s,i]=(0,r.useState)(""),[d,c]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),m=(0,l.useRouter)(),x=async t=>{t.preventDefault(),c(!0),h("");let{error:a}=await (0,o.Jv)(e,s);a?(h(a.message),c(!1)):(m.push("/dashboard"),m.refresh())};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0",children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 w-72 h-72 bg-white/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"}),(0,a.jsx)("div",{className:"absolute bottom-0 right-0 w-72 h-72 bg-white/10 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"})]}),(0,a.jsx)("div",{className:"max-w-md w-full space-y-8 relative z-10",children:(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-white rounded-2xl flex items-center justify-center mb-4 shadow-lg",children:(0,a.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"W"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-white",children:"Welcome Back"}),(0,a.jsx)("p",{className:"mt-2 text-white/80",children:"Sign in to your wealth management account"})]}),(0,a.jsxs)("form",{className:"space-y-6",onSubmit:x,children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-white/90 mb-2",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 backdrop-blur-sm",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-white/90 mb-2",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 backdrop-blur-sm",placeholder:"Enter your password",value:s,onChange:e=>i(e.target.value)})]})]}),u&&(0,a.jsx)("div",{className:"bg-red-500/20 border border-red-500/30 text-red-100 text-sm text-center py-3 px-4 rounded-xl backdrop-blur-sm",children:u}),(0,a.jsx)("button",{type:"submit",disabled:d,className:"w-full bg-white text-gray-900 font-semibold py-3 px-4 rounded-xl hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:d?"Signing in...":"Sign in"})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsxs)("p",{className:"text-white/80",children:["Don't have an account?"," ",(0,a.jsx)(n(),{href:"/signup",className:"font-semibold text-white hover:text-white/80 underline underline-offset-4",children:"Create one here"})]})})]})})]})}},52643:(e,t,s)=>{"use strict";s.d(t,{U:()=>r});var a=s(43865);function r(){return(0,a.createBrowserClient)("https://lwfiqyypbdphguadzqbe.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.KaxWQZgd1EBY-ksnefW6pokzLTO4LracWY36dnGC0us")}},79323:(e,t,s)=>{"use strict";s.d(t,{CI:()=>i,Hh:()=>r,Jv:()=>l});var a=s(52643);async function r(e,t,s){let r=(0,a.U)(),{data:l,error:i}=await r.auth.signUp({email:e,password:t,options:{data:{full_name:s}}});return{data:l,error:i}}async function l(e,t){let s=(0,a.U)(),{data:r,error:l}=await s.auth.signInWithPassword({email:e,password:t});return{data:r,error:l}}async function i(){let e=(0,a.U)(),{error:t}=await e.auth.signOut();return{error:t}}},80103:(e,t,s)=>{Promise.resolve().then(s.bind(s,9690))}},e=>{var t=t=>e(e.s=t);e.O(0,[271,874,441,684,358],()=>t(80103)),_N_E=e.O()}]);