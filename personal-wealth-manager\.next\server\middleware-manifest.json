{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4e1c08c4._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_dd15cafc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RMAtdUbsubgDP5IsA8bFq2Vl4TCbSe812PztFsz/xqw=", "__NEXT_PREVIEW_MODE_ID": "d307a5cb7f7195eb1b13e4ba90777358", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c05986c3449381b25a2c917f585f5c2cf8ddc154c0141eba9f5c08b736a92593", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d5abd54c4351157174906715719c84d4f7990797b9abb5d3f95cb52c3986f2b8"}}}, "sortedMiddleware": ["/"], "functions": {}}