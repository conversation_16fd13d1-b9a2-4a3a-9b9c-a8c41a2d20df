(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{40545:(e,r,a)=>{"use strict";a.d(r,{default:()=>y});var s=a(95155),l=a(12115),t=a(76408),n=a(19946);let i=(0,n.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),c=(0,n.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var d=a(17703),o=a(52643);async function u(){let e=(0,o.U)(),{data:{user:r},error:a}=await e.auth.getUser();if(a||!r)return{profile:null,error:a};let{data:s,error:l}=await e.from("profiles").select("*").eq("id",r.id).single();return{profile:s,error:l}}async function h(e){let r=(0,o.U)(),{data:{user:a}}=await r.auth.getUser();if(!a)throw Error("User not authenticated");let{data:s,error:l}=await r.from("profiles").update(e).eq("id",a.id).select().single();return{data:s,error:l}}async function x(e){return h({preferred_currency:e})}let m={LKR:{code:"LKR",name:"Sri Lankan Rupee",symbol:"Rs.",locale:"si-LK"},USD:{code:"USD",name:"US Dollar",symbol:"$",locale:"en-US"}};function y(){let[e,r]=(0,l.useState)(null),[a,n]=(0,l.useState)(!0),[o,h]=(0,l.useState)(!1);(0,l.useEffect)(()=>{y()},[]);let y=async()=>{n(!0);let{profile:e}=await u();e&&r(e),n(!1)},f=async e=>{h(!0);try{let{data:a}=await x(e);a&&(r(a),window.location.reload())}catch(e){console.error("Error updating currency:",e)}finally{h(!1)}};return a?(0,s.jsx)(d.Card,{children:(0,s.jsx)(d.CardContent,{className:"p-8",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"h-16 bg-gray-300 rounded"}),(0,s.jsx)("div",{className:"h-16 bg-gray-300 rounded"})]})]})})}):(0,s.jsxs)(d.Card,{children:[(0,s.jsx)(d.CardHeader,{children:(0,s.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,s.jsx)(i,{className:"h-5 w-5 mr-2"}),"Currency Settings"]})}),(0,s.jsx)(d.CardContent,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600",children:"Choose your preferred currency for displaying amounts throughout the application."}),(0,s.jsx)("div",{className:"grid gap-4",children:Object.entries(m).map(r=>{let[a,l]=r,n=(null==e?void 0:e.preferred_currency)===a;return(0,s.jsx)(t.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>f(a),disabled:o||n,className:"\n                    p-4 rounded-xl border-2 transition-all duration-200 text-left\n                    ".concat(n?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50","\n                    ").concat(o?"opacity-50 cursor-not-allowed":"","\n                  "),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"\n                        w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg\n                        ".concat(n?"bg-blue-500 text-white":"bg-gray-100 text-gray-600","\n                      "),children:l.symbol}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:l.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:a})]})]}),n&&(0,s.jsx)(t.P.div,{initial:{scale:0},animate:{scale:1},className:"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(c,{className:"h-4 w-4 text-white"})})]})},a)})}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-xl",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Currency Information"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• LKR (Sri Lankan Rupee) is the default currency"}),(0,s.jsx)("li",{children:"• USD (US Dollar) is also supported"}),(0,s.jsx)("li",{children:"• You can change your currency preference at any time"}),(0,s.jsx)("li",{children:"• Existing data will be displayed in your selected currency"})]})]})]})})]})}},75447:(e,r,a)=>{Promise.resolve().then(a.bind(a,13049)),Promise.resolve().then(a.bind(a,40545)),Promise.resolve().then(a.bind(a,17703))}},e=>{var r=r=>e(e.s=r);e.O(0,[271,874,556,49,441,684,358],()=>r(75447)),_N_E=e.O()}]);