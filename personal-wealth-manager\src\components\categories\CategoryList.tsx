'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Edit, Trash2, Plus, Tag } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { getCategories, deleteCategory } from '@/lib/api/categories'
import CategoryForm from './CategoryForm'
import type { Category } from '@/lib/types/database'

export default function CategoryList() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | undefined>()
  const [selectedType, setSelectedType] = useState<string>('all')

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    setLoading(true)
    const { data } = await getCategories()
    if (data) setCategories(data)
    setLoading(false)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this category?')) {
      await deleteCategory(id)
      loadCategories()
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingCategory(undefined)
    loadCategories()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingCategory(undefined)
  }

  const filteredCategories = selectedType === 'all' 
    ? categories 
    : categories.filter(cat => cat.type === selectedType)

  const categoryTypes = [
    { value: 'all', label: 'All Categories', count: categories.length },
    { value: 'income', label: 'Income', count: categories.filter(c => c.type === 'income').length },
    { value: 'expense', label: 'Expense', count: categories.filter(c => c.type === 'expense').length },
    { value: 'asset', label: 'Asset', count: categories.filter(c => c.type === 'asset').length },
    { value: 'liability', label: 'Liability', count: categories.filter(c => c.type === 'liability').length },
    { value: 'receivable', label: 'Receivable', count: categories.filter(c => c.type === 'receivable').length },
  ]

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Categories
          </h1>
          <p className="text-gray-600 mt-2">Organize your financial transactions with custom categories</p>
        </div>
        <Button onClick={() => setShowForm(true)} variant="gradient">
          <Plus className="h-5 w-5 mr-2" />
          Add Category
        </Button>
      </div>

      {/* Category Type Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-3">
            {categoryTypes.map((type) => (
              <button
                key={type.value}
                onClick={() => setSelectedType(type.value)}
                className={`
                  px-4 py-2 rounded-xl font-medium transition-all duration-200
                  ${selectedType === type.value
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }
                `}
              >
                {type.label} ({type.count})
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {loading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                    <div className="h-4 bg-gray-300 rounded flex-1"></div>
                  </div>
                  <div className="mt-4 h-3 bg-gray-300 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))
          ) : filteredCategories.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Tag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
              <p className="text-gray-500 mb-6">
                {selectedType === 'all' 
                  ? 'Create your first category to get started'
                  : `No ${selectedType} categories found`
                }
              </p>
              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </div>
          ) : (
            filteredCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        <h3 className="font-semibold text-gray-900">{category.name}</h3>
                      </div>
                      <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(category)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(category.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 capitalize">
                      {category.type.replace('_', ' ')}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>

      {/* Category Form Modal */}
      <AnimatePresence>
        {showForm && (
          <CategoryForm
            category={editingCategory}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
