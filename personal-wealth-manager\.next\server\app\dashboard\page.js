(()=>{var e={};e.id=105,e.ids=[105],e.modules={175:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},1640:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},1706:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5664:(e,t,r)=>{e.exports=r(87509).get},9489:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>d,tree:()=>c});var n=r(65239),i=r(48088),a=r(88170),o=r.n(a),l=r(30893),u={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>l[e]);r.d(t,u);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\dashboard\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10687:(e,t,r)=>{e.exports=r(75446).sortBy},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10907:(e,t,r)=>{"use strict";var n=r(43210),i=r(57379),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,u=n.useEffect,c=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=o(e,(f=c(function(){function e(e){if(!u){if(u=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=h},[h]),s(h),h}},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var d,h=s.length;for(c=0;c<h;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,i);break;default:if(!u)for(d=1,u=Array(f-1);d<f;d++)u[d-1]=arguments[d];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},11997:e=>{"use strict";e.exports=require("punycode")},12640:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},12728:(e,t,r)=>{e.exports=r(92292).isEqual},14454:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(1640),a=r(23457),o=r(1706);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return c(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let c=e[u],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,u){let c=r(t,n,i,a,o,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},t.isSetMatch=c},15708:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95819);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},17617:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92681),i=r(40144),a=r(74838),o=r(30415);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20311:(e,t,r)=>{Promise.resolve().then(r.bind(r,82992)),Promise.resolve().then(r.bind(r,63087))},20911:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,u=null,{leading:c=!1,trailing:s=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(l=e,u=setTimeout(b,t),c&&null!==i)?p(e):n,g=e=>(u=null,s&&null!==i)?p(e):n,v=e=>{if(null===o)return!0;let r=e-o,n=d&&e-l>=h;return r>=t||r<0||n},m=e=>{let r=t-(null===o?0:e-o),n=h-(e-l);return d?Math.min(r,n):r},b=()=>{let e=Date.now();if(v(e))return g(e);u=setTimeout(b,m(e))},x=function(...e){let r=Date.now(),l=v(r);if(i=e,a=this,o=r,l){if(null===u)return y(r);if(d)return clearTimeout(u),u=setTimeout(b,t),p(r)}return null===u&&(u=setTimeout(b,t)),n};return x.cancel=()=>{null!==u&&clearTimeout(u),l=0,o=i=a=u=null},x.flush=()=>null===u?n:g(Date.now()),x}},21251:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},23457:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},23854:(e,t,r)=>{e.exports=r(45263).uniqBy},25887:(e,t,r)=>{Promise.resolve().then(r.bind(r,75185)),Promise.resolve().then(r.bind(r,46655))},26349:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(48130);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},27469:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},27910:e=>{"use strict";e.exports=require("stream")},28382:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},29243:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(91428);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},29862:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92923),i=r(27469);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let u=t?.(r,a,o,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},30415:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(30657),a=r(59138),o=r(87509),l=r(57841);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},30657:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},30921:(e,t,r)=>{e.exports=r(71337).range},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35314:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},36023:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},37586:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(36023),i=r(76021),a=r(43574);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>u(t,e))})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},39733:(e,t,r)=>{"use strict";e.exports=r(10907)},40144:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(87509);t.property=function(e){return function(t){return n.get(t,e)}}},42066:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14454);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},42750:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15708);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},43084:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(20911);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},43574:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},43649:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},45263:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(59618),i=r(92681),a=r(90830),o=r(17617);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},48130:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},49899:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},52371:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92923);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},53332:(e,t,r)=>{"use strict";var n=r(43210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57379:(e,t,r)=>{"use strict";e.exports=r(53332)},57841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(76431),i=r(98150),a=r(29243),o=r(43574);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},59138:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29862);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},59618:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},60324:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66777:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98150),i=r(26349),a=r(1640),o=r(1706);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},67766:(e,t,r)=>{e.exports=r(43084).throttle},69404:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(175),i=r(91653),a=r(91428),o=r(27469),l=r(1706);t.isEqualWith=function(e,t,r){return function e(t,r,u,c,s,f,d){let h=d(t,r,u,c,s,f);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,u,c,s){if(Object.is(r,u))return!0;let f=a.getTag(r),d=a.getTag(u);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return r.toString()===u.toString();case o.numberTag:{let e=r.valueOf(),t=u.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),u.valueOf());case o.regexpTag:return r.source===u.source&&r.flags===u.flags;case o.functionTag:return r===u}let h=(c=c??new Map).get(r),p=c.get(u);if(null!=h&&null!=p)return h===u;c.set(r,u),c.set(u,r);try{switch(f){case o.mapTag:if(r.size!==u.size)return!1;for(let[t,n]of r.entries())if(!u.has(t)||!e(n,u.get(t),t,r,u,c,s))return!1;return!0;case o.setTag:{if(r.size!==u.size)return!1;let t=Array.from(r.values()),n=Array.from(u.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,u,c,s));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(u)||r.length!==u.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],u[t],t,r,u,c,s))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==u.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(u),c,s);case o.dataViewTag:if(r.byteLength!==u.byteLength||r.byteOffset!==u.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(u),c,s);case o.errorTag:return r.name===u.name&&r.message===u.message;case o.objectTag:{if(!(t(r.constructor,u.constructor,c,s)||n.isPlainObject(r)&&n.isPlainObject(u)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(u),...i.getSymbols(u)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(u,n))return!1;let o=u[n];if(!e(i,o,n,r,u,c,s))return!1}return!0}default:return!1}}finally{c.delete(r),c.delete(u)}}(t,r,f,d)}(e,t,void 0,void 0,void 0,void 0,r)}},71337:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(66777),i=r(42750);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},74075:e=>{"use strict";e.exports=require("zlib")},74838:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(52371);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},75185:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\dashboard\\\\Dashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\dashboard\\Dashboard.tsx","default")},75446:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(37586),i=r(28382),a=r(66777);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},76021:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95819),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},76431:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(37413),i=r(46655),a=r(75185);function o(){return(0,n.jsx)(i.default,{children:(0,n.jsx)(a.default,{})})}},81630:e=>{"use strict";e.exports=require("http")},82992:(e,t,r)=>{"use strict";r.d(t,{default:()=>g3});var n={};r.r(n),r.d(n,{scaleBand:()=>it,scaleDiverging:()=>function e(){var t=av(lk()(i9));return t.copy=function(){return lT(t,e())},n6.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=aS(lk()).domain([.1,1,10]);return t.copy=function(){return lT(t,e()).base(t.base())},n6.apply(t,arguments)},scaleDivergingPow:()=>lN,scaleDivergingSqrt:()=>lD,scaleDivergingSymlog:()=>function e(){var t=aM(lk());return t.copy=function(){return lT(t,e()).constant(t.constant())},n6.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,i8),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,i8):[0,1],av(n)},scaleImplicit:()=>n9,scaleLinear:()=>function e(){var t=aa();return t.copy=function(){return an(t,e())},n3.apply(t,arguments),av(t)},scaleLog:()=>function e(){let t=aS(ai()).domain([1,10]);return t.copy=()=>an(t,e()).base(t.base()),n3.apply(t,arguments),t},scaleOrdinal:()=>ie,scalePoint:()=>ir,scalePow:()=>aN,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=ig){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[im(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(id),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},n3.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[im(a,e,0,i)]:t}function u(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,u()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},n3.apply(av(l),arguments)},scaleRadial:()=>function e(){var t,r=aa(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(aI(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,i8)).map(aI)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},n3.apply(a,arguments),av(a)},scaleSequential:()=>function e(){var t=av(lM()(i9));return t.copy=function(){return lT(t,e())},n6.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=aS(lM()).domain([1,10]);return t.copy=function(){return lT(t,e()).base(t.base())},n6.apply(t,arguments)},scaleSequentialPow:()=>lE,scaleSequentialQuantile:()=>function e(){var t=[],r=i9;function n(e){if(null!=e&&!isNaN(e*=1))return r((im(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(id),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return aR(e);if(t>=1)return aL(e);var n,i=(n-1)*t,a=Math.floor(i),o=aL((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?a$:function(e=id){if(e===id)return a$;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),d=Math.min(i,Math.floor(r+(o-l)*c/o+s));e(t,r,f,d,a)}let o=t[r],l=n,u=i;for(aU(t,n,r),a(t[i],o)>0&&aU(t,n,i);l<u;){for(aU(t,l,u),++l,--u;0>a(t[l],o);)++l;for(;a(t[u],o)>0;)--u}0===a(t[n],o)?aU(t,n,u):aU(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,a).subarray(0,a+1));return o+(aR(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},n6.apply(n,arguments)},scaleSequentialSqrt:()=>lC,scaleSequentialSymlog:()=>function e(){var t=aM(lM());return t.copy=function(){return lT(t,e()).constant(t.constant())},n6.apply(t,arguments)},scaleSqrt:()=>aD,scaleSymlog:()=>function e(){var t=aM(ai());return t.copy=function(){return an(t,e()).constant(t.constant())},n3.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[im(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},n3.apply(a,arguments)},scaleTime:()=>lA,scaleUtc:()=>l_,tickFormat:()=>ag});var i=r(60687),a=r(43210),o=r(23576),l=r(28947),u=r(96474),c=r(53411),s=r(25541),f=(r(79481),r(2643)),d=r(28749),h=r(12640),p=r(4780);function y({totalAssets:e,totalLiabilities:t,totalReceivables:r,netWorth:n}){let a=n>=0;return(0,i.jsxs)(d.Card,{className:"col-span-2",children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsxs)(d.CardTitle,{className:"flex items-center",children:["Net Worth",a?(0,i.jsx)(s.A,{className:"ml-2 h-5 w-5 text-green-500"}):(0,i.jsx)(h.A,{className:"ml-2 h-5 w-5 text-red-500"})]})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("div",{className:"text-3xl font-bold",children:(0,i.jsx)("span",{className:a?"text-green-600":"text-red-600",children:(0,p.vv)(n)})}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-gray-500",children:"Assets"}),(0,i.jsx)("div",{className:"font-semibold text-green-600",children:(0,p.vv)(e)})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-gray-500",children:"Receivables"}),(0,i.jsx)("div",{className:"font-semibold text-blue-600",children:(0,p.vv)(r)})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-gray-500",children:"Liabilities"}),(0,i.jsx)("div",{className:"font-semibold text-red-600",children:(0,p.vv)(t)})]})]}),(0,i.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Net Worth = Assets + Receivables - Liabilities"})]})})]})}var g=r(85778),v=r(41312),m=r(43649);function b({totalAssets:e,totalLiabilities:t,totalReceivables:r,overdueItems:n}){let a=[{title:"Total Assets",value:(0,p.vv)(e),icon:s.A,color:"text-green-600",bgColor:"bg-green-50"},{title:"Total Liabilities",value:(0,p.vv)(t),icon:g.A,color:"text-red-600",bgColor:"bg-red-50"},{title:"Total Receivables",value:(0,p.vv)(r),icon:v.A,color:"text-blue-600",bgColor:"bg-blue-50"},{title:"Overdue Items",value:`${n.liabilities+n.receivables}`,icon:m.A,color:n.liabilities+n.receivables>0?"text-red-600":"text-gray-600",bgColor:n.liabilities+n.receivables>0?"bg-red-50":"bg-gray-50"}];return(0,i.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:a.map((e,t)=>(0,i.jsx)(d.Card,{children:(0,i.jsx)(d.CardContent,{className:"p-4 lg:p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:(0,i.jsx)(e.icon,{className:`h-5 w-5 lg:h-6 lg:w-6 ${e.color}`})}),(0,i.jsxs)("div",{className:"ml-3 lg:ml-4 min-w-0 flex-1",children:[(0,i.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-600 truncate",children:e.title}),(0,i.jsx)("p",{className:`text-lg lg:text-2xl font-bold ${e.color} truncate`,children:e.value})]})]})})},t))})}var x=r(49384),w=r(67766),O=r.n(w),j=r(5664),P=r.n(j),S=e=>0===e?0:e>0?1:-1,A=e=>"number"==typeof e&&e!=+e,_=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,M=e=>("number"==typeof e||e instanceof Number)&&!A(e),T=e=>M(e)||"string"==typeof e,E=0,C=e=>{var t=++E;return"".concat(e||"").concat(t)},k=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!M(e)&&"string"!=typeof e)return n;if(_(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return A(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},N=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},D=(e,t)=>M(e)&&M(t)?r=>e+r*(t-e):()=>t,I=e=>null==e,L=e=>I(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),R=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function U(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var z=(0,a.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:o="100%",minWidth:l=0,minHeight:u,maxHeight:c,children:s,debounce:f=0,id:d,className:h,onResize:p,style:y={}}=e,g=(0,a.useRef)(null),v=(0,a.useRef)();v.current=p,(0,a.useImperativeHandle)(t,()=>g.current);var[m,b]=(0,a.useState)({containerWidth:n.width,containerHeight:n.height}),w=(0,a.useCallback)((e,t)=>{b(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,a.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;w(r,n),null==(t=v.current)||t.call(v,r,n)};f>0&&(e=O()(e,f,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=g.current.getBoundingClientRect();return w(r,n),t.observe(g.current),()=>{t.disconnect()}},[w,f]);var j=(0,a.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=m;if(e<0||t<0)return null;R(_(i)||_(o),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,o),R(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=_(i)?e:i,f=_(o)?t:o;return r&&r>0&&(n?f=n/r:f&&(n=f*r),c&&f>c&&(f=c)),R(n>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,f,i,o,l,u,r),a.Children.map(s,e=>(0,a.cloneElement)(e,{width:n,height:f,style:U({width:n,height:f},e.props.style)}))},[r,s,o,c,u,l,m,i]);return a.createElement("div",{id:d?"".concat(d):void 0,className:(0,x.$)("recharts-responsive-container",h),style:U(U({},y),{},{width:i,height:o,minWidth:l,minHeight:u,maxHeight:c}),ref:g},a.createElement("div",{style:{width:0,height:0,overflow:"visible"}},j))});function F(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var K="function"==typeof Symbol&&Symbol.observable||"@@observable",B=()=>Math.random().toString(36).substring(7).split("").join("."),q={INIT:`@@redux/INIT${B()}`,REPLACE:`@@redux/REPLACE${B()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${B()}`};function W(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function H(e){let t,r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{Object.keys(n).forEach(e=>{let t=n[e];if(void 0===t(void 0,{type:q.INIT}))throw Error(F(12));if(void 0===t(void 0,{type:q.PROBE_UNKNOWN_ACTION()}))throw Error(F(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],u=n[l],c=e[l],s=u(c,r);if(void 0===s)throw r&&r.type,Error(F(14));o[l]=s,a=a||s!==c}return(a=a||i.length!==Object.keys(e).length)?o:e}}function Y(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function V(e){return W(e)&&"type"in e&&"string"==typeof e.type}function X(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var G=X(),Z=Symbol.for("immer-nothing"),J=Symbol.for("immer-draftable"),Q=Symbol.for("immer-state");function ee(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var et=Object.getPrototypeOf;function er(e){return!!e&&!!e[Q]}function en(e){return!!e&&(ea(e)||Array.isArray(e)||!!e[J]||!!e.constructor?.[J]||es(e)||ef(e))}var ei=Object.prototype.constructor.toString();function ea(e){if(!e||"object"!=typeof e)return!1;let t=et(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===ei}function eo(e,t){0===el(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function el(e){let t=e[Q];return t?t.type_:Array.isArray(e)?1:es(e)?2:3*!!ef(e)}function eu(e,t){return 2===el(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ec(e,t,r){let n=el(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function es(e){return e instanceof Map}function ef(e){return e instanceof Set}function ed(e){return e.copy_||e.base_}function eh(e,t){if(es(e))return new Map(e);if(ef(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=ea(e);if(!0!==t&&("class_only"!==t||r)){let t=et(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[Q];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(et(e),t)}}function ep(e,t=!1){return eg(e)||er(e)||!en(e)||(el(e)>1&&(e.set=e.add=e.clear=e.delete=ey),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>ep(t,!0))),e}function ey(){ee(2)}function eg(e){return Object.isFrozen(e)}var ev={};function em(e){let t=ev[e];return t||ee(0,e),t}function eb(e,t){t&&(em("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function ex(e){ew(e),e.drafts_.forEach(ej),e.drafts_=null}function ew(e){e===lF&&(lF=e.parent_)}function eO(e){return lF={drafts_:[],parent_:lF,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ej(e){let t=e[Q];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function eP(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[Q].modified_&&(ex(t),ee(4)),en(e)&&(e=eS(t,e),t.parent_||e_(t,e)),t.patches_&&em("Patches").generateReplacementPatches_(r[Q].base_,e,t.patches_,t.inversePatches_)):e=eS(t,r,[]),ex(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Z?e:void 0}function eS(e,t,r){if(eg(t))return t;let n=t[Q];if(!n)return eo(t,(i,a)=>eA(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return e_(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),eo(i,(i,o)=>eA(e,n,t,i,o,r,a)),e_(e,t,!1),r&&e.patches_&&em("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function eA(e,t,r,n,i,a,o){if(er(i)){let o=eS(e,i,a&&t&&3!==t.type_&&!eu(t.assigned_,n)?a.concat(n):void 0);if(ec(r,n,o),!er(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(en(i)&&!eg(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;eS(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&e_(e,i)}}function e_(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&ep(t,r)}var eM={get(e,t){if(t===Q)return e;let r=ed(e);if(!eu(r,t)){var n=e,i=r,a=t;let o=eC(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let o=r[t];return e.finalized_||!en(o)?o:o===eE(e.base_,t)?(eN(e),e.copy_[t]=eD(o,e)):o},has:(e,t)=>t in ed(e),ownKeys:e=>Reflect.ownKeys(ed(e)),set(e,t,r){let n=eC(ed(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=eE(ed(e),t),i=n?.[Q];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||eu(e.base_,t)))return!0;eN(e),ek(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==eE(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,eN(e),ek(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=ed(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){ee(11)},getPrototypeOf:e=>et(e.base_),setPrototypeOf(){ee(12)}},eT={};function eE(e,t){let r=e[Q];return(r?ed(r):e)[t]}function eC(e,t){if(!(t in e))return;let r=et(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=et(r)}}function ek(e){!e.modified_&&(e.modified_=!0,e.parent_&&ek(e.parent_))}function eN(e){e.copy_||(e.copy_=eh(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function eD(e,t){let r=es(e)?em("MapSet").proxyMap_(e,t):ef(e)?em("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),n={type_:+!!r,scope_:t?t.scope_:lF,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=n,a=eM;r&&(i=[n],a=eT);let{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:lF).drafts_.push(r),r}function eI(e){return er(e)||ee(10,e),function e(t){let r;if(!en(t)||eg(t))return t;let n=t[Q];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=eh(t,n.scope_.immer_.useStrictShallowCopy_)}else r=eh(t,!0);return eo(r,(t,n)=>{ec(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}eo(eM,(e,t)=>{eT[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),eT.deleteProperty=function(e,t){return eT.set.call(this,e,t,void 0)},eT.set=function(e,t,r){return eM.set.call(this,e[0],t,r,e[0])};var eL=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&ee(6),void 0!==r&&"function"!=typeof r&&ee(7),en(e)){let i=eO(this),a=eD(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?ex(i):ew(i)}return eb(i,r),eP(n,i)}if(e&&"object"==typeof e)ee(1,e);else{if(void 0===(n=t(e))&&(n=e),n===Z&&(n=void 0),this.autoFreeze_&&ep(n,!0),r){let t=[],i=[];em("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){en(e)||ee(8),er(e)&&(e=eI(e));let t=eO(this),r=eD(e,void 0);return r[Q].isManual_=!0,ew(t),r}finishDraft(e,t){let r=e&&e[Q];r&&r.isManual_||ee(9);let{scope_:n}=r;return eb(n,t),eP(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=em("Patches").applyPatches_;return er(e)?n(e,t):this.produce(e,e=>n(e,t))}},eR=eL.produce;eL.produceWithPatches.bind(eL),eL.setAutoFreeze.bind(eL),eL.setUseStrictShallowCopy.bind(eL),eL.applyPatches.bind(eL),eL.createDraft.bind(eL),eL.finishDraft.bind(eL);var e$="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?Y:Y.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var eU=e=>e&&"function"==typeof e.match;function ez(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(tF(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>V(t)&&t.type===e,r}function eF(e){return["type","payload","error","meta"].indexOf(e)>-1}var eK=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function eB(e){return en(e)?eR(e,()=>{}):e}function eq(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var eW=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{},a=new eK;return t&&("boolean"==typeof t?a.push(G):a.push(X(t.extraArgument))),a},eH=e=>t=>{setTimeout(t,e)},eY=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:eH(10):"callback"===e.type?e.queueNotification:eH(e.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,u(c)),n.dispatch(e)}finally{i=!0}}})},eV=e=>function(t){let{autoBatch:r=!0}=t??{},n=new eK(e);return r&&n.push(eY("object"==typeof r?r:void 0)),n};function eX(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(tF(28));if(n in r)throw Error(tF(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var eG=(e,t)=>eU(e)?e.match(t):e(t);function eZ(...e){return t=>e.some(e=>eG(e,t))}var eJ=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},eQ=["name","message","stack","code"],e0=class{constructor(e,t){this.payload=e,this.meta=t}_type},e1=class{constructor(e,t){this.payload=e,this.meta=t}_type},e2=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of eQ)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},e5="External signal was aborted";function e3(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var e6=Symbol.for("rtk-slice-createasyncthunk"),e4=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(e4||{}),e8=function({creators:e}={}){let t=e?.asyncThunk?.[e6];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(tF(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},u={},c={},s=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(tF(12));if(r in u)throw Error(tF(13));return u[r]=t,f},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(c[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?eX(e.extraReducers):[e.extraReducers],i={...t,...u};return function(e,t){let r,[n,i,a]=eX(t);if("function"==typeof e)r=()=>eB(e());else{let t=eB(e);r=()=>t}function o(e=r(),t){let l=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[a]),l.reduce((e,r)=>{if(r)if(er(e)){let n=r(e,t);return void 0===n?e:n}else{if(en(e))return eR(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of s)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(tF(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:u,settled:c,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||e7,pending:l||e7,rejected:u||e7,settled:c||e7})}(o,i,f,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(tF(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?ez(e,o):ez(e))}(o,i,f)});let h=e=>e,p=new Map,y=new WeakMap;function g(e,t){return r||(r=d()),r(e,t)}function v(){return r||(r=d()),r.getInitialState()}function m(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=eq(y,n,v)),i}function i(t=h){let n=eq(p,r,()=>new WeakMap);return eq(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>eq(y,t,v),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:g,actions:c,caseReducers:l,getInitialState:v,...m(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:g},r),{...b,...m(n,!0)}}};return b}}();function e7(){}function e9(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(eF)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function te(e,t){return t(e)}function tt(e){return Array.isArray(e)||(e=Object.values(e)),e}var tr="listener",tn="completed",ti="cancelled",ta=`task-${ti}`,to=`task-${tn}`,tl=`${tr}-${ti}`,tu=`${tr}-${tn}`,tc=class{constructor(e){this.code=e,this.message=`task ${ti} (reason: ${e})`}name="TaskAbortError";message},ts=(e,t)=>{if("function"!=typeof e)throw TypeError(tF(32))},tf=()=>{},td=(e,t=tf)=>(e.catch(t),e),th=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),tp=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},ty=e=>{if(e.aborted){let{reason:t}=e;throw new tc(t)}};function tg(e,t){let r=tf;return new Promise((n,i)=>{let a=()=>i(new tc(e.reason));if(e.aborted)return void a();r=th(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=tf})}var tv=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof tc?"cancelled":"rejected",error:e}}finally{t?.()}},tm=e=>t=>td(tg(e,t).then(t=>(ty(e),t))),tb=e=>{let t=tm(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:tx}=Object,tw={},tO="listenerMiddleware",tj=(e,t)=>{let r=t=>th(e,()=>tp(t,e.reason));return(n,i)=>{ts(n,"taskExecutor");let a=new AbortController;r(a);let o=tv(async()=>{ty(e),ty(a.signal);let t=await n({pause:tm(a.signal),delay:tb(a.signal),signal:a.signal});return ty(a.signal),t},()=>tp(a,to));return i?.autoJoin&&t.push(o.catch(tf)),{result:tm(e)(o),cancel(){tp(a,ta)}}}},tP=(e,t)=>{let r=async(r,n)=>{ty(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await tg(t,Promise.race(a));return ty(t),e}finally{i()}};return(e,t)=>td(r(e,t))},tS=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=ez(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tF(21));return ts(a,"options.listener"),{predicate:i,type:t,effect:a}},tA=tx(e=>{let{type:t,predicate:r,effect:n}=tS(e);return{id:eJ(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tF(22))}}},{withTypes:()=>tA}),t_=(e,t)=>{let{type:r,effect:n,predicate:i}=tS(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},tM=e=>{e.pending.forEach(e=>{tp(e,tl)})},tT=e=>()=>{e.forEach(tM),e.clear()},tE=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},tC=tx(ez(`${tO}/add`),{withTypes:()=>tC}),tk=ez(`${tO}/removeAll`),tN=tx(ez(`${tO}/remove`),{withTypes:()=>tN}),tD=(...e)=>{console.error(`${tO}/error`,...e)},tI=(e={})=>{let t=new Map,{extra:r,onError:n=tD}=e;ts(n,"onError");let i=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&tM(e)}),a=e=>i(t_(t,e)??tA(e));tx(a,{withTypes:()=>a});let o=e=>{let r=t_(t,e);return r&&(r.unsubscribe(),e.cancelActive&&tM(r)),!!r};tx(o,{withTypes:()=>o});let l=async(e,i,o,l)=>{let u=new AbortController,c=tP(a,u.signal),s=[];try{e.pending.add(u),await Promise.resolve(e.effect(i,tx({},o,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:tb(u.signal),pause:tm(u.signal),extra:r,signal:u.signal,fork:tj(u.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(tp(e,tl),r.delete(e))})},cancel:()=>{tp(u,tl),e.pending.delete(u)},throwIfCancelled:()=>{ty(u.signal)}})))}catch(e){e instanceof tc||tE(n,e,{raisedBy:"effect"})}finally{await Promise.all(s),tp(u,tu),e.pending.delete(u)}},u=tT(t);return{middleware:e=>r=>i=>{let c;if(!V(i))return r(i);if(tC.match(i))return a(i.payload);if(tk.match(i))return void u();if(tN.match(i))return o(i.payload);let s=e.getState(),f=()=>{if(s===tw)throw Error(tF(23));return s};try{if(c=r(i),t.size>0){let r=e.getState();for(let a of Array.from(t.values())){let t=!1;try{t=a.predicate(i,r,s)}catch(e){t=!1,tE(n,e,{raisedBy:"predicate"})}t&&l(a,i,e,f)}}}finally{s=tw}return c},startListening:a,stopListening:o,clearListeners:u}},tL=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,tR=Symbol.for("rtk-state-proxy-original"),t$=e=>!!e&&!!e[tR],tU=new WeakMap,tz={};function tF(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}function tK(e,t){if(t){var r=Number.parseInt(t,10);if(!A(r))return null==e?void 0:e[r]}}var tB=e8({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),tq=tB.reducer,{createEventEmitter:tW}=tB.actions;r(6895);var tH=Symbol.for("react.forward_ref"),tY=Symbol.for("react.memo");function tV(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var tX={notify(){},get:()=>[]},tG="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,tZ="undefined"!=typeof navigator&&"ReactNative"===navigator.product,tJ=tG||tZ?a.useLayoutEffect:a.useEffect;function tQ(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var t0={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},t1={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},t2={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},t5={[tH]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[tY]:t2};function t3(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case tH:case null:case tY:case null:return e;default:return t}}case null:return t}}}(e)===tY?t2:t5[e.$$typeof]||t0}var t6=Object.defineProperty,t4=Object.getOwnPropertyNames,t8=Object.getOwnPropertySymbols,t7=Object.getOwnPropertyDescriptor,t9=Object.getPrototypeOf,re=Object.prototype,rt=Symbol.for("react-redux-context"),rr="undefined"!=typeof globalThis?globalThis:{},rn=function(){if(!a.createContext)return{};let e=rr[rt]??=new Map,t=e.get(a.createContext);return t||(t=a.createContext(null),e.set(a.createContext,t)),t}(),ri=function(e){let{children:t,context:r,serverState:n,store:i}=e,o=a.useMemo(()=>{let e=function(e,t){let r,n=tX,i=0,a=!1;function o(){c.onStateChange&&c.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function u(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=tX)}let c={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>n};return c}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),l=a.useMemo(()=>i.getState(),[i]);return tJ(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,l]),a.createElement((r||rn).Provider,{value:o},t)},ra={active:!1,index:null,dataKey:void 0,coordinate:void 0},ro=e8({name:"tooltip",initialState:{itemInteraction:{click:ra,hover:ra},axisInteraction:{click:ra,hover:ra},keyboardInteraction:ra,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=eI(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:rl,removeTooltipEntrySettings:ru,setTooltipSettingsState:rc,setActiveMouseOverItemIndex:rs,mouseLeaveItem:rf,mouseLeaveChart:rd,setActiveClickItemIndex:rh,setMouseOverAxisIndex:rp,setMouseClickAxisIndex:ry,setSyncInteraction:rg,setKeyboardInteraction:rv}=ro.actions,rm=ro.reducer,rb=e8({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:rx,setDataStartEndIndexes:rw,setComputedData:rO}=rb.actions,rj=rb.reducer,rP=e8({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:rS,setLayout:rA,setChartSize:r_,setScale:rM}=rP.actions,rT=rP.reducer,rE=e=>Array.isArray(e)?e:[e],rC=0,rk=null,rN=class{revision=rC;_value;_lastValue;_isEqual=rD;constructor(e,t=rD){this._value=this._lastValue=e,this._isEqual=t}get value(){return rk?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++rC)}};function rD(e,t){return e===t}function rI(e){return e instanceof rN||console.warn("Not a valid cell! ",e),e.value}var rL=(e,t)=>!1;function rR(){return function(e,t=rD){return new rN(null,t)}(0,rL)}var r$=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=rR()),rI(t)};Symbol();var rU=0,rz=Object.getPrototypeOf({}),rF=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,rK);tag=rR();tags={};children={};collectionTag=null;id=rU++},rK={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in rz)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new rB(e):new rF(e)}(n)),r.tag&&rI(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=rR()).value=n),rI(r),n}})(),ownKeys:e=>(r$(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},rB=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],rq);tag=rR();tags={};children={};collectionTag=null;id=rU++},rq={get:([e],t)=>("length"===t&&r$(e),rK.get(e,t)),ownKeys:([e])=>rK.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>rK.getOwnPropertyDescriptor(e,t),has:([e],t)=>rK.has(e,t)},rW="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function rH(){return{s:0,v:void 0,o:null,p:null}}function rY(e,t={}){let r,n=rH(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=rH(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=rH(),e.set(t,o)):o=r}}let u=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new rW(t):t}return u.s=1,u.v=t,t}return o.clearCache=()=>{n=rH(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var rV=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:u=[],argsMemoize:c=rY,argsMemoizeOptions:s=[],devModeChecks:f={}}={...r,...a},d=rE(u),h=rE(s),p=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...d);return Object.assign(c(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(p,arguments);return t=y.apply(null,e)},...h),{resultFunc:o,memoizedResultFunc:y,dependencies:p,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:c})};return Object.assign(n,{withTypes:()=>n}),n}(rY),rX=Object.assign((e,t=rV)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>rX}),rG=r(39733),rZ=(0,a.createContext)(null),rJ=e=>e,rQ=()=>{var e=(0,a.useContext)(rZ);return e?e.store.dispatch:rJ},r0=()=>{},r1=()=>r0,r2=(e,t)=>e===t;function r5(e){var t=(0,a.useContext)(rZ);return(0,rG.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:r1,t?t.store.getState:r0,t?t.store.getState:r0,t?e:r0,r2)}var r3=r(10687),r6=r.n(r3),r4=e=>e.legend.settings;function r8(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function r7(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function r9(e){return function(){return e}}function ne(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function nt(e,t){return e[t]}function nr(e){let t=[];return t.key=e,t}function nn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ni(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nn(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nn(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}rV([e=>e.legend.payload,r4],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?r6()(n,r):n}),Array.prototype.slice;var na=Math.PI/180,no=e=>180*e/Math.PI,nl=(e,t,r,n)=>({x:e+Math.cos(-na*n)*r,y:t+Math.sin(-na*n)*r}),nu=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},nc=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},ns=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=nc({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:no(l),angleInRadian:l}},nf=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},nd=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},nh=(e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=ns({x:n,y:i},t),{innerRadius:l,outerRadius:u}=t;if(a<l||a>u||0===a)return null;var{startAngle:c,endAngle:s}=nf(t),f=o;if(c<=s){for(;f>s;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=s}else{for(;f>c;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=c}return r?ni(ni({},t),{},{radius:a,angle:nd(f,t)}):null};function np(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ny(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?np(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):np(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ng(e,t,r){return I(e)||I(t)?r:T(t)?P()(e,t,r):"function"==typeof t?t(e):r}var nv=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,d=void 0;if(S(s-c)!==S(f-s)){var h=[];if(S(f-s)===S(i[1]-i[0])){d=f;var p=s+i[1]-i[0];h[0]=Math.min(p,(p+c)/2),h[1]=Math.max(p,(p+c)/2)}else{d=c;var y=f+i[1]-i[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=h[0]&&e<=h[1]){({index:o}=r[u]);break}}else{var v=Math.min(c,f),m=Math.max(c,f);if(e>(v+s)/2&&e<=(m+s)/2){({index:o}=r[u]);break}}}else if(t){for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}}return o},nm=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&M(e[a]))return ny(ny({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&M(e[o]))return ny(ny({},e),{},{[o]:e[o]+(i||0)})}return e},nb=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,nx=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},nw={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=A(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}r8(e,t)}},none:r8,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}r8(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,u=0,c=0;l<i;++l){for(var s=e[t[l]],f=s[o][1]||0,d=(f-(s[o-1][1]||0))/2,h=0;h<l;++h){var p=e[t[h]];d+=(p[o][1]||0)-(p[o-1][1]||0)}u+=f,c+=d*f}r[o-1][1]+=r[o-1][0]=a,u&&(a-=c/u)}r[o-1][1]+=r[o-1][0]=a,r8(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=A(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},nO=(e,t,r)=>{var n=nw[r];return(function(){var e=r9([]),t=ne,r=r8,n=nt;function i(i){var a,o,l=Array.from(e.apply(this,arguments),nr),u=l.length,c=-1;for(let e of i)for(a=0,++c;a<u;++a)(l[a][c]=[0,+n(e,l[a].key,c,i)]).data=e;for(a=0,o=r7(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:r9(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:r9(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?ne:"function"==typeof e?e:r9(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?r8:e,i):r},i})().keys(t).value((e,t)=>+ng(e,t,0)).order(ne).offset(n)(e)},nj=e=>{var t=e.flat(2).filter(M);return[Math.min(...t),Math.max(...t)]},nP=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],nS=(e,t,r)=>{if(null!=e)return nP(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=nj(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},nA=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,n_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nM=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=r6()(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var u=i[o],c=i[o-1];a=Math.min((u.coordinate||0)-(c.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function nT(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return ny(ny({},t),{},{dataKey:r,payload:n,value:i,name:a})}function nE(e,t){return e?String(e):"string"==typeof t?t:void 0}var nC=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return ny(ny(ny({},n),nl(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return ny(ny(ny({},n),nl(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},nk=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius,nN=e=>e.layout.width,nD=e=>e.layout.height,nI=e=>e.layout.scale,nL=e=>e.layout.margin,nR=rV(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),n$=rV(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),nU="data-recharts-item-index",nz="data-recharts-item-data-key";function nF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nF(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nB=rV([nN,nD,nL,e=>e.brush.height,nR,n$,r4,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var u=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return nK(nK({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),c=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:nK(nK({},e),{},{[r]:P()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),s=nK(nK({},c),u),f=s.bottom;s.bottom+=n;var d=e-(s=nm(s,o,l)).left-s.right,h=t-s.top-s.bottom;return nK(nK({brushBottom:f},s),{},{width:Math.max(d,0),height:Math.max(h,0)})}),nq=rV(nB,e=>({x:e.left,y:e.top,width:e.width,height:e.height}));rV(nN,nD,(e,t)=>({x:0,y:0,width:e,height:t}));var nW=(0,a.createContext)(null),nH=()=>null!=(0,a.useContext)(nW),nY=e=>e.brush,nV=rV([nY,nB,nL],(e,t,r)=>({height:e.height,x:M(e.x)?e.x:t.left,y:M(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:M(e.width)?e.width:t.width})),nX=()=>{var e,t=nH(),r=r5(nq),n=r5(nV),i=null==(e=r5(nY))?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},nG={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},nZ=()=>{var e;return null!=(e=r5(nB))?e:nG},nJ=()=>r5(nN),nQ=()=>r5(nD),n0=e=>e.layout.layoutType,n1=()=>r5(n0),n2=r(30921),n5=r.n(n2);function n3(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function n6(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class n4 extends Map{constructor(e,t=n7){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(n8(this,e))}has(e){return super.has(n8(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function n8({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function n7(e){return null!==e&&"object"==typeof e?e.valueOf():e}let n9=Symbol("implicit");function ie(){var e=new n4,t=[],r=[],n=n9;function i(i){let a=e.get(i);if(void 0===a){if(n!==n9)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new n4,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return ie(t,r).unknown(n)},n3.apply(i,arguments),i}function it(){var e,t,r=ie().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),d+=(h-d-e*(r-u))*s,t=e*(1-u),l&&(d=Math.round(d),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return it(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},n3.apply(f(),arguments)}function ir(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(it.apply(null,arguments).paddingInner(1))}let ii=Math.sqrt(50),ia=Math.sqrt(10),io=Math.sqrt(2);function il(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=ii?10:u>=ia?5:u>=io?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?il(e,t,2*r):[n,i,a]}function iu(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?il(t,e,r):il(e,t,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)u[e]=-((a-e)/o);else for(let e=0;e<l;++e)u[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)u[e]=-((i+e)/o);else for(let e=0;e<l;++e)u[e]=(i+e)*o;return u}function ic(e,t,r){return il(e*=1,t*=1,r*=1)[2]}function is(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?ic(t,e,r):ic(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function id(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ih(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ip(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=id,r=(t,r)=>id(e(t),r),n=(t,r)=>e(t)-r):(t=e===id||e===ih?e:iy,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function iy(){return 0}function ig(e){return null===e?NaN:+e}let iv=ip(id),im=iv.right;function ib(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function ix(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function iw(){}iv.left,ip(ig).center;var iO="\\s*([+-]?\\d+)\\s*",ij="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iP="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iS=/^#([0-9a-f]{3,8})$/,iA=RegExp(`^rgb\\(${iO},${iO},${iO}\\)$`),i_=RegExp(`^rgb\\(${iP},${iP},${iP}\\)$`),iM=RegExp(`^rgba\\(${iO},${iO},${iO},${ij}\\)$`),iT=RegExp(`^rgba\\(${iP},${iP},${iP},${ij}\\)$`),iE=RegExp(`^hsl\\(${ij},${iP},${iP}\\)$`),iC=RegExp(`^hsla\\(${ij},${iP},${iP},${ij}\\)$`),ik={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iN(){return this.rgb().formatHex()}function iD(){return this.rgb().formatRgb()}function iI(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=iS.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?iL(t):3===r?new iU(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iR(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iR(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=iA.exec(e))?new iU(t[1],t[2],t[3],1):(t=i_.exec(e))?new iU(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=iM.exec(e))?iR(t[1],t[2],t[3],t[4]):(t=iT.exec(e))?iR(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=iE.exec(e))?iW(t[1],t[2]/100,t[3]/100,1):(t=iC.exec(e))?iW(t[1],t[2]/100,t[3]/100,t[4]):ik.hasOwnProperty(e)?iL(ik[e]):"transparent"===e?new iU(NaN,NaN,NaN,0):null}function iL(e){return new iU(e>>16&255,e>>8&255,255&e,1)}function iR(e,t,r,n){return n<=0&&(e=t=r=NaN),new iU(e,t,r,n)}function i$(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof iw||(i=iI(i)),i)?new iU((i=i.rgb()).r,i.g,i.b,i.opacity):new iU:new iU(e,t,r,null==n?1:n)}function iU(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function iz(){return`#${iq(this.r)}${iq(this.g)}${iq(this.b)}`}function iF(){let e=iK(this.opacity);return`${1===e?"rgb(":"rgba("}${iB(this.r)}, ${iB(this.g)}, ${iB(this.b)}${1===e?")":`, ${e})`}`}function iK(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function iB(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function iq(e){return((e=iB(e))<16?"0":"")+e.toString(16)}function iW(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new iY(e,t,r,n)}function iH(e){if(e instanceof iY)return new iY(e.h,e.s,e.l,e.opacity);if(e instanceof iw||(e=iI(e)),!e)return new iY;if(e instanceof iY)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,u=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=u<.5?a+i:2-a-i,o*=60):l=u>0&&u<1?0:o,new iY(o,l,u,e.opacity)}function iY(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function iV(e){return(e=(e||0)%360)<0?e+360:e}function iX(e){return Math.max(0,Math.min(1,e||0))}function iG(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function iZ(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}ib(iw,iI,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iN,formatHex:iN,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iH(this).formatHsl()},formatRgb:iD,toString:iD}),ib(iU,i$,ix(iw,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iU(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iU(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new iU(iB(this.r),iB(this.g),iB(this.b),iK(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iz,formatHex:iz,formatHex8:function(){return`#${iq(this.r)}${iq(this.g)}${iq(this.b)}${iq((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iF,toString:iF})),ib(iY,function(e,t,r,n){return 1==arguments.length?iH(e):new iY(e,t,r,null==n?1:n)},ix(iw,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iY(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iY(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new iU(iG(e>=240?e-240:e+120,i,n),iG(e,i,n),iG(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new iY(iV(this.h),iX(this.s),iX(this.l),iK(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=iK(this.opacity);return`${1===e?"hsl(":"hsla("}${iV(this.h)}, ${100*iX(this.s)}%, ${100*iX(this.l)}%${1===e?")":`, ${e})`}`}}));let iJ=e=>()=>e;function iQ(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):iJ(isNaN(e)?t:e)}let i0=function e(t){var r,n=1==(r=+t)?iQ:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):iJ(isNaN(e)?t:e)};function i(e,t){var r=n((e=i$(e)).r,(t=i$(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=iQ(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function i1(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=i$(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}i1(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return iZ((r-n/t)*t,o,i,a,l)}}),i1(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return iZ((r-n/t)*t,i,a,o,l)}});function i2(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var i5=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,i3=RegExp(i5.source,"g");function i6(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?iJ(t):("number"===i?i2:"string"===i?(n=iI(t))?(t=n,i0):function(e,t){var r,n,i,a,o,l=i5.lastIndex=i3.lastIndex=0,u=-1,c=[],s=[];for(e+="",t+="";(i=i5.exec(e))&&(a=i3.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),c[u]?c[u]+=o:c[++u]=o),(i=i[0])===(a=a[0])?c[u]?c[u]+=a:c[++u]=a:(c[++u]=null,s.push({i:u,x:i2(i,a)})),l=i3.lastIndex;return l<t.length&&(o=t.slice(l),c[u]?c[u]+=o:c[++u]=o),c.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)c[(r=s[n]).i]=r.x(e);return c.join("")})}:t instanceof iI?i0:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=i6(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=i6(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:i2:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function i4(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function i8(e){return+e}var i7=[0,1];function i9(e){return e}function ae(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function at(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=ae(i,n),a=r(o,a)):(n=ae(n,i),a=r(a,o)),function(e){return a(n(e))}}function ar(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=ae(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=im(e,t,1,n)-1;return a[r](i[r](t))}}function an(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ai(){var e,t,r,n,i,a,o=i7,l=i7,u=i6,c=i9;function s(){var e,t,r,u=Math.min(o.length,l.length);return c!==i9&&(e=o[0],t=o[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?ar:at,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,u)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),i2)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,i8),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=i4,s()},f.clamp=function(e){return arguments.length?(c=!!e||i9,s()):c!==i9},f.interpolate=function(e){return arguments.length?(u=e,s()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function aa(){return ai()(i9,i9)}var ao=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function al(e){var t;if(!(t=ao.exec(e)))throw Error("invalid format: "+e);return new au({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function au(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function ac(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function as(e){return(e=ac(Math.abs(e)))?e[1]:NaN}function af(e,t){var r=ac(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}al.prototype=au.prototype,au.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ad={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>af(100*e,t),r:af,s:function(e,t){var r=ac(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(lK=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+ac(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function ah(e){return e}var ap=Array.prototype.map,ay=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ag(e,t,r,n){var i,a,o,l=is(e,t,r);switch((n=al(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(o=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(as(u)/3)))-as(Math.abs(l))))||(n.precision=o),lW(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=Math.max(0,as(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=l)))-as(i))+1)||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=Math.max(0,-as(Math.abs(l))))||(n.precision=o-("%"===n.type)*2)}return lq(n)}function av(e){var t=e.domain;return e.ticks=function(e){var r=t();return iu(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return ag(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=ic(u,c,r))===n)return a[o]=u,a[l]=c,t(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function am(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function ab(e){return Math.log(e)}function ax(e){return Math.exp(e)}function aw(e){return-Math.log(-e)}function aO(e){return-Math.exp(-e)}function aj(e){return isFinite(e)?+("1e"+e):e<0?0:e}function aP(e){return(t,r)=>-e(-t,r)}function aS(e){let t,r,n=e(ab,ax),i=n.domain,a=10;function o(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?aj:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=aP(t),r=aP(r),e(aw,aO)):e(ab,ax),n}return n.base=function(e){return arguments.length?(a=+e,o()):a},n.domain=function(e){return arguments.length?(i(e),o()):i()},n.ticks=e=>{let n,o,l=i(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=t(u),d=t(c),h=null==e?10:+e,p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),u>0){for(;f<=d;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<u)){if(o>c)break;p.push(o)}}else for(;f<=d;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<u)){if(o>c)break;p.push(o)}2*p.length<h&&(p=iu(u,c,h))}else p=iu(f,d,Math.min(d-f,h)).map(r);return s?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=al(i)).precision||(i.trim=!0),i=lq(i)),e===1/0)return i;let o=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=o?i(e):""}},n.nice=()=>i(am(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function aA(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function a_(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function aM(e){var t=1,r=e(aA(1),a_(t));return r.constant=function(r){return arguments.length?e(aA(t=+r),a_(t)):t},av(r)}function aT(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function aE(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function aC(e){return e<0?-e*e:e*e}function ak(e){var t=e(i9,i9),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(i9,i9):.5===r?e(aE,aC):e(aT(r),aT(1/r)):r},av(t)}function aN(){var e=ak(ai());return e.copy=function(){return an(e,aN()).exponent(e.exponent())},n3.apply(e,arguments),e}function aD(){return aN.apply(null,arguments).exponent(.5)}function aI(e){return Math.sign(e)*e*e}function aL(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function aR(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}lq=(lB=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?ah:(t=ap.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),a.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?ah:(n=ap.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),c=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=al(e)).fill,r=e.align,n=e.sign,d=e.symbol,h=e.zero,p=e.width,y=e.comma,g=e.precision,v=e.trim,m=e.type;"n"===m?(y=!0,m="g"):ad[m]||(void 0===g&&(g=12),v=!0,m="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var b="$"===d?a:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===d?o:/[%p]/.test(m)?c:"",w=ad[m],O=/[defgprs%]/.test(m);function j(e){var a,o,c,d=b,j=x;if("c"===m)j=w(e)+j,e="";else{var P=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),g),v&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),P&&0==+e&&"+"!==n&&(P=!1),d=(P?"("===n?n:s:"-"===n||"("===n?"":n)+d,j=("s"===m?ay[8+lK/3]:"")+j+(P&&"("===n?")":""),O){for(a=-1,o=e.length;++a<o;)if(48>(c=e.charCodeAt(a))||c>57){j=(46===c?l+e.slice(a+1):e.slice(a))+j,e=e.slice(0,a);break}}}y&&!h&&(e=i(e,1/0));var S=d.length+e.length+j.length,A=S<p?Array(p-S+1).join(t):"";switch(y&&h&&(e=i(A+e,A.length?p-j.length:1/0),A=""),r){case"<":e=d+e+j+A;break;case"=":e=d+A+e+j;break;case"^":e=A.slice(0,S=A.length>>1)+d+e+j+A.slice(S);break;default:e=A+d+e+j}return u(e)}return g=void 0===g?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),j.toString=function(){return e+""},j}return{format:d,formatPrefix:function(e,t){var r=d(((e=al(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(as(t)/3))),i=Math.pow(10,-n),a=ay[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,lW=lB.formatPrefix;function a$(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function aU(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let az=new Date,aF=new Date;function aK(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>aK(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(az.setTime(+t),aF.setTime(+n),e(az),e(aF),Math.floor(r(az,aF))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let aB=aK(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);aB.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?aK(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):aB:null,aB.range;let aq=aK(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());aq.range;let aW=aK(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());aW.range;let aH=aK(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());aH.range;let aY=aK(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());aY.range;let aV=aK(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());aV.range;let aX=aK(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);aX.range;let aG=aK(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);aG.range;let aZ=aK(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function aJ(e){return aK(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}aZ.range;let aQ=aJ(0),a0=aJ(1),a1=aJ(2),a2=aJ(3),a5=aJ(4),a3=aJ(5),a6=aJ(6);function a4(e){return aK(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}aQ.range,a0.range,a1.range,a2.range,a5.range,a3.range,a6.range;let a8=a4(0),a7=a4(1),a9=a4(2),oe=a4(3),ot=a4(4),or=a4(5),on=a4(6);a8.range,a7.range,a9.range,oe.range,ot.range,or.range,on.range;let oi=aK(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());oi.range;let oa=aK(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());oa.range;let oo=aK(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());oo.every=e=>isFinite(e=Math.floor(e))&&e>0?aK(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,oo.range;let ol=aK(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function ou(e,t,r,n,i,a){let o=[[aq,1,1e3],[aq,5,5e3],[aq,15,15e3],[aq,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=ip(([,,e])=>e).right(o,i);if(a===o.length)return e.every(is(t/31536e6,r/31536e6,n));if(0===a)return aB.every(Math.max(is(t,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}ol.every=e=>isFinite(e=Math.floor(e))&&e>0?aK(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,ol.range;let[oc,os]=ou(ol,oa,a8,aZ,aV,aH),[of,od]=ou(oo,oi,aQ,aX,aY,aW);function oh(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function op(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function oy(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var og={"-":"",_:" ",0:"0"},ov=/^\s*\d+/,om=/^%/,ob=/[\\^$*+?|[\]().{}]/g;function ox(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function ow(e){return e.replace(ob,"\\$&")}function oO(e){return RegExp("^(?:"+e.map(ow).join("|")+")","i")}function oj(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function oP(e,t,r){var n=ov.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function oS(e,t,r){var n=ov.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function oA(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function o_(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function oM(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function oT(e,t,r){var n=ov.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function oE(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function oC(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function ok(e,t,r){var n=ov.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function oN(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function oD(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function oI(e,t,r){var n=ov.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function oL(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function oR(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function o$(e,t,r){var n=ov.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function oU(e,t,r){var n=ov.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function oz(e,t,r){var n=ov.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function oF(e,t,r){var n=om.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function oK(e,t,r){var n=ov.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function oB(e,t,r){var n=ov.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function oq(e,t){return ox(e.getDate(),t,2)}function oW(e,t){return ox(e.getHours(),t,2)}function oH(e,t){return ox(e.getHours()%12||12,t,2)}function oY(e,t){return ox(1+aX.count(oo(e),e),t,3)}function oV(e,t){return ox(e.getMilliseconds(),t,3)}function oX(e,t){return oV(e,t)+"000"}function oG(e,t){return ox(e.getMonth()+1,t,2)}function oZ(e,t){return ox(e.getMinutes(),t,2)}function oJ(e,t){return ox(e.getSeconds(),t,2)}function oQ(e){var t=e.getDay();return 0===t?7:t}function o0(e,t){return ox(aQ.count(oo(e)-1,e),t,2)}function o1(e){var t=e.getDay();return t>=4||0===t?a5(e):a5.ceil(e)}function o2(e,t){return e=o1(e),ox(a5.count(oo(e),e)+(4===oo(e).getDay()),t,2)}function o5(e){return e.getDay()}function o3(e,t){return ox(a0.count(oo(e)-1,e),t,2)}function o6(e,t){return ox(e.getFullYear()%100,t,2)}function o4(e,t){return ox((e=o1(e)).getFullYear()%100,t,2)}function o8(e,t){return ox(e.getFullYear()%1e4,t,4)}function o7(e,t){var r=e.getDay();return ox((e=r>=4||0===r?a5(e):a5.ceil(e)).getFullYear()%1e4,t,4)}function o9(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ox(t/60|0,"0",2)+ox(t%60,"0",2)}function le(e,t){return ox(e.getUTCDate(),t,2)}function lt(e,t){return ox(e.getUTCHours(),t,2)}function lr(e,t){return ox(e.getUTCHours()%12||12,t,2)}function ln(e,t){return ox(1+aG.count(ol(e),e),t,3)}function li(e,t){return ox(e.getUTCMilliseconds(),t,3)}function la(e,t){return li(e,t)+"000"}function lo(e,t){return ox(e.getUTCMonth()+1,t,2)}function ll(e,t){return ox(e.getUTCMinutes(),t,2)}function lu(e,t){return ox(e.getUTCSeconds(),t,2)}function lc(e){var t=e.getUTCDay();return 0===t?7:t}function ls(e,t){return ox(a8.count(ol(e)-1,e),t,2)}function lf(e){var t=e.getUTCDay();return t>=4||0===t?ot(e):ot.ceil(e)}function ld(e,t){return e=lf(e),ox(ot.count(ol(e),e)+(4===ol(e).getUTCDay()),t,2)}function lh(e){return e.getUTCDay()}function lp(e,t){return ox(a7.count(ol(e)-1,e),t,2)}function ly(e,t){return ox(e.getUTCFullYear()%100,t,2)}function lg(e,t){return ox((e=lf(e)).getUTCFullYear()%100,t,2)}function lv(e,t){return ox(e.getUTCFullYear()%1e4,t,4)}function lm(e,t){var r=e.getUTCDay();return ox((e=r>=4||0===r?ot(e):ot.ceil(e)).getUTCFullYear()%1e4,t,4)}function lb(){return"+0000"}function lx(){return"%"}function lw(e){return+e}function lO(e){return Math.floor(e/1e3)}function lj(e){return new Date(e)}function lP(e){return e instanceof Date?+e:+new Date(+e)}function lS(e,t,r,n,i,a,o,l,u,c){var s=aa(),f=s.invert,d=s.domain,h=c(".%L"),p=c(":%S"),y=c("%I:%M"),g=c("%I %p"),v=c("%a %d"),m=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(u(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?g:n(e)<e?i(e)<e?v:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,lP)):d().map(lj)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:c(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(am(r,e)):s},s.copy=function(){return an(s,lS(e,t,r,n,i,a,o,l,u,c))},s}function lA(){return n3.apply(lS(of,od,oo,oi,aQ,aX,aY,aW,aq,lY).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function l_(){return n3.apply(lS(oc,os,ol,oa,a8,aG,aV,aH,aq,lV).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function lM(){var e,t,r,n,i,a=0,o=1,l=i9,u=!1;function c(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(i6),c.rangeRound=s(i4),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function lT(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function lE(){var e=ak(lM());return e.copy=function(){return lT(e,lE()).exponent(e.exponent())},n6.apply(e,arguments)}function lC(){return lE.apply(null,arguments).exponent(.5)}function lk(){var e,t,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=i9,d=!1;function h(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=i6);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,u,c]=o,e=a(l*=1),t=a(u*=1),r=a(c*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h):[l,u,c]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(i6),h.rangeRound=p(i4),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(u),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,h}}function lN(){var e=ak(lk());return e.copy=function(){return lT(e,lN()).exponent(e.exponent())},n6.apply(e,arguments)}function lD(){return lN.apply(null,arguments).exponent(.5)}lY=(lH=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,u=e.shortMonths,c=oO(i),s=oj(i),f=oO(a),d=oj(a),h=oO(o),p=oj(o),y=oO(l),g=oj(l),v=oO(u),m=oj(u),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:oq,e:oq,f:oX,g:o4,G:o7,H:oW,I:oH,j:oY,L:oV,m:oG,M:oZ,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:lw,s:lO,S:oJ,u:oQ,U:o0,V:o2,w:o5,W:o3,x:null,X:null,y:o6,Y:o8,Z:o9,"%":lx},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:le,e:le,f:la,g:lg,G:lm,H:lt,I:lr,j:ln,L:li,m:lo,M:ll,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:lw,s:lO,S:lu,u:lc,U:ls,V:ld,w:lh,W:lp,x:null,X:null,y:ly,Y:lv,Z:lb,"%":lx},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:oD,e:oD,f:oz,g:oE,G:oT,H:oL,I:oL,j:oI,L:oU,m:oN,M:oR,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:ok,Q:oK,s:oB,S:o$,u:oS,U:oA,V:o_,w:oP,W:oM,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:oE,Y:oT,Z:oC,"%":oF};function O(e,t){return function(r){var n,i,a,o=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(u,l)),null!=(i=og[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(e.slice(u,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=oy(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=op(oy(a.y,0,1))).getUTCDay())>4||0===i?a7.ceil(n):a7(n),n=aG.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=oh(oy(a.y,0,1))).getDay())>4||0===i?a0.ceil(n):a0(n),n=aX.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?op(oy(a.y,0,1)).getUTCDay():oh(oy(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,op(a)):oh(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in og?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,lH.parse,lV=lH.utcFormat,lH.utcParse;var lI=e=>e.chartData,lL=rV([lI],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}});function lR(e){return Number.isFinite(e)}function l$(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function lU(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(lR(t)&&lR(r))return!0}return!1}function lz(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var lF,lK,lB,lq,lW,lH,lY,lV,lX,lG,lZ=!0,lJ="[DecimalError] ",lQ=lJ+"Invalid argument: ",l0=lJ+"Exponent out of range: ",l1=Math.floor,l2=Math.pow,l5=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,l3=l1(1286742750677284.5),l6={};function l4(e,t){var r,n,i,a,o,l,u,c,s=e.constructor,f=s.precision;if(!e.s||!t.s)return t.s||(t=new s(e)),lZ?uo(t,f):t;if(u=e.d,c=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,l=c.length):(n=c,i=o,l=u.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=u.length)-(a=c.length)<0&&(a=l,n=c,c=u,u=n),r=0;a;)r=(u[--a]=u[a]+c[a]+r)/1e7|0,u[a]%=1e7;for(r&&(u.unshift(r),++i),l=u.length;0==u[--l];)u.pop();return t.d=u,t.e=i,lZ?uo(t,f):t}function l8(e,t,r){if(e!==~~e||e<t||e>r)throw Error(lQ+e)}function l7(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=un(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=un(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}l6.absoluteValue=l6.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},l6.comparedTo=l6.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},l6.decimalPlaces=l6.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},l6.dividedBy=l6.div=function(e){return l9(this,new this.constructor(e))},l6.dividedToIntegerBy=l6.idiv=function(e){var t=this.constructor;return uo(l9(this,new t(e),0,1),t.precision)},l6.equals=l6.eq=function(e){return!this.cmp(e)},l6.exponent=function(){return ut(this)},l6.greaterThan=l6.gt=function(e){return this.cmp(e)>0},l6.greaterThanOrEqualTo=l6.gte=function(e){return this.cmp(e)>=0},l6.isInteger=l6.isint=function(){return this.e>this.d.length-2},l6.isNegative=l6.isneg=function(){return this.s<0},l6.isPositive=l6.ispos=function(){return this.s>0},l6.isZero=function(){return 0===this.s},l6.lessThan=l6.lt=function(e){return 0>this.cmp(e)},l6.lessThanOrEqualTo=l6.lte=function(e){return 1>this.cmp(e)},l6.logarithm=l6.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(lG))throw Error(lJ+"NaN");if(this.s<1)throw Error(lJ+(this.s?"NaN":"-Infinity"));return this.eq(lG)?new r(0):(lZ=!1,t=l9(ui(this,i),ui(e,i),i),lZ=!0,uo(t,n))},l6.minus=l6.sub=function(e){return e=new this.constructor(e),this.s==e.s?ul(this,e):l4(this,(e.s=-e.s,e))},l6.modulo=l6.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(lJ+"NaN");return this.s?(lZ=!1,t=l9(this,e,0,1).times(e),lZ=!0,this.minus(t)):uo(new r(this),n)},l6.naturalExponential=l6.exp=function(){return ue(this)},l6.naturalLogarithm=l6.ln=function(){return ui(this)},l6.negated=l6.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},l6.plus=l6.add=function(e){return e=new this.constructor(e),this.s==e.s?l4(this,e):ul(this,(e.s=-e.s,e))},l6.precision=l6.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(lQ+e);if(t=ut(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},l6.squareRoot=l6.sqrt=function(){var e,t,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(lJ+"NaN")}for(e=ut(this),lZ=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=l7(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=l1((e+1)/2)-(e<0||e%2),n=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(l9(this,a,o+2)).times(.5),l7(a.d).slice(0,o)===(t=l7(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(uo(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return lZ=!0,uo(n,r)},l6.times=l6.mul=function(e){var t,r,n,i,a,o,l,u,c,s=this.constructor,f=this.d,d=(e=new s(e)).d;if(!this.s||!e.s)return new s(0);for(e.s*=this.s,r=this.e+e.e,(u=f.length)<(c=d.length)&&(a=f,f=d,d=a,o=u,u=c,c=o),a=[],n=o=u+c;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=u+n;i>n;)l=a[i]+d[n]*f[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,lZ?uo(e,s.precision):e},l6.toDecimalPlaces=l6.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(l8(e,0,1e9),void 0===t?t=n.rounding:l8(t,0,8),uo(r,e+ut(r)+1,t))},l6.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=uu(n,!0):(l8(e,0,1e9),void 0===t?t=i.rounding:l8(t,0,8),r=uu(n=uo(new i(n),e+1,t),!0,e+1)),r},l6.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?uu(this):(l8(e,0,1e9),void 0===t?t=i.rounding:l8(t,0,8),r=uu((n=uo(new i(this),e+ut(this)+1,t)).abs(),!1,e+ut(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},l6.toInteger=l6.toint=function(){var e=this.constructor;return uo(new e(this),ut(this)+1,e.rounding)},l6.toNumber=function(){return+this},l6.toPower=l6.pow=function(e){var t,r,n,i,a,o,l=this,u=l.constructor,c=+(e=new u(e));if(!e.s)return new u(lG);if(!(l=new u(l)).s){if(e.s<1)throw Error(lJ+"Infinity");return l}if(l.eq(lG))return l;if(n=u.precision,e.eq(lG))return uo(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=c<0?-c:c)<=0x1fffffffffffff){for(i=new u(lG),t=Math.ceil(n/7+4),lZ=!1;r%2&&uc((i=i.times(l)).d,t),0!==(r=l1(r/2));)uc((l=l.times(l)).d,t);return lZ=!0,e.s<0?new u(lG).div(i):uo(i,n)}}else if(a<0)throw Error(lJ+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,lZ=!1,i=e.times(ui(l,n+12)),lZ=!0,(i=ue(i)).s=a,i},l6.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=ut(i),n=uu(i,r<=a.toExpNeg||r>=a.toExpPos)):(l8(e,1,1e9),void 0===t?t=a.rounding:l8(t,0,8),r=ut(i=uo(new a(i),e,t)),n=uu(i,e<=r||r<=a.toExpNeg,e)),n},l6.toSignificantDigits=l6.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(l8(e,1,1e9),void 0===t?t=r.rounding:l8(t,0,8)),uo(new r(this),e,t)},l6.toString=l6.valueOf=l6.val=l6.toJSON=l6[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=ut(this),t=this.constructor;return uu(this,e<=t.toExpNeg||e>=t.toExpPos)};var l9=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,u,c,s,f,d,h,p,y,g,v,m,b,x,w,O,j,P,S=n.constructor,A=n.s==i.s?1:-1,_=n.d,M=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(lJ+"Division by zero");for(c=0,u=n.e-i.e,j=M.length,w=_.length,p=(h=new S(A)).d=[];M[c]==(_[c]||0);)++c;if(M[c]>(_[c]||0)&&--u,(m=null==a?a=S.precision:o?a+(ut(n)-ut(i))+1:a)<0)return new S(0);if(m=m/7+2|0,c=0,1==j)for(s=0,M=M[0],m++;(c<w||s)&&m--;c++)b=1e7*s+(_[c]||0),p[c]=b/M|0,s=b%M|0;else{for((s=1e7/(M[0]+1)|0)>1&&(M=e(M,s),_=e(_,s),j=M.length,w=_.length),x=j,g=(y=_.slice(0,j)).length;g<j;)y[g++]=0;(P=M.slice()).unshift(0),O=M[0],M[1]>=1e7/2&&++O;do s=0,(l=t(M,y,j,g))<0?(v=y[0],j!=g&&(v=1e7*v+(y[1]||0)),(s=v/O|0)>1?(s>=1e7&&(s=1e7-1),d=(f=e(M,s)).length,g=y.length,1==(l=t(f,y,d,g))&&(s--,r(f,j<d?P:M,d))):(0==s&&(l=s=1),f=M.slice()),(d=f.length)<g&&f.unshift(0),r(y,f,g),-1==l&&(g=y.length,(l=t(M,y,j,g))<1&&(s++,r(y,j<g?P:M,g))),g=y.length):0===l&&(s++,y=[0]),p[c++]=s,l&&y[0]?y[g++]=_[x]||0:(y=[_[x]],g=1);while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),h.e=u,uo(h,o?a+ut(h)+1:a)}}();function ue(e,t){var r,n,i,a,o,l=0,u=0,c=e.constructor,s=c.precision;if(ut(e)>16)throw Error(l0+ut(e));if(!e.s)return new c(lG);for(null==t?(lZ=!1,o=s):o=t,a=new c(.03125);e.abs().gte(.1);)e=e.times(a),u+=5;for(o+=Math.log(l2(2,u))/Math.LN10*2+5|0,r=n=i=new c(lG),c.precision=o;;){if(n=uo(n.times(e),o),r=r.times(++l),l7((a=i.plus(l9(n,r,o))).d).slice(0,o)===l7(i.d).slice(0,o)){for(;u--;)i=uo(i.times(i),o);return c.precision=s,null==t?(lZ=!0,uo(i,s)):i}i=a}}function ut(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function ur(e,t,r){if(t>e.LN10.sd())throw lZ=!0,r&&(e.precision=r),Error(lJ+"LN10 precision limit exceeded");return uo(new e(e.LN10),t)}function un(e){for(var t="";e--;)t+="0";return t}function ui(e,t){var r,n,i,a,o,l,u,c,s,f=1,d=e,h=d.d,p=d.constructor,y=p.precision;if(d.s<1)throw Error(lJ+(d.s?"NaN":"-Infinity"));if(d.eq(lG))return new p(0);if(null==t?(lZ=!1,c=y):c=t,d.eq(10))return null==t&&(lZ=!0),ur(p,c);if(p.precision=c+=10,n=(r=l7(h)).charAt(0),!(15e14>Math.abs(a=ut(d))))return u=ur(p,c+2,y).times(a+""),d=ui(new p(n+"."+r.slice(1)),c-10).plus(u),p.precision=y,null==t?(lZ=!0,uo(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=l7((d=d.times(e)).d)).charAt(0),f++;for(a=ut(d),n>1?(d=new p("0."+r),a++):d=new p(n+"."+r.slice(1)),l=o=d=l9(d.minus(lG),d.plus(lG),c),s=uo(d.times(d),c),i=3;;){if(o=uo(o.times(s),c),l7((u=l.plus(l9(o,new p(i),c))).d).slice(0,c)===l7(l.d).slice(0,c))return l=l.times(2),0!==a&&(l=l.plus(ur(p,c+2,y).times(a+""))),l=l9(l,new p(f),c),p.precision=y,null==t?(lZ=!0,uo(l,y)):l;l=u,i+=2}}function ua(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=l1((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),lZ&&(e.e>l3||e.e<-l3))throw Error(l0+r)}else e.s=0,e.e=0,e.d=[0];return e}function uo(e,t,r){var n,i,a,o,l,u,c,s,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,c=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(a=f.length))return e;for(o=1,c=a=f[s];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=c/(a=l2(10,o-i-1))%10|0,u=t<0||void 0!==f[s+1]||c%a,u=r<4?(l||u)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||u||6==r&&(n>0?i>0?c/l2(10,o-i):0:f[s-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return u?(a=ut(e),f.length=1,t=t-a-1,f[0]=l2(10,(7-t%7)%7),e.e=l1(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=s,a=1,s--):(f.length=s+1,a=l2(10,7-n),f[s]=i>0?(c/l2(10,o-i)%l2(10,i)|0)*a:0),u)for(;;)if(0==s){1e7==(f[0]+=a)&&(f[0]=1,++e.e);break}else{if(f[s]+=a,1e7!=f[s])break;f[s--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(lZ&&(e.e>l3||e.e<-l3))throw Error(l0+ut(e));return e}function ul(e,t){var r,n,i,a,o,l,u,c,s,f,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),lZ?uo(t,h):t;if(u=e.d,f=t.d,n=t.e,c=e.e,u=u.slice(),o=c-n){for((s=o<0)?(r=u,o=-o,l=f.length):(r=f,n=c,l=u.length),o>(i=Math.max(Math.ceil(h/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((s=(i=u.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(u[i]!=f[i]){s=u[i]<f[i];break}o=0}for(s&&(r=u,u=f,f=r,t.s=-t.s),l=u.length,i=f.length-l;i>0;--i)u[l++]=0;for(i=f.length;i>o;){if(u[--i]<f[i]){for(a=i;a&&0===u[--a];)u[a]=1e7-1;--u[a],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--l];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,lZ?uo(t,h):t):new d(0)}function uu(e,t,r){var n,i=ut(e),a=l7(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+un(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+un(-i-1)+a,r&&(n=r-o)>0&&(a+=un(n))):i>=o?(a+=un(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+un(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=un(n))),e.s<0?"-"+a:a}function uc(e,t){if(e.length>t)return e.length=t,!0}function us(e){if(!e||"object"!=typeof e)throw Error(lJ+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(l1(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(lQ+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(lQ+r+": "+n);return this}var lX=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(lQ+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return ua(this,e.toString())}if("string"!=typeof e)throw Error(lQ+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,l5.test(e))ua(this,e);else throw Error(lQ+e)}if(a.prototype=l6,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=us,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});lG=new lX(1);let uf=lX;var ud=e=>e,uh={},up=e=>e===uh,uy=e=>function t(){return 0==arguments.length||1==arguments.length&&up(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},ug=(e,t)=>1===e?t:uy(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==uh).length;return a>=e?t(...n):ug(e-a,uy(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>up(e)?r.shift():e),...r)}))}),uv=e=>ug(e.length,e),um=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},ub=uv((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ux=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return ud;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},uw=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),uO=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function uj(e){var t;return 0===e?1:Math.floor(new uf(e).abs().log(10).toNumber())+1}function uP(e,t,r){for(var n=new uf(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}uv((e,t,r)=>{var n=+e;return n+r*(t-n)}),uv((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),uv((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var uS=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},uA=(e,t,r)=>{if(e.lte(0))return new uf(0);var n=uj(e.toNumber()),i=new uf(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new uf(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new uf(t?l.toNumber():Math.ceil(l.toNumber()))},u_=(e,t,r)=>{var n=new uf(1),i=new uf(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new uf(10).pow(uj(e)-1),i=new uf(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new uf(Math.floor(e)))}else 0===e?i=new uf(Math.floor((t-1)/2)):r||(i=new uf(Math.floor(e)));var o=Math.floor((t-1)/2);return ux(ub(e=>i.add(new uf(e-o).mul(n)).toNumber()),um)(0,t)},uM=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new uf(0),tickMin:new uf(0),tickMax:new uf(0)};var o=uA(new uf(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new uf(0):(i=new uf(e).add(t).div(2)).sub(new uf(i).mod(o))).sub(e).div(o).toNumber()),u=Math.ceil(new uf(t).sub(i).div(o).toNumber()),c=l+u+1;return c>r?uM(e,t,r,n,a+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new uf(l).mul(o)),tickMax:i.add(new uf(u).mul(o))})},uT=uO(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=uS([t,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...um(0,n-1).map(()=>1/0)]:[...um(0,n-1).map(()=>-1/0),l];return t>r?uw(u):u}if(o===l)return u_(o,n,i);var{step:c,tickMin:s,tickMax:f}=uM(o,l,a,i,0),d=uP(s,f.add(new uf(.1).mul(c)),c);return t>r?uw(d):d}),uE=uO(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=uS([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),u=uA(new uf(o).sub(a).div(l-1),i,0),c=[...uP(new uf(a),new uf(o),u),o];return!1===i&&(c=c.map(e=>Math.round(e))),r>n?uw(c):c}),uC=e=>e.rootProps.stackOffset,uk=e=>e.options.chartName,uN=e=>e.rootProps.syncId,uD=e=>e.rootProps.syncMethod,uI=e=>e.options.eventEmitter,uL={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},uR={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},u$=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},uU={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:uL.angleAxisId,includeHidden:!1,name:void 0,reversed:uL.reversed,scale:uL.scale,tick:uL.tick,tickCount:void 0,ticks:void 0,type:uL.type,unit:void 0},uz={allowDataOverflow:uR.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:uR.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:uR.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:uR.scale,tick:uR.tick,tickCount:uR.tickCount,ticks:void 0,type:uR.type,unit:void 0},uF={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:uL.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:uL.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:uL.scale,tick:uL.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},uK={allowDataOverflow:uR.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:uR.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:uR.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:uR.scale,tick:uR.tick,tickCount:uR.tickCount,ticks:void 0,type:"category",unit:void 0},uB=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?uF:uU,uq=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?uK:uz,uW=e=>e.polarOptions,uH=rV([nN,nD,nB],nu),uY=rV([uW,uH],(e,t)=>{if(null!=e)return k(e.innerRadius,t,0)}),uV=rV([uW,uH],(e,t)=>{if(null!=e)return k(e.outerRadius,t,.8*t)}),uX=rV([uW],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});rV([uB,uX],u$);var uG=rV([uH,uY,uV],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});rV([uq,uG],u$);var uZ=rV([n0,uW,uY,uV,nN,nD],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:u,endAngle:c}=t;return{cx:k(o,i,i/2),cy:k(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:c,clockWise:!1}}}),uJ=(e,t)=>t,uQ=(e,t,r)=>r;function u0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u0(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u2=[0,"auto"],u5={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},u3=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?u5:r},u6={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:u2,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},u4=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?u6:r},u8={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},u7=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?u8:r},u9=(e,t,r)=>{switch(t){case"xAxis":return u3(e,r);case"yAxis":return u4(e,r);case"zAxis":return u7(e,r);case"angleAxis":return uB(e,r);case"radiusAxis":return uq(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ce=(e,t,r)=>{switch(t){case"xAxis":return u3(e,r);case"yAxis":return u4(e,r);case"angleAxis":return uB(e,r);case"radiusAxis":return uq(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ct=e=>e.graphicalItems.countOfBars>0;function cr(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var cn=rV([uJ,uQ],cr),ci=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),ca=rV([e=>e.graphicalItems.cartesianItems,u9,cn],ci),co=e=>e.filter(e=>void 0===e.stackId),cl=rV([ca],co),cu=e=>e.map(e=>e.data).filter(Boolean).flat(1),cc=rV([ca],cu),cs=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},cf=rV([cc,(e,t,r,n)=>n?lL(e):lI(e)],cs),cd=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:ng(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:ng(e,t)}))):e.map(e=>({value:e})),ch=rV([cf,u9,ca],cd);function cp(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function cy(e){return e.filter(e=>T(e)||e instanceof Date).map(Number).filter(e=>!1===A(e))}var cg=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:nO(e,i.map(e=>e.dataKey),r),graphicalItems:i}]})),cv=rV([cf,ca,uC],cg),cm=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=nS(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},cb=rV([cv,lI,uJ],cm),cx=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>cp(n,e)),l=ng(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||A(t)||!r.length?[]:cy(r.flatMap(r=>{var n,i,a=ng(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,lR(n)&&lR(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:ng(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),cw=rV(cf,u9,cl,uJ,cx);function cO(e){var{value:t}=e;if(T(t)||t instanceof Date)return t}var cj=e=>{var t=cy(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},cP=(e,t,r)=>{var n=e.map(cO).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&N(n))?n5()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},cS=e=>{var t;if(null==e||!("domain"in e))return u2;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=cy(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:u2},cA=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},c_=e=>e.referenceElements.dots,cM=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),cT=rV([c_,uJ,uQ],cM),cE=e=>e.referenceElements.areas,cC=rV([cE,uJ,uQ],cM),ck=e=>e.referenceElements.lines,cN=rV([ck,uJ,uQ],cM),cD=(e,t)=>{var r=cy(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cI=rV(cT,uJ,cD),cL=(e,t)=>{var r=cy(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cR=rV([cC,uJ],cL),c$=(e,t)=>{var r=cy(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cU=rV(cN,uJ,c$),cz=rV(cI,cU,cR,(e,t,r)=>cA(e,r,t)),cF=rV([u9],cS),cK=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(lR(i))r=i;else if("function"==typeof i)return;if(lR(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(lU(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(lU(n))return lz(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(M(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&nA.test(o)){var u=nA.exec(o);if(null==u||null==t)i=void 0;else{var c=+u[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(M(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&n_.test(l)){var s=n_.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(lU(d))return null==t?d:lz(d,t,r)}}}(t,cA(r,i,cj(n)),e.allowDataOverflow)},cB=rV([u9,cF,cb,cw,cz],cK),cq=[0,1],cW=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,c=nb(t,a);return c&&null==l?n5()(0,r.length):"category"===u?cP(n,e,c):"expand"===i?cq:o}},cH=rV([u9,n0,cf,ch,uC,uJ,cB],cW),cY=(e,t,r,i,a)=>{if(null!=e){var{scale:o,type:l}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var u="scale".concat(L(o));return u in n?u:"point"}}},cV=rV([u9,n0,ct,uk,uJ],cY);function cX(e,t,r,i){if(null!=r&&null!=i){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(i);var a=function(e){if(null!=e){if(e in n)return n[e]();var t="scale".concat(L(e));if(t in n)return n[t]()}}(t);if(null!=a){var o=a.domain(r).range(i);return nx(o),o}}}var cG=(e,t,r)=>{var n=cS(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&lU(e))return uT(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&lU(e))return uE(e,t.tickCount,t.allowDecimals)}},cZ=rV([cH,ce,cV],cG),cJ=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&lU(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,cQ=rV([u9,cH,cZ,uJ],cJ),c0=rV(ch,u9,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(cy(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),c1=rV(c0,n0,e=>e.rootProps.barCategoryGap,nB,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!lR(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=k(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),c2=rV(u3,(e,t)=>{var r=u3(e,t);return null==r||"string"!=typeof r.padding?0:c1(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),c5=rV(u4,(e,t)=>{var r=u4(e,t);return null==r||"string"!=typeof r.padding?0:c1(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),c3=rV([nB,c2,nV,nY,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),c6=rV([nB,n0,c5,nV,nY,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),c4=(e,t,r,n)=>{var i;switch(t){case"xAxis":return c3(e,r,n);case"yAxis":return c6(e,r,n);case"zAxis":return null==(i=u7(e,r))?void 0:i.range;case"angleAxis":return uX(e);case"radiusAxis":return uG(e,r);default:return}},c8=rV([u9,c4],u$),c7=rV([u9,cV,cQ,c8],cX);function c9(e,t){return e.id<t.id?-1:+(e.id>t.id)}rV(ca,uJ,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>cp(t,e)));var se=(e,t)=>t,st=(e,t,r)=>r,sr=rV(nR,se,st,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(c9)),sn=rV(n$,se,st,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(c9)),si=(e,t)=>({width:e.width,height:t.height}),sa=(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}),so=(rV(nB,u3,si),(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}}),sl=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},su=(rV(nD,nB,sr,se,st,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=si(t,r);null==a&&(a=so(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),rV(nN,nB,sn,se,st,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=sa(t,r);null==a&&(a=sl(t,n,e));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),rV(nB,u4,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=nb(e,n),u=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&N(u))return u}}),sc=rV([n0,ch,u9,uJ],su),ss=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(nb(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},sf=rV([n0,ch,ce,uJ],ss);rV([n0,(e,t,r)=>{switch(t){case"xAxis":return u3(e,r);case"yAxis":return u4(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},cV,c7,sc,sf,c4,cZ,uJ],(e,t,r,n,i,a,o,l,u)=>{if(null==t)return null;var c=nb(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),rV([n0,ce,cV,c7,cZ,c4,sc,sf,uJ],(e,t,r,n,i,a,o,l,u)=>{if(null!=t&&null!=n){var c=nb(e,u),{type:s,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===u&&null!=a&&a.length>=2?2*S(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!A(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),rV([n0,ce,c7,c4,sc,sf,uJ],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=nb(e,o),{tickCount:u}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*S(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),rV(u9,c7,(e,t)=>{if(null!=e&&null!=t)return u1(u1({},e),{},{scale:t})});var sd=rV([u9,cV,cH,c8],cX);rV((e,t,r)=>u7(e,r),sd,(e,t)=>{if(null!=e&&null!=t)return u1(u1({},e),{},{scale:t})});var sh=rV([n0,nR,n$],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),sp=e=>e.options.defaultTooltipEventType,sy=e=>e.options.validateTooltipEventTypes;function sg(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function sv(e,t){return sg(t,sp(e),sy(e))}var sm=(e,t)=>{var r,n=Number(t);if(!A(n)&&null!=t)return n>=0?null==e||null==(r=e[n])?void 0:r.value:void 0};function sb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sb(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sw=(e,t,r,n)=>{if(null==t)return ra;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return ra;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return sx(sx({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return sx(sx({},ra),{},{coordinate:i.coordinate})},sO=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!lR(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},sj=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}},sP=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})},sS=e=>e.options.tooltipPayloadSearcher,sA=e=>e.tooltip;function s_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sT=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:u,dataStartIndex:c,dataEndIndex:s}=r;return e.reduce((e,r)=>{var f,d,h,p,y,{dataDefinedOnItem:g,settings:v}=r,m=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((f=g,d=l,null!=f?f:d),c,s),b=null!=(h=null==v?void 0:v.dataKey)?h:null==n?void 0:n.dataKey,x=null==v?void 0:v.nameKey;return Array.isArray(p=null!=n&&n.dataKey&&Array.isArray(m)&&!Array.isArray(m[0])&&"axis"===o?function(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):P()(e,t))===r)}(m,n.dataKey,i):a(m,t,u,x))?p.forEach(t=>{var r=sM(sM({},v),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(nT({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:ng(t.payload,t.dataKey),name:t.name}))}):e.push(nT({tooltipEntrySettings:v,dataKey:b,payload:p,value:ng(p,b),name:null!=(y=ng(p,x))?y:null==v?void 0:v.name})),e},[])}},sE=e=>{var t=n0(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},sC=e=>e.tooltip.settings.axisId,sk=e=>{var t=sE(e),r=sC(e);return ce(e,t,r)},sN=rV([sk,n0,ct,uk,sE],cY),sD=rV([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),sI=rV([sE,sC],cr),sL=rV([sD,sk,sI],ci),sR=rV([sL],cu),s$=rV([sR,lI],cs),sU=rV([s$,sk,sL],cd),sz=rV([sk],cS),sF=rV([s$,sL,uC],cg),sK=rV([sF,lI,sE],cm),sB=rV([sL],co),sq=rV([s$,sk,sB,sE],cx),sW=rV([c_,sE,sC],cM),sH=rV([sW,sE],cD),sY=rV([cE,sE,sC],cM),sV=rV([sY,sE],cL),sX=rV([ck,sE,sC],cM),sG=rV([sX,sE],c$),sZ=rV([sH,sG,sV],cA),sJ=rV([sk,sz,sK,sq,sZ],cK),sQ=rV([sk,n0,s$,sU,uC,sE,sJ],cW),s0=rV([sQ,sk,sN],cG),s1=rV([sk,sQ,s0,sE],cJ),s2=e=>{var t=sE(e),r=sC(e);return c4(e,t,r,!1)},s5=rV([sk,s2],u$),s3=rV([sk,sN,s1,s5],cX),s6=rV([n0,sU,sk,sE],su),s4=rV([n0,sU,sk,sE],ss),s8=rV([n0,sk,sN,s3,s2,s6,s4,sE],(e,t,r,n,i,a,o,l)=>{if(t){var{type:u}=t,c=nb(e,l);if(n){var s="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===u&&n.bandwidth?n.bandwidth()/s:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*S(i[0]-i[1])*f:f,c&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),s7=rV([sp,sy,e=>e.tooltip.settings],(e,t,r)=>sg(r.shared,e,t)),s9=e=>e.tooltip.settings.trigger,fe=e=>e.tooltip.settings.defaultIndex,ft=rV([sA,s7,s9,fe],sw),fr=rV([ft,s$],sO),fn=rV([s8,fr],sm),fi=rV([ft],e=>{if(e)return e.dataKey}),fa=rV([sA,s7,s9,fe],sP),fo=rV([nN,nD,n0,nB,s8,fe,fa,sS],sj),fl=rV([ft,fo],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),fu=rV([ft],e=>e.active),fc=rV([fa,fr,lI,sk,fn,sS,s7],sT);rV([fc],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))});var fs=()=>r5(uk),ff=(e,t)=>t,fd=(e,t,r)=>r,fh=(e,t,r,n)=>n,fp=rV(s8,e=>r6()(e,e=>e.coordinate)),fy=rV([sA,ff,fd,fh],sw),fg=rV([fy,s$],sO),fv=(e,t,r)=>{if(null!=t){var n=sA(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},fm=rV([sA,ff,fd,fh],sP),fb=rV([nN,nD,n0,nB,s8,fh,fm,sS],sj),fx=rV([fy,fb],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),fw=rV(s8,fg,sm),fO=rV([fm,fg,lI,sk,fw,sS,ff],sT),fj=rV([fy],e=>({isActive:e.active,activeIndex:e.index})),fP=rV([(e,t)=>t,n0,uZ,sE,s5,s8,fp,nB],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var u=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?nh({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(u){var c=nv(nk(u,t),o,a,n,i),s=nC(t,a,c,u);return{activeIndex:String(c),activeCoordinate:s}}}}),fS=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},fA=ez("mouseClick"),f_=tI();f_.startListening({actionCreator:fA,effect:(e,t)=>{var r=e.payload,n=fP(t.getState(),fS(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(ry({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var fM=ez("mouseMove"),fT=tI();function fE(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}function fC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fC(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fT.startListening({actionCreator:fM,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=sv(n,n.tooltip.settings.shared),a=fP(n,fS(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(rp({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(rd()))}});var fN=e8({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=fk(fk({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:fD,removeXAxis:fI,addYAxis:fL,removeYAxis:fR,addZAxis:f$,removeZAxis:fU,updateYAxisWidth:fz}=fN.actions,fF=fN.reducer,fK=e8({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=eI(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=eI(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=eI(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:fB,removeBar:fq,addCartesianGraphicalItem:fW,replaceCartesianGraphicalItem:fH,removeCartesianGraphicalItem:fY,addPolarGraphicalItem:fV,removePolarGraphicalItem:fX}=fK.actions,fG=fK.reducer,fZ=e8({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=eI(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=eI(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=eI(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:fJ,removeDot:fQ,addArea:f0,removeArea:f1,addLine:f2,removeLine:f5}=fZ.actions,f3=fZ.reducer,f6={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},f4=e8({name:"brush",initialState:f6,reducers:{setBrushSettings:(e,t)=>null==t.payload?f6:t.payload}}),{setBrushSettings:f8}=f4.actions,f7=f4.reducer,f9=e8({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=eI(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:de,setLegendSettings:dt,addLegendPayload:dr,removeLegendPayload:dn}=f9.actions,di=f9.reducer,da={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},dl=e8({name:"rootProps",initialState:da,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:da.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),du=dl.reducer,{updateOptions:dc}=dl.actions,ds=e8({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:df,removeRadiusAxis:dd,addAngleAxis:dh,removeAngleAxis:dp}=ds.actions,dy=ds.reducer,dg=e8({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:dv}=dg.actions,dm=dg.reducer,db=ez("keyDown"),dx=ez("focus"),dw=tI();dw.startListening({actionCreator:db,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(sO(n,s$(r))),o=s8(r);if("Enter"===i){var l=fb(r,"axis","hover",String(n.index));t.dispatch(rv({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var u=a+("ArrowRight"===i?1:-1)*("left-to-right"===sh(r)?1:-1);if(null!=o&&!(u>=o.length)&&!(u<0)){var c=fb(r,"axis","hover",String(u));t.dispatch(rv({active:!0,activeIndex:u.toString(),activeDataKey:void 0,activeCoordinate:c}))}}}}}),dw.startListening({actionCreator:dx,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=fb(r,"axis","hover",String("0"));t.dispatch(rv({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var dO=ez("externalEvent"),dj=tI();dj.startListening({actionCreator:dO,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:fl(r),activeDataKey:fi(r),activeIndex:fr(r),activeLabel:fn(r),activeTooltipIndex:fr(r),isTooltipActive:fu(r)};e.payload.handler(n,e.payload.reactEvent)}}});var dP=rV([sA],e=>e.tooltipItemPayloads),dS=rV([dP,sS,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),dA=ez("touchMove"),d_=tI();d_.startListening({actionCreator:dA,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=sv(n,n.tooltip.settings.shared);if("axis"===i){var a=fP(n,fS({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(rp({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],u=document.elementFromPoint(l.clientX,l.clientY);if(!u||!u.getAttribute)return;var c=u.getAttribute(nU),s=null!=(o=u.getAttribute(nz))?o:void 0,f=dS(t.getState(),c,s);t.dispatch(rs({activeDataKey:s,activeIndex:c,activeCoordinate:f}))}}});var dM=H({brush:f7,cartesianAxis:fF,chartData:rj,graphicalItems:fG,layout:rT,legend:di,options:tq,polarAxis:dy,polarOptions:dm,referenceElements:f3,rootProps:du,tooltip:rm}),dT=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r,n=eW(),{reducer:i,middleware:a,devTools:o=!0,duplicateMiddlewareCheck:l=!0,preloadedState:u,enhancers:c}=e||{};if("function"==typeof i)t=i;else if(W(i))t=H(i);else throw Error(tF(1));r="function"==typeof a?a(n):n();let s=Y;o&&(s=e$({trace:!1,..."object"==typeof o&&o}));let f=eV(function(...e){return t=>(r,n)=>{let i=t(r,n),a=()=>{throw Error(F(15))},o={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=Y(...e.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r));return function e(t,r,n){if("function"!=typeof t)throw Error(F(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(F(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(F(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,u=0,c=!1;function s(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(c)throw Error(F(3));return a}function d(e){if("function"!=typeof e)throw Error(F(4));if(c)throw Error(F(5));let t=!0;s();let r=u++;return l.set(r,e),function(){if(t){if(c)throw Error(F(6));t=!1,s(),l.delete(r),o=null}}}function h(e){if(!W(e))throw Error(F(7));if(void 0===e.type)throw Error(F(8));if("string"!=typeof e.type)throw Error(F(17));if(c)throw Error(F(9));try{c=!0,a=i(a,e)}finally{c=!1}return(o=l).forEach(e=>{e()}),e}return h({type:q.INIT}),{dispatch:h,subscribe:d,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(F(10));i=e,h({type:q.REPLACE})},[K]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(F(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:d(t)}},[K](){return this}}}}}(t,u,s(..."function"==typeof c?c(f):f()))}({reducer:dM,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([f_.middleware,fT.middleware,dw.middleware,dj.middleware,d_.middleware]),devTools:{serialize:{replacer:fE},name:"recharts-".concat(t)}})};function dE(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=nH(),o=(0,a.useRef)(null);return i?r:(null==o.current&&(o.current=dT(t,n)),a.createElement(ri,{context:rZ,store:o.current},r))}var dC=e=>{var{chartData:t}=e,r=rQ(),n=nH();return(0,a.useEffect)(()=>n?()=>{}:(r(rx(t)),()=>{r(rx(void 0))}),[t,r,n]),null};function dk(e){var{layout:t,width:r,height:n,margin:i}=e;return rQ(),nH(),null}function dN(e){return rQ(),null}function dD(e){return rQ(),null}var dI=r(29632),dL=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],dR=["points","pathLength"],d$={svg:["viewBox","children"],polygon:dR,polyline:dR},dU=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],dz=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,a.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{dU.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},dF=(e,t,r)=>n=>(e(t,r,n),null),dK=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];dU.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=dF(a,t,r))}),n},dB=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",dq=null,dW=null,dH=e=>{if(e===dq&&Array.isArray(dW))return dW;var t=[];return a.Children.forEach(e,e=>{I(e)||((0,dI.isFragment)(e)?t=t.concat(dH(e.props.children)):t.push(e))}),dW=t,dq=e,t};function dY(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>dB(e)):[dB(t)],dH(e).forEach(e=>{var t=P()(e,"type.displayName")||P()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var dV=(e,t,r,n)=>{var i,a=null!=(i=n&&(null==d$?void 0:d$[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||dL.includes(t))||r&&dU.includes(t)},dX=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;dV(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},dG=()=>r5(e=>e.rootProps.accessibilityLayer),dZ=["children","width","height","viewBox","className","style","title","desc"];function dJ(){return(dJ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var dQ=(0,a.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:o,className:l,style:u,title:c,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dZ),d=o||{width:n,height:i,x:0,y:0},h=(0,x.$)("recharts-surface",l);return a.createElement("svg",dJ({},dX(f,!0,"svg"),{className:h,width:n,height:i,style:u,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),a.createElement("title",null,c),a.createElement("desc",null,s),r)}),d0=["children"];function d1(){return(d1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d2={width:"100%",height:"100%"},d5=(0,a.forwardRef)((e,t)=>{var r,n,i=nJ(),o=nQ(),l=dG();if(!l$(i)||!l$(o))return null;var{children:u,otherAttributes:c,title:s,desc:f}=e;return r="number"==typeof c.tabIndex?c.tabIndex:l?0:void 0,n="string"==typeof c.role?c.role:l?"application":void 0,a.createElement(dQ,d1({},c,{title:s,desc:f,role:n,tabIndex:r,width:i,height:o,style:d2,ref:t}),u)}),d3=e=>{var{children:t}=e,r=r5(nV);if(!r)return null;var{width:n,height:i,y:o,x:l}=r;return a.createElement(dQ,{width:n,height:i,x:l,y:o},t)},d6=(0,a.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d0);return nH()?a.createElement(d3,null,r):a.createElement(d5,d1({ref:t},n),r)});function d4(e){return e.tooltip.syncInteraction}new(r(11117));var d8=(0,a.createContext)(null),d7=()=>(0,a.useContext)(d8),d9=(0,a.createContext)(null);function he(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var ht=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:c,onMouseEnter:s,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:g,style:v,width:m}=e,b=rQ(),[w,O]=(0,a.useState)(null),[j,P]=(0,a.useState)(null);rQ(),r5(uN),r5(uI),rQ(),r5(uD),r5(s8),n1(),nX(),r5(e=>e.rootProps.className),r5(uN),r5(uI),rQ();var S=function(){rQ();var[e,t]=(0,a.useState)(null);return r5(nI),t}(),A=(0,a.useCallback)(e=>{S(e),"function"==typeof t&&t(e),O(e),P(e)},[S,t,O,P]),_=(0,a.useCallback)(e=>{b(fA(e)),b(dO({handler:o,reactEvent:e}))},[b,o]),M=(0,a.useCallback)(e=>{b(fM(e)),b(dO({handler:s,reactEvent:e}))},[b,s]),T=(0,a.useCallback)(e=>{b(rd()),b(dO({handler:f,reactEvent:e}))},[b,f]),E=(0,a.useCallback)(e=>{b(fM(e)),b(dO({handler:d,reactEvent:e}))},[b,d]),C=(0,a.useCallback)(()=>{b(dx())},[b]),k=(0,a.useCallback)(e=>{b(db(e.key))},[b]),N=(0,a.useCallback)(e=>{b(dO({handler:l,reactEvent:e}))},[b,l]),D=(0,a.useCallback)(e=>{b(dO({handler:u,reactEvent:e}))},[b,u]),I=(0,a.useCallback)(e=>{b(dO({handler:c,reactEvent:e}))},[b,c]),L=(0,a.useCallback)(e=>{b(dO({handler:h,reactEvent:e}))},[b,h]),R=(0,a.useCallback)(e=>{b(dO({handler:g,reactEvent:e}))},[b,g]),$=(0,a.useCallback)(e=>{b(dA(e)),b(dO({handler:y,reactEvent:e}))},[b,y]),U=(0,a.useCallback)(e=>{b(dO({handler:p,reactEvent:e}))},[b,p]);return a.createElement(d8.Provider,{value:w},a.createElement(d9.Provider,{value:j},a.createElement("div",{className:(0,x.$)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?he(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):he(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:m,height:i},v),onClick:_,onContextMenu:N,onDoubleClick:D,onFocus:C,onKeyDown:k,onMouseDown:I,onMouseEnter:M,onMouseLeave:T,onMouseMove:E,onMouseUp:L,onTouchEnd:U,onTouchMove:$,onTouchStart:R,ref:A},r)))}),hr=rV([nB],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),hn=rV([hr,nN,nD],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),hi=()=>r5(hn),ha=(0,a.createContext)(void 0),ho=e=>{var{children:t}=e,[r]=(0,a.useState)("".concat(C("recharts"),"-clip")),n=hi();if(null==n)return null;var{x:i,y:o,width:l,height:u}=n;return a.createElement(ha.Provider,{value:r},a.createElement("defs",null,a.createElement("clipPath",{id:r},a.createElement("rect",{x:i,y:o,height:u,width:l}))),t)},hl=["children","className","width","height","style","compact","title","desc"],hu=(0,a.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:o,style:l,compact:u,title:c,desc:s}=e,f=dX(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hl),!1);return u?a.createElement(d6,{otherAttributes:f,title:c,desc:s},r):a.createElement(ht,{className:n,style:l,width:i,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},a.createElement(d6,{otherAttributes:f,title:c,desc:s,ref:t},a.createElement(ho,null,r)))});function hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hs(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hc(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var hf=["width","height","layout"];function hd(){return(hd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hh={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},hp=(0,a.forwardRef)(function(e,t){var r,n=hs(e.categoricalChartProps,hh),{width:i,height:o,layout:l}=n,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,hf);if(!l$(i)||!l$(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e;return a.createElement(dE,{preloadedState:{options:{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0}},reduxStoreName:null!=(r=n.id)?r:c},a.createElement(dC,{chartData:n.data}),a.createElement(dk,{width:i,height:o,layout:l,margin:n.margin}),a.createElement(dN,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),a.createElement(dD,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),a.createElement(hu,hd({width:i,height:o},u,{ref:t})))}),hy=["item"],hg={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},hv=(0,a.forwardRef)((e,t)=>{var r=hs(e,hg);return a.createElement(hp,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:hy,tooltipPayloadSearcher:tK,categoricalChartProps:r,ref:t})}),hm=e=>e.graphicalItems.polarItems,hb=rV([uJ,uQ],cr),hx=rV([hm,u9,hb],ci),hw=rV([hx],cu),hO=rV([hw,lL],cs),hj=rV([hO,u9,hx],cd),hP=rV([hO,u9,hx],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:ng(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:ng(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),hS=()=>void 0,hA=rV([u9,cF,hS,hP,hS],cK),h_=rV([u9,n0,hO,hj,uC,uJ,hA],cW),hM=rV([h_,u9,cV],cG);function hT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hT(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}rV([u9,h_,hM,uJ],cJ);var hC=(e,t)=>t,hk=[],hN=(e,t,r)=>(null==r?void 0:r.length)===0?hk:r,hD=rV([lL,hC,hN],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>hE(hE({},t.presentationProps),e.props))),null!=n)return n}),hI=rV([hD,hC,hN],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=ng(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:nE(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),hL=rV([hm,hC],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),hR=rV([hD,hL,hN,nB],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:s,dataKey:f,nameKey:d,tooltipType:h}=i,p=Math.abs(i.minAngle),y=gc(c,s),g=Math.abs(y),v=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,m=a.filter(e=>0!==ng(e,f,0)).length,b=g-m*p-(g>=360?m:m-1)*v,x=a.reduce((e,t)=>{var r=ng(t,f,0);return e+(M(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=ng(e,f,0),s=ng(e,d,t),g=gu(i,l,e),m=(M(a)?a:0)/x,w=gt(gt({},e),o&&o[t]&&o[t].props),O=(r=t?n.endAngle+S(y)*v*(0!==a):c)+S(y)*((0!==a?p:0)+m*b),j=(r+O)/2,P=(g.innerRadius+g.outerRadius)/2,A=[{name:s,value:a,payload:w,dataKey:f,type:h}],_=nl(g.cx,g.cy,P,j);return n=gt(gt(gt(gt({},i.presentationProps),{},{percent:m,cornerRadius:u,name:s,tooltipPayload:A,midAngle:j,middleRadius:P,tooltipPosition:_},w),g),{},{value:ng(e,f),startAngle:r,endAngle:O,payload:w,paddingAngle:S(y)*v})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})});function h$(e){return rQ(),null}var hU=["children","className"];function hz(){return(hz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var hF=a.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hU),o=(0,x.$)("recharts-layer",n);return a.createElement("g",hz({className:o},dX(i,!0),{ref:t}),r)});function hK(){}function hB(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function hq(e){this._context=e}function hW(e){this._context=e}function hH(e){this._context=e}hq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:hB(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:hB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hW.prototype={areaStart:hK,areaEnd:hK,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:hB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hH.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:hB(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class hY{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function hV(e){this._context=e}function hX(e){this._context=e}function hG(e){return new hX(e)}hV.prototype={areaStart:hK,areaEnd:hK,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function hZ(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function hJ(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function hQ(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function h0(e){this._context=e}function h1(e){this._context=new h2(e)}function h2(e){this._context=e}function h5(e){this._context=e}function h3(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function h6(e,t){this._context=e,this._t=t}hX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},h0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:hQ(this,this._t0,hJ(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,hQ(this,hJ(this,r=hZ(this,e,t)),r);break;default:hQ(this,this._t0,r=hZ(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(h1.prototype=Object.create(h0.prototype)).point=function(e,t){h0.prototype.point.call(this,t,e)},h2.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},h5.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=h3(e),i=h3(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},h6.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};let h4=Math.PI,h8=2*h4,h7=h8-1e-6;function h9(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class pe{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?h9:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return h9;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e*=1,t*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,u=n-t,c=a-e,s=o-t,f=c*c+s*s;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(s*l-u*c)>1e-6&&i){let d=r-a,h=n-o,p=l*l+u*u,y=Math.sqrt(p),g=Math.sqrt(f),v=i*Math.tan((h4-Math.acos((p+f-(d*d+h*h))/(2*y*g)))/2),m=v/g,b=v/y;Math.abs(m-1)>1e-6&&this._append`L${e+m*c},${t+m*s}`,this._append`A${i},${i},0,0,${+(s*d>c*h)},${this._x1=e+b*l},${this._y1=t+b*u}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,i,a){if(e*=1,t*=1,r*=1,a=!!a,r<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),u=e+o,c=t+l,s=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${u},${c}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${u},${c}`,r&&(f<0&&(f=f%h8+h8),f>h7?this._append`A${r},${r},0,1,${s},${e-o},${t-l}A${r},${r},0,1,${s},${this._x1=u},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=h4)},${s},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function pt(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new pe(t)}function pr(e){return e[0]}function pn(e){return e[1]}function pi(e,t){var r=r9(!0),n=null,i=hG,a=null,o=pt(l);function l(l){var u,c,s,f=(l=r7(l)).length,d=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(c,u,l),+t(c,u,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?pr:r9(e),t="function"==typeof t?t:void 0===t?pn:r9(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:r9(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:r9(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:r9(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function pa(e,t,r){var n=null,i=r9(!0),a=null,o=hG,l=null,u=pt(c);function c(c){var s,f,d,h,p,y=(c=r7(c)).length,g=!1,v=Array(y),m=Array(y);for(null==a&&(l=o(p=u())),s=0;s<=y;++s){if(!(s<y&&i(h=c[s],s,c))===g)if(g=!g)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=s-1;d>=f;--d)l.point(v[d],m[d]);l.lineEnd(),l.areaEnd()}g&&(v[s]=+e(h,s,c),m[s]=+t(h,s,c),l.point(n?+n(h,s,c):v[s],r?+r(h,s,c):m[s]))}if(p)return l=null,p+""||null}function s(){return pi().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?pr:r9(+e),t="function"==typeof t?t:void 0===t?r9(0):r9(+t),r="function"==typeof r?r:void 0===r?pn:r9(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:r9(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:r9(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:r9(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:r9(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:r9(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:r9(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:r9(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}function po(){return(po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pl(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}pe.prototype;var pc={curveBasisClosed:function(e){return new hW(e)},curveBasisOpen:function(e){return new hH(e)},curveBasis:function(e){return new hq(e)},curveBumpX:function(e){return new hY(e,!0)},curveBumpY:function(e){return new hY(e,!1)},curveLinearClosed:function(e){return new hV(e)},curveLinear:hG,curveMonotoneX:function(e){return new h0(e)},curveMonotoneY:function(e){return new h1(e)},curveNatural:function(e){return new h5(e)},curveStep:function(e){return new h6(e,.5)},curveStepAfter:function(e){return new h6(e,1)},curveStepBefore:function(e){return new h6(e,0)}},ps=e=>lR(e.x)&&lR(e.y),pf=e=>e.x,pd=e=>e.y,ph=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat(L(e));return("curveMonotone"===r||"curveBump"===r)&&t?pc["".concat(r).concat("vertical"===t?"Y":"X")]:pc[r]||hG},pp=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=ph(r,a),u=o?n.filter(ps):n;if(Array.isArray(i)){var c=o?i.filter(e=>ps(e)):i,s=u.map((e,t)=>pu(pu({},e),{},{base:c[t]}));return(t="vertical"===a?pa().y(pd).x1(pf).x0(e=>e.base.x):pa().x(pf).y1(pd).y0(e=>e.base.y)).defined(ps).curve(l),t(s)}return(t="vertical"===a&&M(i)?pa().y(pd).x1(pf).x0(i):M(i)?pa().x(pf).y1(pd).y0(i):pi().x(pf).y(pd)).defined(ps).curve(l),t(u)},py=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var o=r&&r.length?pp(e):n;return a.createElement("path",po({},dX(e,!1),dz(e),{className:(0,x.$)("recharts-curve",t),d:null===o?void 0:o,ref:i}))},pg={isSsr:!0};function pv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pv(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var pb={widthCache:{},cacheCount:0},px={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},pw="recharts_measurement_span",pO=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||pg.isSsr)return{width:0,height:0};var n=(Object.keys(t=pm({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(pb.widthCache[i])return pb.widthCache[i];try{var a=document.getElementById(pw);a||((a=document.createElement("span")).setAttribute("id",pw),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=pm(pm({},px),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),u={width:l.width,height:l.height};return pb.widthCache[i]=u,++pb.cacheCount>2e3&&(pb.cacheCount=0,pb.widthCache={}),u}catch(e){return{width:0,height:0}}},pj=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,pP=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,pS=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,pA=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p_={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},pM=Object.keys(p_);class pT{static parse(e){var t,[,r,n]=null!=(t=pA.exec(e))?t:[];return new pT(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,A(e)&&(this.unit=""),""===t||pS.test(t)||(this.num=NaN,this.unit=""),pM.includes(t)&&(this.num=e*p_[t],this.unit="px")}add(e){return this.unit!==e.unit?new pT(NaN,""):new pT(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new pT(NaN,""):new pT(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new pT(NaN,""):new pT(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new pT(NaN,""):new pT(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return A(this.num)}}function pE(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=pj.exec(t))?r:[],o=pT.parse(null!=n?n:""),l=pT.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";t=t.replace(pj,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var c,[,s,f,d]=null!=(c=pP.exec(t))?c:[],h=pT.parse(null!=s?s:""),p=pT.parse(null!=d?d:""),y="+"===f?h.add(p):h.subtract(p);if(y.isNaN())return"NaN";t=t.replace(pP,y.toString())}return t}var pC=/\(([^()]*)\)/;function pk(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=pC.exec(r));){var[,n]=t;r=r.replace(pC,pE(n))}return r}(t),t=pE(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var pN=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],pD=["dx","dy","angle","className","breakAll"];function pI(){return(pI=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pL(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var pR=/[ \f\n\r\t\v\u2028\u2029]+/,p$=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];I(t)||(i=r?t.toString().split(""):t.toString().split(pR));var a=i.map(e=>({word:e,width:pO(e,n).width})),o=r?0:pO("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},pU=(e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:u,breakAll:c}=e,s=M(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i||!(d.length>o||h(d).width>Number(n)))return d;for(var p=e=>{var t=f(p$({breakAll:c,style:u,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||h(t).width>Number(n),t]},y=0,g=l.length-1,v=0;y<=g&&v<=l.length-1;){var m=Math.floor((y+g)/2),[b,x]=p(m-1),[w]=p(m);if(b||w||(y=m+1),b&&w&&(g=m-1),!b&&w){a=x;break}v++}return a||d},pz=e=>[{words:I(e)?[]:e.toString().split(pR)}],pF=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!pg.isSsr){var l=p$({breakAll:a,children:n,style:i});if(!l)return pz(n);var{wordsWithComputedWidth:u,spaceWidth:c}=l;return pU({breakAll:a,children:n,maxLines:o,style:i},u,c,t,r)}return pz(n)},pK="#808080",pB=(0,a.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:o="1em",capHeight:l="0.71em",scaleToFit:u=!1,textAnchor:c="start",verticalAnchor:s="end",fill:f=pK}=e,d=pL(e,pN),h=(0,a.useMemo)(()=>pF({breakAll:d.breakAll,children:d.children,maxLines:d.maxLines,scaleToFit:u,style:d.style,width:d.width}),[d.breakAll,d.children,d.maxLines,u,d.style,d.width]),{dx:p,dy:y,angle:g,className:v,breakAll:m}=d,b=pL(d,pD);if(!T(n)||!T(i))return null;var w=n+(M(p)?p:0),O=i+(M(y)?y:0);switch(s){case"start":r=pk("calc(".concat(l,")"));break;case"middle":r=pk("calc(".concat((h.length-1)/2," * -").concat(o," + (").concat(l," / 2))"));break;default:r=pk("calc(".concat(h.length-1," * -").concat(o,")"))}var j=[];if(u){var P=h[0].width,{width:S}=d;j.push("scale(".concat(M(S)?S/P:1,")"))}return g&&j.push("rotate(".concat(g,", ").concat(w,", ").concat(O,")")),j.length&&(b.transform=j.join(" ")),a.createElement("text",pI({},dX(b,!0),{ref:t,x:w,y:O,className:(0,x.$)("recharts-text",v),textAnchor:c,fill:f.includes("url")?pK:f}),h.map((e,t)=>{var n=e.words.join(m?"":" ");return a.createElement("tspan",{x:w,dy:0===t?r:o,key:"".concat(n,"-").concat(t)},n)}))});pB.displayName="Text";var pq=e=>null;pq.displayName="Cell";var pW=r(92867),pH=r.n(pW),pY=r(12728),pV=r.n(pY),pX=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],pG=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),pZ=(e,t)=>r=>pG(pX(e,t),r),pJ=(e,t)=>r=>pG([...pX(e,t).map((e,t)=>e*t).slice(1),0],r),pQ=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var u=pZ(e,t),c=pZ(r,n),s=pJ(e,t),f=e=>e>1?1:e<0?0:e,d=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=u(r)-t,a=s(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=f(r-i/a)}return c(r)};return d.isStepper=!1,d},p0=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},p1=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return pQ(e);case"spring":return p0();default:if("cubic-bezier"===e.split("(")[0])return pQ(e)}return"function"==typeof e?e:null};function p2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p5(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p2(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p3=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),p6=(e,t,r)=>e.map(e=>"".concat(p3(e)," ").concat(t,"ms ").concat(r)).join(","),p4=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),p8=(e,t)=>Object.keys(t).reduce((r,n)=>p5(p5({},r),{},{[n]:e(n,t[n])}),{});function p7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p7(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ye=(e,t,r)=>e+(t-e)*r,yt=e=>{var{from:t,to:r}=e;return t!==r},yr=(e,t,r)=>{var n=p8((t,r)=>{if(yt(r)){var[n,i]=e(r.from,r.to,r.velocity);return p9(p9({},r),{},{from:n,velocity:i})}return r},t);return r<1?p8((e,t)=>yt(t)?p9(p9({},t),{},{velocity:ye(t.velocity,n[e].velocity,r),from:ye(t.from,n[e].from,r)}):t,t):yr(e,n,r-1)};let yn=(e,t,r,n,i,a)=>{var o=p4(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>p9(p9({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),u=()=>p8((e,t)=>t.from,l),c=()=>!Object.values(l).filter(yt).length,s=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=yr(r,l,d),i(p9(p9(p9({},e),t),u())),o=n,c()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,u=null,c=i.reduce((r,n)=>p9(p9({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,d=p8((e,t)=>ye(...t,r(f)),c);if(a(p9(p9(p9({},e),t),d)),f<1)u=o.setTimeout(s);else{var h=p8((e,t)=>ye(...t,r(1)),c);a(p9(p9(p9({},e),t),h))}};return()=>(u=o.setTimeout(s),()=>{u()})}(e,t,r,n,o,i,a)};class yi{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var ya=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function yo(){return(yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yl(Object(r),!0).forEach(function(t){yc(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yc(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class ys extends a.PureComponent{constructor(e,t){super(e,t),yc(this,"mounted",!1),yc(this,"manager",null),yc(this,"stopJSAnimation",null),yc(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!pV()(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var c=u||i?o:e.to;this.state&&l&&(n&&l[n]!==c||!n&&l!==c)&&this.setState({style:n?{[n]:c}:c}),this.runAnimation(yu(yu({},this.props),{},{from:c,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,u=yn(t,r,p1(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=u()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof u||"spring"===a)return void this.runJSAnimation(e);var c=n?{[n]:i}:i,s=p6(Object.keys(c),r,a);this.manager.start([o,t,yu(yu({},c),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:o,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ya),g=a.Children.count(t),v=this.state.style;if("function"==typeof t)return t(v);if(!l||0===g||n<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,a.cloneElement)(e,yu(yu({},y),{},{style:yu(yu({},t),v),className:r}))};return 1===g?m(a.Children.only(t)):a.createElement("div",null,a.Children.map(t,e=>m(e)))}}yc(ys,"displayName","Animate"),yc(ys,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var yf=(0,a.createContext)(null);function yd(e){var t,r,n,i,o,l,u,c,s=(0,a.useContext)(yf);return a.createElement(ys,yo({},e,{animationManager:null!=(u=null!=(c=e.animationManager)?c:s)?u:(t=new yi,n=()=>null,i=!1,o=null,l=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[r,...a]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,a),r);return}l(r),o=t.setTimeout(l.bind(null,a));return}"object"==typeof e&&n(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,o&&(o(),o=null),l(e)},subscribe:e=>(n=e,()=>{n=()=>null}),getTimeoutController:()=>t})}))}function yh(){return(yh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var yp=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),a+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),a+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+u*d,",").concat(t,"\n            L ").concat(e+r-u*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-u*d,",").concat(t+n,"\n            L ").concat(e+u*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},yy={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},yg=e=>{var t=hs(e,yy),r=(0,a.useRef)(null),[n,i]=(0,a.useState)(-1);(0,a.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:o,y:l,width:u,height:c,radius:s,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isAnimationActive:y,isUpdateAnimationActive:g}=t;if(o!==+o||l!==+l||u!==+u||c!==+c||0===u||0===c)return null;var v=(0,x.$)("recharts-rectangle",f);return g?a.createElement(yd,{canBegin:n>0,from:{width:u,height:c,x:o,y:l},to:{width:u,height:c,x:o,y:l},duration:h,animationEasing:d,isActive:g},e=>{var{width:i,height:o,x:l,y:u}=e;return a.createElement(yd,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,isActive:y,easing:d},a.createElement("path",yh({},dX(t,!0),{className:v,d:yp(l,u,i,o,s),ref:r})))}):a.createElement("path",yh({},dX(t,!0),{className:v,d:yp(o,l,u,c,s)}))};function yv(){return(yv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var ym=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},yb={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},yx=e=>{var t=hs(e,yb),r=(0,a.useRef)(),[n,i]=(0,a.useState)(-1);(0,a.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:o,y:l,upperWidth:u,lowerWidth:c,height:s,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isUpdateAnimationActive:y}=t;if(o!==+o||l!==+l||u!==+u||c!==+c||s!==+s||0===u&&0===c||0===s)return null;var g=(0,x.$)("recharts-trapezoid",f);return y?a.createElement(yd,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:s,x:o,y:l},to:{upperWidth:u,lowerWidth:c,height:s,x:o,y:l},duration:h,animationEasing:d,isActive:y},e=>{var{upperWidth:i,lowerWidth:o,height:l,x:u,y:c}=e;return a.createElement(yd,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,easing:d},a.createElement("path",yv({},dX(t,!0),{className:g,d:ym(u,c,i,o,l),ref:r})))}):a.createElement("g",null,a.createElement("path",yv({},dX(t,!0),{className:g,d:ym(o,l,u,c,s)})))};function yw(){return(yw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var yO=(e,t)=>S(t-e)*Math.min(Math.abs(t-e),359.999),yj=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:u}=e,c=l*(o?1:-1)+n,s=Math.asin(l/c)/na,f=u?i:i+a*s,d=nl(t,r,c,f);return{center:d,circleTangency:nl(t,r,n,f),lineTangency:nl(t,r,c*Math.cos(s*na),u?i-a*s:i),theta:s}},yP=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=yO(a,o),u=a+l,c=nl(t,r,i,a),s=nl(t,r,i,u),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var d=nl(t,r,n,a),h=nl(t,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=u),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},yS=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:u,endAngle:c}=e,s=S(c-u),{circleTangency:f,lineTangency:d,theta:h}=yj({cx:t,cy:r,radius:i,angle:u,sign:s,cornerRadius:a,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:g}=yj({cx:t,cy:r,radius:i,angle:c,sign:-s,cornerRadius:a,cornerIsExternal:l}),v=l?Math.abs(u-c):Math.abs(u-c)-h-g;if(v<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):yP({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:c});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(v>180),",").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:w}=yj({cx:t,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:O,lineTangency:j,theta:P}=yj({cx:t,cy:r,radius:n,angle:c,sign:-s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),A=l?Math.abs(u-c):Math.abs(u-c)-w-P;if(A<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(A>180),",").concat(+(s>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m},yA={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y_=e=>{var t,r=hs(e,yA),{cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:u,forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:d,className:h}=r;if(l<o||f===d)return null;var p=(0,x.$)("recharts-sector",h),y=l-o,g=k(u,y,0,!0);return t=g>0&&360>Math.abs(f-d)?yS({cx:n,cy:i,innerRadius:o,outerRadius:l,cornerRadius:Math.min(g,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:d}):yP({cx:n,cy:i,innerRadius:o,outerRadius:l,startAngle:f,endAngle:d}),a.createElement("path",yw({},dX(r,!0),{className:p,d:t}))};let yM=Math.cos,yT=Math.sin,yE=Math.sqrt,yC=Math.PI,yk=2*yC,yN={draw(e,t){let r=yE(t/yC);e.moveTo(r,0),e.arc(0,0,r,0,yk)}},yD=yE(1/3),yI=2*yD,yL=yT(yC/10)/yT(7*yC/10),yR=yT(yk/10)*yL,y$=-yM(yk/10)*yL,yU=yE(3),yz=yE(3)/2,yF=1/yE(12),yK=(yF/2+1)*3;yE(3),yE(3);var yB=["type","size","sizeType"];function yq(){return(yq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yW(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var yY={symbolCircle:yN,symbolCross:{draw(e,t){let r=yE(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=yE(t/yI),n=r*yD;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=yE(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=yE(.8908130915292852*t),n=yR*r,i=y$*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=yk*t/5,o=yM(a),l=yT(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-yE(t/(3*yU));e.moveTo(0,2*r),e.lineTo(-yU*r,-r),e.lineTo(yU*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=yE(t/yK),n=r/2,i=r*yF,a=r*yF+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-yz*i,yz*n+-.5*i),e.lineTo(-.5*n-yz*a,yz*n+-.5*a),e.lineTo(-.5*o-yz*a,yz*o+-.5*a),e.lineTo(-.5*n+yz*i,-.5*i-yz*n),e.lineTo(-.5*n+yz*a,-.5*a-yz*n),e.lineTo(-.5*o+yz*a,-.5*a-yz*o),e.closePath()}}},yV=Math.PI/180,yX=e=>yY["symbol".concat(L(e))]||yN,yG=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*yV;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},yZ=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=yH(yH({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yB)),{},{type:t,size:r,sizeType:n}),{className:o,cx:l,cy:u}=i,c=dX(i,!0);return l===+l&&u===+u&&r===+r?a.createElement("path",yq({},c,{className:(0,x.$)("recharts-symbols",o),transform:"translate(".concat(l,", ").concat(u,")"),d:(()=>{var e=yX(t);return(function(e,t){let r=null,n=pt(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:r9(e||yN),t="function"==typeof t?t:r9(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:r9(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:r9(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(yG(r,n,t))()})()})):null};yZ.registerSymbol=(e,t)=>{yY["symbol".concat(L(e))]=t};var yJ=["option","shapeType","propTransformer","activeClassName","isActive"];function yQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yQ(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y1(e,t){return y0(y0({},t),e)}function y2(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return a.createElement(yg,r);case"trapezoid":return a.createElement(yx,r);case"sector":return a.createElement(y_,r);case"symbols":if("symbols"===t)return a.createElement(yZ,r);break;default:return null}}function y5(e){var t,{option:r,shapeType:n,propTransformer:i=y1,activeClassName:o="recharts-active-shape",isActive:l}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yJ);if((0,a.isValidElement)(r))t=(0,a.cloneElement)(r,y0(y0({},u),(0,a.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(pH()(r)&&"boolean"!=typeof r){var c=i(r,u);t=a.createElement(y2,{shapeType:n,elementProps:c})}else t=a.createElement(y2,{shapeType:n,elementProps:u});return l?a.createElement(hF,{className:o},t):t}var y3=(e,t)=>{var r=rQ();return(n,i)=>a=>{null==e||e(n,i,a),r(rs({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},y6=e=>{var t=rQ();return(r,n)=>i=>{null==e||e(r,n,i),t(rf())}},y4=(e,t)=>{var r=rQ();return(n,i)=>a=>{null==e||e(n,i,a),r(rh({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function y8(e){var{fn:t,args:r}=e;return rQ(),nH(),null}function y7(e){var{legendPayload:t}=e;return rQ(),r5(n0),null}var y9=["onMouseEnter","onClick","onMouseLeave"];function ge(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ge(Object(r),!0).forEach(function(t){gr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ge(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gr(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gn(){return(gn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gi(e){var t=(0,a.useMemo)(()=>dX(e,!1),[e]),r=(0,a.useMemo)(()=>dY(e.children,pq),[e.children]),n=(0,a.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=r5(e=>hI(e,n,r));return a.createElement(y7,{legendPayload:i})}function ga(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:nE(l,t),hide:u,type:c,color:o,unit:""}}}var go=(e,t)=>e>t?"start":e<t?"end":"middle",gl=(e,t,r)=>"function"==typeof t?t(e):k(t,r,.8*r),gu=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=nu(a,o),u=i+k(e.cx,a,a/2),c=n+k(e.cy,o,o/2),s=k(e.innerRadius,l,0);return{cx:u,cy:c,innerRadius:s,outerRadius:gl(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},gc=(e,t)=>S(t-e)*Math.min(Math.abs(t-e),360),gs=(e,t)=>{if(a.isValidElement(e))return a.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,x.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a.createElement(py,gn({},t,{type:"linear",className:r}))},gf=(e,t,r)=>{if(a.isValidElement(e))return a.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),a.isValidElement(n)))return n;var i=(0,x.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return a.createElement(pB,gn({},t,{alignmentBaseline:"middle",className:i}),n)};function gd(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:o,dataKey:l}=r;if(!n||!i||!t)return null;var u=dX(r,!1),c=dX(i,!1),s=dX(o,!1),f="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,d=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,n=nl(e.cx,e.cy,e.outerRadius+f,r),d=gt(gt(gt(gt({},u),e),{},{stroke:"none"},c),{},{index:t,textAnchor:go(n.x,e.cx)},n),h=gt(gt(gt(gt({},u),e),{},{fill:"none",stroke:e.fill},s),{},{index:t,points:[nl(e.cx,e.cy,e.outerRadius,r),n],key:"line"});return a.createElement(hF,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&gs(o,h),gf(i,d,ng(e,l)))});return a.createElement(hF,{className:"recharts-pie-labels"},d)}function gh(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:o}=e,l=r5(fr),{onMouseEnter:u,onClick:c,onMouseLeave:s}=i,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y9),d=y3(u,i.dataKey),h=y6(s),p=y4(c,i.dataKey);return null==t?null:a.createElement(a.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(o)===l,c=u?r:l?n:null,s=gt(gt({},e),{},{stroke:e.stroke,tabIndex:-1,[nU]:o,[nz]:i.dataKey});return a.createElement(hF,gn({tabIndex:-1,className:"recharts-pie-sector"},dK(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:h(e,o),onClick:p(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),a.createElement(y5,gn({option:c,isActive:u,shapeType:"sector"},s)))}),a.createElement(gd,{sectors:t,props:i,showLabels:o}))}function gp(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:u,activeShape:c,inactiveShape:s,onAnimationStart:f,onAnimationEnd:d}=t,h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,a.useRef)(C(t)),n=(0,a.useRef)(e);return n.current!==e&&(r.current=C(t),n.current=e),r.current}(t,"recharts-pie-"),p=r.current,[y,g]=(0,a.useState)(!0),v=(0,a.useCallback)(()=>{"function"==typeof d&&d(),g(!1)},[d]),m=(0,a.useCallback)(()=>{"function"==typeof f&&f(),g(!0)},[f]);return a.createElement(yd,{begin:o,duration:l,isActive:i,easing:u,from:{t:0},to:{t:1},onAnimationStart:m,onAnimationEnd:v,key:h},e=>{var{t:i}=e,o=[],l=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=p&&p[t],n=t>0?P()(e,"paddingAngle",0):0;if(r){var a=D(r.endAngle-r.startAngle,e.endAngle-e.startAngle),u=gt(gt({},e),{},{startAngle:l+n,endAngle:l+a(i)+n});o.push(u),l=u.endAngle}else{var{endAngle:c,startAngle:s}=e,f=D(0,c-s)(i),d=gt(gt({},e),{},{startAngle:l+n,endAngle:l+f+n});o.push(d),l=d.endAngle}}),r.current=o,a.createElement(hF,null,a.createElement(gh,{sectors:o,activeShape:c,inactiveShape:s,allOtherPieProps:t,showLabels:!y}))})}function gy(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,o=(0,a.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?a.createElement(gp,{props:e,previousSectorsRef:o}):a.createElement(gh,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function gg(e){var{hide:t,className:r,rootTabIndex:n}=e,i=(0,x.$)("recharts-pie",r);return t?null:a.createElement(hF,{tabIndex:n,className:i},a.createElement(gy,e))}var gv={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!pg.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function gm(e){var t=hs(e,gv),r=(0,a.useMemo)(()=>dY(e.children,pq),[e.children]),n=dX(t,!1),i=(0,a.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),o=r5(e=>hR(e,i,r));return a.createElement(a.Fragment,null,a.createElement(y8,{fn:ga,args:gt(gt({},t),{},{sectors:o})}),a.createElement(gg,gn({},t,{sectors:o})))}class gb extends a.PureComponent{constructor(){super(...arguments),gr(this,"id",C("recharts-pie-"))}render(){return a.createElement(a.Fragment,null,a.createElement(h$,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),a.createElement(gi,this.props),a.createElement(gm,this.props),this.props.children)}}gr(gb,"displayName","Pie"),gr(gb,"defaultProps",gv);var gx=r(51215);function gw(){return(gw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gP(e){return Array.isArray(e)&&T(e[0])&&T(e[1])?e.join(" ~ "):e}var gS=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:o,formatter:l,itemSorter:u,wrapperClassName:c,labelClassName:s,label:f,labelFormatter:d,accessibilityLayer:h=!1}=e,p=gj({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),y=gj({margin:0},i),g=!I(f),v=g?f:"",m=(0,x.$)("recharts-default-tooltip",c),b=(0,x.$)("recharts-tooltip-label",s);return g&&d&&null!=o&&(v=d(f,o)),a.createElement("div",gw({className:m,style:p},h?{role:"status","aria-live":"assertive"}:{}),a.createElement("p",{className:b,style:y},a.isValidElement(v)?v:"".concat(v)),(()=>{if(o&&o.length){var e=(u?r6()(o,u):o).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||l||gP,{value:u,name:c}=e,s=u,f=c;if(i){var d=i(u,c,e,r,o);if(Array.isArray(d))[s,f]=d;else{if(null==d)return null;s=d}}var h=gj({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return a.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:h},T(f)?a.createElement("span",{className:"recharts-tooltip-item-name"},f):null,T(f)?a.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,a.createElement("span",{className:"recharts-tooltip-item-value"},s),a.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},gA="recharts-tooltip-wrapper",g_={visibility:"hidden"};function gM(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:c}=e;if(a&&M(a[n]))return a[n];var s=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?s:f;var d=u[n];return null==d?0:o[n]?s<d?Math.max(f,d):Math.max(s,d):null==c?0:f+l>d+c?Math.max(s,d):Math.max(f,d)}function gT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gT(Object(r),!0).forEach(function(t){gC(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gC(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class gk extends a.PureComponent{constructor(){super(...arguments),gC(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),gC(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:o,hasPayload:l,isAnimationActive:u,offset:c,position:s,reverseDirection:f,useTranslate3d:d,viewBox:h,wrapperStyle:p,lastBoundingBox:y,innerRef:g,hasPortalFromProps:v}=this.props,{cssClasses:m,cssProperties:b}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:u,tooltipBox:c,useTranslate3d:s,viewBox:f}=e;return{cssProperties:t=c.height>0&&c.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=gM({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:u,tooltipDimension:c.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=gM({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:u,tooltipDimension:c.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:s}):g_,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,x.$)(gA,{["".concat(gA,"-right")]:M(r)&&t&&M(t.x)&&r>=t.x,["".concat(gA,"-left")]:M(r)&&t&&M(t.x)&&r<t.x,["".concat(gA,"-bottom")]:M(n)&&t&&M(t.y)&&n>=t.y,["".concat(gA,"-top")]:M(n)&&t&&M(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:c,position:s,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:h}),w=v?{}:gE(gE({transition:u&&e?"transform ".concat(r,"ms ").concat(n):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),O=gE(gE({},w),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},p);return a.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:m,style:O,ref:g},i)}}var gN=r(23854),gD=r.n(gN),gI=["x","y","top","left","width","height","className"];function gL(){return(gL=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gR(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var g$=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),gU=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:o=0,height:l=0,className:u}=e,c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gR(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gR(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:o,height:l},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gI));return M(t)&&M(r)&&M(o)&&M(l)&&M(n)&&M(i)?a.createElement("path",gL({},dX(c,!0),{className:(0,x.$)("recharts-cross",u),d:g$(t,r,o,l,n,i)})):null};function gz(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[nl(t,r,n,i),nl(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function gF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gF(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var gB=()=>r5(sk),gq=()=>{var e=gB(),t=r5(s8),r=r5(s3);return nM(gK(gK({},e),{},{scale:r}),t)};function gW(){return(gW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gH(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gV(e){var t,r,n,{coordinate:i,payload:o,index:l,offset:u,tooltipAxisBandSize:c,layout:s,cursor:f,tooltipEventType:d,chartName:h}=e;if(!f||!i||"ScatterChart"!==h&&"axis"!==d)return null;if("ScatterChart"===h)r=i,n=gU;else if("BarChart"===h)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===s?i.x-t:u.left+.5,y:"horizontal"===s?u.top+.5:i.y-t,width:"horizontal"===s?c:u.width-1,height:"horizontal"===s?u.height-1:c},n=yg;else if("radial"===s){var{cx:p,cy:y,radius:g,startAngle:v,endAngle:m}=gz(i);r={cx:p,cy:y,startAngle:v,endAngle:m,innerRadius:g,outerRadius:g},n=y_}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return gz(t);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=t,d=nl(l,u,c,f),h=nl(l,u,s,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}(s,i,u)},n=py;var b="object"==typeof f&&"className"in f?f.className:void 0,w=gY(gY(gY(gY({stroke:"#ccc",pointerEvents:"none"},u),r),dX(f,!1)),{},{payload:o,payloadIndex:l,className:(0,x.$)("recharts-tooltip-cursor",b)});return(0,a.isValidElement)(f)?(0,a.cloneElement)(f,w):(0,a.createElement)(n,w)}function gX(e){var t=gq(),r=nZ(),n=n1(),i=fs();return a.createElement(gV,gW({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}function gG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gZ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gG(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gJ(e){return e.dataKey}var gQ=[],g0={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!pg.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function g1(e){var t,r,n,i=hs(e,g0),{active:o,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:f,isAnimationActive:d,offset:h,payloadUniqBy:p,position:y,reverseDirection:g,useTranslate3d:v,wrapperStyle:m,cursor:b,shared:x,trigger:w,defaultIndex:O,portal:j,axisId:P}=i;rQ();var S="number"==typeof O?String(O):O,A=nX(),_=dG(),M=r5(e=>sv(e,x)),{activeIndex:T,isActive:E}=r5(e=>fj(e,M,w,S)),C=r5(e=>fO(e,M,w,S)),k=r5(e=>fw(e,M,w,S)),N=r5(e=>fx(e,M,w,S)),D=d7(),I=null!=o?o:E,[L,R]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,a.useState)({height:0,left:0,top:0,width:0}),n=(0,a.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}([C,I]),$="axis"===M?k:void 0;r5(e=>fv(e,M,w)),r5(uI),r5(uN),r5(uD),null==(t=r5(d4))||t.active;var U=null!=j?j:D;if(null==U)return null;var z=null!=C?C:gQ;I||(z=gQ),f&&z.length&&(r=C.filter(e=>null!=e.value&&(!0!==e.hide||i.includeHidden)),z=!0===p?gD()(r,gJ):"function"==typeof p?gD()(r,p):r);var F=z.length>0,K=a.createElement(gk,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:d,active:I,coordinate:N,hasPayload:F,offset:h,position:y,reverseDirection:g,useTranslate3d:v,viewBox:A,wrapperStyle:m,lastBoundingBox:L,innerRef:R,hasPortalFromProps:!!j},(n=gZ(gZ({},i),{},{payload:z,label:$,active:I,coordinate:N,accessibilityLayer:_}),a.isValidElement(s)?a.cloneElement(s,n):"function"==typeof s?a.createElement(s,n):a.createElement(gS,n)));return a.createElement(a.Fragment,null,(0,gx.createPortal)(K,U),I&&a.createElement(gX,{cursor:b,tooltipEventType:M,coordinate:N,payload:C,index:T}))}let g2=["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4"];function g5({data:e}){let t=e.map(e=>({name:e.type.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),value:e.value,count:e.count}));return 0===e.length?(0,i.jsxs)(d.Card,{children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsx)(d.CardTitle,{children:"Assets by Type"})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"No assets to display"})})]}):(0,i.jsxs)(d.Card,{children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsx)(d.CardTitle,{children:"Assets by Type"})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsx)(z,{width:"100%",height:300,children:(0,i.jsxs)(hv,{children:[(0,i.jsx)(gb,{data:t,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:t})=>`${e} ${(100*t).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:t.map((e,t)=>(0,i.jsx)(pq,{fill:g2[t%g2.length]},`cell-${t}`))}),(0,i.jsx)(g1,{content:(0,i.jsx)(({active:e,payload:t})=>{if(e&&t&&t.length){let e=t[0].payload;return(0,i.jsxs)("div",{className:"bg-white p-3 border rounded shadow",children:[(0,i.jsx)("p",{className:"font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-blue-600",children:["Value: ",(0,p.vv)(e.value)]}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Count: ",e.count]})]})}return null},{})})]})})})]})}function g3(){let[e,t]=(0,a.useState)(null),[r,n]=(0,a.useState)(!0);return r?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-64",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):e?(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Dashboard"}),(0,i.jsx)("p",{className:"text-gray-600 mt-2",children:"Overview of your financial portfolio"})]}),(0,i.jsx)(b,{totalAssets:e.totalAssets,totalLiabilities:e.totalLiabilities,totalReceivables:e.totalReceivables,overdueItems:e.overdueItems}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsx)(y,{totalAssets:e.totalAssets,totalLiabilities:e.totalLiabilities,totalReceivables:e.totalReceivables,netWorth:e.netWorth}),(0,i.jsx)(g5,{data:e.assetsByType})]}),(0,i.jsxs)(d.Card,{children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsx)(d.CardTitle,{children:"Quick Actions"})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4",children:[(0,i.jsxs)(f.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-blue-50 hover:border-blue-300 transition-all duration-200",onClick:()=>window.location.href="/transactions",children:[(0,i.jsx)(o.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-blue-600"}),(0,i.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Add Transaction"})]}),(0,i.jsxs)(f.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-green-50 hover:border-green-300 transition-all duration-200",onClick:()=>window.location.href="/budget",children:[(0,i.jsx)(l.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-green-600"}),(0,i.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Budget Planner"})]}),(0,i.jsxs)(f.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-purple-50 hover:border-purple-300 transition-all duration-200",onClick:()=>window.location.href="/assets",children:[(0,i.jsx)(u.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-purple-600"}),(0,i.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"Add Asset"})]}),(0,i.jsxs)(f.$,{variant:"outline",className:"h-16 lg:h-20 flex-col hover:bg-orange-50 hover:border-orange-300 transition-all duration-200",onClick:()=>window.location.href="/reports",children:[(0,i.jsx)(c.A,{className:"h-5 w-5 lg:h-6 lg:w-6 mb-1 lg:mb-2 text-orange-600"}),(0,i.jsx)("span",{className:"text-xs lg:text-sm font-medium",children:"View Reports"})]})]})})]}),e.recentTransactions&&e.recentTransactions.length>0&&(0,i.jsxs)(d.Card,{children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsxs)(d.CardTitle,{className:"flex items-center justify-between",children:["Recent Transactions",(0,i.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>window.location.href="/transactions",children:"View All"})]})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsx)("div",{className:"space-y-3",children:e.recentTransactions.slice(0,5).map(e=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:`p-2 rounded-full mr-3 ${"income"===e.type?"bg-green-100 text-green-600":"expense"===e.type?"bg-red-100 text-red-600":"bg-blue-100 text-blue-600"}`,children:"income"===e.type?(0,i.jsx)(s.A,{className:"h-4 w-4"}):"expense"===e.type?(0,i.jsx)(s.A,{className:"h-4 w-4 rotate-180"}):(0,i.jsx)(o.A,{className:"h-4 w-4"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium",children:e.description||"No description"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:e.categories?.name||"Uncategorized"})]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsxs)("p",{className:`font-semibold ${"income"===e.type?"text-green-600":"text-red-600"}`,children:["income"===e.type?"+":"-",new Intl.NumberFormat("en-US",{style:"currency",currency:e.currency||"LKR",currencyDisplay:"symbol"}).format(e.amount).replace("LKR","Rs.")]}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.transaction_date).toLocaleDateString()})]})]},e.id))})})]}),e.overdueItems.liabilities>0||e.overdueItems.receivables>0?(0,i.jsxs)(d.Card,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(d.CardHeader,{children:(0,i.jsx)(d.CardTitle,{className:"text-red-800",children:"⚠️ Attention Required"})}),(0,i.jsx)(d.CardContent,{children:(0,i.jsxs)("div",{className:"text-red-700 space-y-1",children:[e.overdueItems.liabilities>0&&(0,i.jsxs)("p",{children:["• ",e.overdueItems.liabilities," overdue liability(ies) need attention"]}),e.overdueItems.receivables>0&&(0,i.jsxs)("p",{children:["• ",e.overdueItems.receivables," overdue receivable(s) need follow-up"]})]})})]}):null]}):(0,i.jsx)("div",{className:"text-center py-8 text-red-600",children:"Error loading dashboard data. Please try again."})}},87509:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35314),i=r(76431),a=r(30657),o=r(43574);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,c=r,s=l;if(0===c.length)return s;let e=u;for(let t=0;t<c.length;t++){if(null==e||n.isUnsafeProperty(c[t]))return s;e=e[c[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},90015:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},90830:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(26349),i=r(49899);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},91428:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},91645:e=>{"use strict";e.exports=require("net")},91653:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},92292:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(69404),i=r(90015);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},92681:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},92867:(e,t,r)=>{e.exports=r(60324).isPlainObject},92923:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(91653),i=r(91428),a=r(27469),o=r(23457),l=r(21251);function u(e,t,r,n=new Map,s){let f=s?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=u(e[i],i,r,n,s);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,u(a,i,r,n,s));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(u(i,void 0,r,n,s));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=u(e[i],i,r,n,s);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),c(t,e,r,n,s),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),c(t,e,r,n,s),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),c(t,e,r,n,s),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,c(t,e,r,n,s),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),c(t,e,r,n,s),t}return e}function c(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],c=Object.getOwnPropertyDescriptor(e,l);(null==c||c.writable)&&(e[l]=u(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return u(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=u,t.copyProperties=c},94735:e=>{"use strict";e.exports=require("events")},95819:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},97668:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function g(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case f:case a:case l:case o:case h:return e;default:switch(e=e&&e.$$typeof){case c:case d:case y:case p:case u:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return g(e)===a}},98150:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145,79,814,435,235],()=>r(9489));module.exports=n})();