'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { createAsset, updateAsset, getAssetCategories } from '@/lib/api/assets'
import type { Asset, Category } from '@/lib/types/database'

interface AssetFormProps {
  asset?: Asset
  onSuccess: () => void
  onCancel: () => void
}

export default function AssetForm({ asset, onSuccess, onCancel }: AssetFormProps) {
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState({
    name: asset?.name || '',
    description: asset?.description || '',
    current_value: asset?.current_value || 0,
    purchase_value: asset?.purchase_value || 0,
    purchase_date: asset?.purchase_date || '',
    asset_type: asset?.asset_type || 'investment' as const,
    category_id: asset?.category_id || '',
    currency: asset?.currency || 'USD',
  })

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    const { data } = await getAssetCategories()
    if (data) setCategories(data)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (asset) {
        await updateAsset(asset.id, formData)
      } else {
        await createAsset(formData)
      }
      onSuccess()
    } catch (error) {
      console.error('Error saving asset:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{asset ? 'Edit Asset' : 'Add New Asset'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Asset Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="asset_type">Asset Type *</Label>
              <Select
                id="asset_type"
                value={formData.asset_type}
                onChange={(e) => handleChange('asset_type', e.target.value)}
                required
              >
                <option value="investment">Investment</option>
                <option value="real_estate">Real Estate</option>
                <option value="vehicle">Vehicle</option>
                <option value="cash">Cash</option>
                <option value="other">Other</option>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="current_value">Current Value *</Label>
              <Input
                id="current_value"
                type="number"
                step="0.01"
                value={formData.current_value}
                onChange={(e) => handleChange('current_value', parseFloat(e.target.value) || 0)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="purchase_value">Purchase Value</Label>
              <Input
                id="purchase_value"
                type="number"
                step="0.01"
                value={formData.purchase_value}
                onChange={(e) => handleChange('purchase_value', parseFloat(e.target.value) || 0)}
              />
            </div>
            
            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                id="currency"
                value={formData.currency}
                onChange={(e) => handleChange('currency', e.target.value)}
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="JPY">JPY</option>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="purchase_date">Purchase Date</Label>
              <Input
                id="purchase_date"
                type="date"
                value={formData.purchase_date}
                onChange={(e) => handleChange('purchase_date', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="category_id">Category</Label>
              <Select
                id="category_id"
                value={formData.category_id}
                onChange={(e) => handleChange('category_id', e.target.value)}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </Select>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : asset ? 'Update Asset' : 'Create Asset'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
