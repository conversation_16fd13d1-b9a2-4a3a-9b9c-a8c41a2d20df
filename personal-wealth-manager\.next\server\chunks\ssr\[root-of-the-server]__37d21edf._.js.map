{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/auth.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\n\nexport async function signUp(email: string, password: string, fullName: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        full_name: fullName,\n      },\n    },\n  })\n\n  return { data, error }\n}\n\nexport async function signIn(email: string, password: string) {\n  const supabase = createClient()\n  \n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  })\n\n  return { data, error }\n}\n\nexport async function signOut() {\n  const supabase = createClient()\n  \n  const { error } = await supabase.auth.signOut()\n  \n  return { error }\n}\n\nexport async function getUser() {\n  const supabase = createClient()\n\n  const { data: { user }, error } = await supabase.auth.getUser()\n\n  return { user, error }\n}\n\nexport async function getProfile() {\n  const supabase = createClient()\n\n  const { data: { user }, error: userError } = await supabase.auth.getUser()\n\n  if (userError || !user) {\n    return { profile: null, error: userError }\n  }\n\n  const { data: profile, error: profileError } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n\n  return { profile, error: profileError }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB,EAAE,QAAgB;IAC5E,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ,WAAW;YACb;QACF;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,OAAO,KAAa,EAAE,QAAgB;IAC1D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7C,OAAO;QAAE;IAAM;AACjB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7D,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAM,OAAO;QAAU;IAC3C;IAEA,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,OAAO;QAAE;QAAS,OAAO;IAAa;AACxC", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  return dateObj.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  })\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return 0\n  return ((current - previous) / previous) * 100\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { \n  LayoutDashboard, \n  TrendingUp, \n  CreditCard, \n  Users, \n  Settings,\n  LogOut\n} from 'lucide-react'\nimport { signOut } from '@/lib/auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Assets', href: '/assets', icon: TrendingUp },\n  { name: 'Liabilities', href: '/liabilities', icon: CreditCard },\n  { name: 'Receivables', href: '/receivables', icon: Users },\n  { name: 'Settings', href: '/settings', icon: Settings },\n]\n\nexport default function Sidebar() {\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n    window.location.href = '/login'\n  }\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white border-r border-gray-200\">\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n        <h1 className=\"text-xl font-bold text-gray-900\">Wealth Manager</h1>\n      </div>\n      \n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                isActive\n                  ? 'bg-blue-50 text-blue-700'\n                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'\n                )}\n              />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n      \n      <div className=\"border-t border-gray-200 p-3\">\n        <button\n          onClick={handleSignOut}\n          className=\"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors\"\n        >\n          <LogOut className=\"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\" />\n          Sign out\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAbA;;;;;;;AAeA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,kNAAA,CAAA,aAAU;IAAC;IACpD;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC9D;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;IACzD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACvD;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD;QACZ,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAkC;;;;;;;;;;;0BAGlD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,6BACA;;0CAGN,8OAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,kBAAkB;;;;;;4BAGhC,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAuE;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport type { User } from '@supabase/supabase-js'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n\n      if (!user) {\n        router.push('/login')\n      }\n    }\n\n    getUser()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN') {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;YAEX,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;QACF;QAEA;QAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,aAAa;gBAChC,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/lib/api/dashboard.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client'\n\nexport interface DashboardData {\n  totalAssets: number\n  totalLiabilities: number\n  totalReceivables: number\n  netWorth: number\n  assetsByType: { type: string; value: number; count: number }[]\n  liabilitiesByType: { type: string; value: number; count: number }[]\n  recentTransactions: any[]\n  overdueItems: {\n    liabilities: number\n    receivables: number\n  }\n}\n\nexport async function getDashboardData(): Promise<DashboardData> {\n  const supabase = createClient()\n\n  // Get assets\n  const { data: assets } = await supabase\n    .from('assets')\n    .select('current_value, asset_type')\n\n  // Get liabilities\n  const { data: liabilities } = await supabase\n    .from('liabilities')\n    .select('current_balance, liability_type, status, due_date')\n\n  // Get receivables\n  const { data: receivables } = await supabase\n    .from('receivables')\n    .select('current_balance, status, due_date')\n\n  // Calculate totals\n  const totalAssets = assets?.reduce((sum, asset) => sum + asset.current_value, 0) || 0\n  const totalLiabilities = liabilities?.filter(l => l.status === 'active')\n    .reduce((sum, liability) => sum + liability.current_balance, 0) || 0\n  const totalReceivables = receivables?.filter(r => r.status === 'active')\n    .reduce((sum, receivable) => sum + receivable.current_balance, 0) || 0\n\n  const netWorth = totalAssets + totalReceivables - totalLiabilities\n\n  // Group assets by type\n  const assetsByType = assets?.reduce((acc, asset) => {\n    const existing = acc.find(item => item.type === asset.asset_type)\n    if (existing) {\n      existing.value += asset.current_value\n      existing.count += 1\n    } else {\n      acc.push({\n        type: asset.asset_type,\n        value: asset.current_value,\n        count: 1\n      })\n    }\n    return acc\n  }, [] as { type: string; value: number; count: number }[]) || []\n\n  // Group liabilities by type\n  const liabilitiesByType = liabilities?.filter(l => l.status === 'active')\n    .reduce((acc, liability) => {\n      const existing = acc.find(item => item.type === liability.liability_type)\n      if (existing) {\n        existing.value += liability.current_balance\n        existing.count += 1\n      } else {\n        acc.push({\n          type: liability.liability_type,\n          value: liability.current_balance,\n          count: 1\n        })\n      }\n      return acc\n    }, [] as { type: string; value: number; count: number }[]) || []\n\n  // Count overdue items\n  const today = new Date()\n  const overdueLiabilities = liabilities?.filter(l => \n    l.status === 'active' && l.due_date && new Date(l.due_date) < today\n  ).length || 0\n\n  const overdueReceivables = receivables?.filter(r => \n    r.status === 'active' && r.due_date && new Date(r.due_date) < today\n  ).length || 0\n\n  return {\n    totalAssets,\n    totalLiabilities,\n    totalReceivables,\n    netWorth,\n    assetsByType,\n    liabilitiesByType,\n    recentTransactions: [], // TODO: Implement transaction history\n    overdueItems: {\n      liabilities: overdueLiabilities,\n      receivables: overdueReceivables\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAgBO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,aAAa;IACb,MAAM,EAAE,MAAM,MAAM,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,UACL,MAAM,CAAC;IAEV,kBAAkB;IAClB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,eACL,MAAM,CAAC;IAEV,kBAAkB;IAClB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,eACL,MAAM,CAAC;IAEV,mBAAmB;IACnB,MAAM,cAAc,QAAQ,OAAO,CAAC,KAAK,QAAU,MAAM,MAAM,aAAa,EAAE,MAAM;IACpF,MAAM,mBAAmB,aAAa,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAC5D,OAAO,CAAC,KAAK,YAAc,MAAM,UAAU,eAAe,EAAE,MAAM;IACrE,MAAM,mBAAmB,aAAa,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAC5D,OAAO,CAAC,KAAK,aAAe,MAAM,WAAW,eAAe,EAAE,MAAM;IAEvE,MAAM,WAAW,cAAc,mBAAmB;IAElD,uBAAuB;IACvB,MAAM,eAAe,QAAQ,OAAO,CAAC,KAAK;QACxC,MAAM,WAAW,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,MAAM,UAAU;QAChE,IAAI,UAAU;YACZ,SAAS,KAAK,IAAI,MAAM,aAAa;YACrC,SAAS,KAAK,IAAI;QACpB,OAAO;YACL,IAAI,IAAI,CAAC;gBACP,MAAM,MAAM,UAAU;gBACtB,OAAO,MAAM,aAAa;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG,EAAE,KAAyD,EAAE;IAEhE,4BAA4B;IAC5B,MAAM,oBAAoB,aAAa,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAC7D,OAAO,CAAC,KAAK;QACZ,MAAM,WAAW,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,UAAU,cAAc;QACxE,IAAI,UAAU;YACZ,SAAS,KAAK,IAAI,UAAU,eAAe;YAC3C,SAAS,KAAK,IAAI;QACpB,OAAO;YACL,IAAI,IAAI,CAAC;gBACP,MAAM,UAAU,cAAc;gBAC9B,OAAO,UAAU,eAAe;gBAChC,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG,EAAE,KAAyD,EAAE;IAElE,sBAAsB;IACtB,MAAM,QAAQ,IAAI;IAClB,MAAM,qBAAqB,aAAa,OAAO,CAAA,IAC7C,EAAE,MAAM,KAAK,YAAY,EAAE,QAAQ,IAAI,IAAI,KAAK,EAAE,QAAQ,IAAI,OAC9D,UAAU;IAEZ,MAAM,qBAAqB,aAAa,OAAO,CAAA,IAC7C,EAAE,MAAM,KAAK,YAAY,EAAE,QAAQ,IAAI,IAAI,KAAK,EAAE,QAAQ,IAAI,OAC9D,UAAU;IAEZ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,oBAAoB,EAAE;QACtB,cAAc;YACZ,aAAa;YACb,aAAa;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg border bg-white shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-gray-600', className)}\n      {...props}\n    />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/dashboard/NetWorthCard.tsx"], "sourcesContent": ["import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { TrendingUp, TrendingDown } from 'lucide-react'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface NetWorthCardProps {\n  totalAssets: number\n  totalLiabilities: number\n  totalReceivables: number\n  netWorth: number\n}\n\nexport default function NetWorthCard({ \n  totalAssets, \n  totalLiabilities, \n  totalReceivables, \n  netWorth \n}: NetWorthCardProps) {\n  const isPositive = netWorth >= 0\n\n  return (\n    <Card className=\"col-span-2\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          Net Worth\n          {isPositive ? (\n            <TrendingUp className=\"ml-2 h-5 w-5 text-green-500\" />\n          ) : (\n            <TrendingDown className=\"ml-2 h-5 w-5 text-red-500\" />\n          )}\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-4\">\n          <div className=\"text-3xl font-bold\">\n            <span className={isPositive ? 'text-green-600' : 'text-red-600'}>\n              {formatCurrency(netWorth)}\n            </span>\n          </div>\n          \n          <div className=\"grid grid-cols-3 gap-4 text-sm\">\n            <div className=\"text-center\">\n              <div className=\"text-gray-500\">Assets</div>\n              <div className=\"font-semibold text-green-600\">\n                {formatCurrency(totalAssets)}\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-gray-500\">Receivables</div>\n              <div className=\"font-semibold text-blue-600\">\n                {formatCurrency(totalReceivables)}\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-gray-500\">Liabilities</div>\n              <div className=\"font-semibold text-red-600\">\n                {formatCurrency(totalLiabilities)}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"text-xs text-gray-500 text-center\">\n            Net Worth = Assets + Receivables - Liabilities\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;AASe,SAAS,aAAa,EACnC,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACU;IAClB,MAAM,aAAa,YAAY;IAE/B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAoB;wBAEtC,2BACC,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;iDAEtB,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI9B,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAW,aAAa,mBAAmB;0CAC9C,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8CAIpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8CAIpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;sCAKtB,8OAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/dashboard/QuickStats.tsx"], "sourcesContent": ["import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { AlertTriangle, TrendingUp, CreditCard, Users } from 'lucide-react'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface QuickStatsProps {\n  totalAssets: number\n  totalLiabilities: number\n  totalReceivables: number\n  overdueItems: {\n    liabilities: number\n    receivables: number\n  }\n}\n\nexport default function QuickStats({ \n  totalAssets, \n  totalLiabilities, \n  totalReceivables, \n  overdueItems \n}: QuickStatsProps) {\n  const stats = [\n    {\n      title: 'Total Assets',\n      value: formatCurrency(totalAssets),\n      icon: TrendingUp,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50'\n    },\n    {\n      title: 'Total Liabilities',\n      value: formatCurrency(totalLiabilities),\n      icon: CreditCard,\n      color: 'text-red-600',\n      bgColor: 'bg-red-50'\n    },\n    {\n      title: 'Total Receivables',\n      value: formatCurrency(totalReceivables),\n      icon: Users,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50'\n    },\n    {\n      title: 'Overdue Items',\n      value: `${overdueItems.liabilities + overdueItems.receivables}`,\n      icon: AlertTriangle,\n      color: overdueItems.liabilities + overdueItems.receivables > 0 ? 'text-red-600' : 'text-gray-600',\n      bgColor: overdueItems.liabilities + overdueItems.receivables > 0 ? 'bg-red-50' : 'bg-gray-50'\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n      {stats.map((stat, index) => (\n        <Card key={index}>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className={`p-2 rounded-lg ${stat.bgColor}`}>\n                <stat.icon className={`h-6 w-6 ${stat.color}`} />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAYe,SAAS,WAAW,EACjC,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACI;IAChB,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YACtB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YACtB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YACtB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,GAAG,aAAa,WAAW,GAAG,aAAa,WAAW,EAAE;YAC/D,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO,aAAa,WAAW,GAAG,aAAa,WAAW,GAAG,IAAI,iBAAiB;YAClF,SAAS,aAAa,WAAW,GAAG,aAAa,WAAW,GAAG,IAAI,cAAc;QACnF;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,eAAe,EAAE,KAAK,OAAO,EAAE;0CAC9C,cAAA,8OAAC,KAAK,IAAI;oCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqC,KAAK,KAAK;;;;;;kDAC5D,8OAAC;wCAAE,WAAW,CAAC,mBAAmB,EAAE,KAAK,KAAK,EAAE;kDAAG,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;eAR1D;;;;;;;;;;AAgBnB", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/dashboard/AssetChart.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface AssetChartProps {\n  data: { type: string; value: number; count: number }[]\n}\n\nconst COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']\n\nexport default function AssetChart({ data }: AssetChartProps) {\n  const chartData = data.map(item => ({\n    name: item.type.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n    value: item.value,\n    count: item.count\n  }))\n\n  const CustomTooltip = ({ active, payload }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload\n      return (\n        <div className=\"bg-white p-3 border rounded shadow\">\n          <p className=\"font-medium\">{data.name}</p>\n          <p className=\"text-blue-600\">Value: {formatCurrency(data.value)}</p>\n          <p className=\"text-gray-600\">Count: {data.count}</p>\n        </div>\n      )\n    }\n    return null\n  }\n\n  if (data.length === 0) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle>Assets by Type</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center justify-center h-64 text-gray-500\">\n            No assets to display\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle>Assets by Type</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <ResponsiveContainer width=\"100%\" height={300}>\n          <PieChart>\n            <Pie\n              data={chartData}\n              cx=\"50%\"\n              cy=\"50%\"\n              labelLine={false}\n              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n              outerRadius={80}\n              fill=\"#8884d8\"\n              dataKey=\"value\"\n            >\n              {chartData.map((entry, index) => (\n                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n              ))}\n            </Pie>\n            <Tooltip content={<CustomTooltip />} />\n          </PieChart>\n        </ResponsiveContainer>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAUA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU;AAElE,SAAS,WAAW,EAAE,IAAI,EAAmB;IAC1D,MAAM,YAAY,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;YAClC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;YACrE,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,KAAK;QACnB,CAAC;IAED,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;QAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe,KAAK,IAAI;;;;;;kCACrC,8OAAC;wBAAE,WAAU;;4BAAgB;4BAAQ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK;;;;;;;kCAC9D,8OAAC;wBAAE,WAAU;;4BAAgB;4BAAQ,KAAK,KAAK;;;;;;;;;;;;;QAGrD;QACA,OAAO;IACT;IAEA,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;;;;;;8BAEb,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;kCAAsD;;;;;;;;;;;;;;;;;IAM7E;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8BAAC;;;;;;;;;;;0BAEb,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;0CACP,8OAAC,+IAAA,CAAA,MAAG;gCACF,MAAM;gCACN,IAAG;gCACH,IAAG;gCACH,WAAW;gCACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gCACtE,aAAa;gCACb,MAAK;gCACL,SAAQ;0CAEP,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;wCAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;uCAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0CAG9B,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Personal_Wealth_Manager/personal-wealth-manager/src/components/dashboard/Dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { getDashboardData, type DashboardData } from '@/lib/api/dashboard'\nimport NetWorthCard from './NetWorthCard'\nimport QuickStats from './QuickStats'\nimport Asset<PERSON>hart from './AssetChart'\n\nexport default function Dashboard() {\n  const [data, setData] = useState<DashboardData | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    loadDashboardData()\n  }, [])\n\n  const loadDashboardData = async () => {\n    try {\n      const dashboardData = await getDashboardData()\n      setData(dashboardData)\n    } catch (error) {\n      console.error('Error loading dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!data) {\n    return (\n      <div className=\"text-center py-8 text-red-600\">\n        Error loading dashboard data. Please try again.\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"text-gray-600\">Overview of your financial portfolio</p>\n      </div>\n\n      <QuickStats\n        totalAssets={data.totalAssets}\n        totalLiabilities={data.totalLiabilities}\n        totalReceivables={data.totalReceivables}\n        overdueItems={data.overdueItems}\n      />\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <NetWorthCard\n          totalAssets={data.totalAssets}\n          totalLiabilities={data.totalLiabilities}\n          totalReceivables={data.totalReceivables}\n          netWorth={data.netWorth}\n        />\n        \n        <AssetChart data={data.assetsByType} />\n      </div>\n\n      {data.overdueItems.liabilities > 0 || data.overdueItems.receivables > 0 ? (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-red-800 mb-2\">Attention Required</h3>\n          <div className=\"text-red-700\">\n            {data.overdueItems.liabilities > 0 && (\n              <p>• {data.overdueItems.liabilities} overdue liability(ies)</p>\n            )}\n            {data.overdueItems.receivables > 0 && (\n              <p>• {data.overdueItems.receivables} overdue receivable(s)</p>\n            )}\n          </div>\n        </div>\n      ) : null}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD;YAC3C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBAAgC;;;;;;IAInD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,8OAAC,6IAAA,CAAA,UAAU;gBACT,aAAa,KAAK,WAAW;gBAC7B,kBAAkB,KAAK,gBAAgB;gBACvC,kBAAkB,KAAK,gBAAgB;gBACvC,cAAc,KAAK,YAAY;;;;;;0BAGjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,+IAAA,CAAA,UAAY;wBACX,aAAa,KAAK,WAAW;wBAC7B,kBAAkB,KAAK,gBAAgB;wBACvC,kBAAkB,KAAK,gBAAgB;wBACvC,UAAU,KAAK,QAAQ;;;;;;kCAGzB,8OAAC,6IAAA,CAAA,UAAU;wBAAC,MAAM,KAAK,YAAY;;;;;;;;;;;;YAGpC,KAAK,YAAY,CAAC,WAAW,GAAG,KAAK,KAAK,YAAY,CAAC,WAAW,GAAG,kBACpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,YAAY,CAAC,WAAW,GAAG,mBAC/B,8OAAC;;oCAAE;oCAAG,KAAK,YAAY,CAAC,WAAW;oCAAC;;;;;;;4BAErC,KAAK,YAAY,CAAC,WAAW,GAAG,mBAC/B,8OAAC;;oCAAE;oCAAG,KAAK,YAAY,CAAC,WAAW;oCAAC;;;;;;;;;;;;;;;;;;uBAIxC;;;;;;;AAGV", "debugId": null}}]}