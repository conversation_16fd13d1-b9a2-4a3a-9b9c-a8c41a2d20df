import { createClient } from '@/lib/supabase/client'
import type { Asset, Database } from '@/lib/types/database'

type AssetInsert = Database['public']['Tables']['assets']['Insert']
type AssetUpdate = Database['public']['Tables']['assets']['Update']

export async function getAssets() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('assets')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .order('created_at', { ascending: false })

  return { data, error }
}

export async function getAsset(id: string) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('assets')
    .select(`
      *,
      categories (
        id,
        name,
        color
      )
    `)
    .eq('id', id)
    .single()

  return { data, error }
}

export async function createAsset(asset: AssetInsert) {
  const supabase = createClient()

  const { data: { user }, error: authError } = await supabase.auth.getUser()
  if (authError || !user) {
    return { data: null, error: authError || new Error('User not authenticated') }
  }

  const { data, error } = await supabase
    .from('assets')
    .insert({
      ...asset,
      user_id: user.id,
    })
    .select()
    .single()

  return { data, error }
}

export async function updateAsset(id: string, updates: AssetUpdate) {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('assets')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export async function deleteAsset(id: string) {
  const supabase = createClient()
  
  const { error } = await supabase
    .from('assets')
    .delete()
    .eq('id', id)

  return { error }
}

export async function getAssetCategories() {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('type', 'asset')
    .order('name')

  return { data, error }
}
