# Personal Wealth Manager

A comprehensive web application for managing personal finances, built with Next.js, TypeScript, and Supabase.

## Features

### 🏦 Asset Management
- Track investments, real estate, vehicles, cash, and other assets
- Monitor current values and purchase history
- Categorize assets for better organization
- Search and filter capabilities

### 💳 Liability Tracking
- Manage loans, credit cards, mortgages, and other debts
- Track principal amounts, current balances, and interest rates
- Monitor due dates with overdue alerts
- Status tracking (active, paid off, defaulted)

### 💰 Receivables Management
- Track money owed to you by others
- Store debtor contact information
- Monitor due dates and overdue amounts
- Interest rate calculations

### 📊 Dashboard & Analytics
- Real-time net worth calculation
- Visual charts and graphs using Recharts
- Quick stats overview
- Overdue items alerts

### 🔍 Search & Filtering
- Advanced search across all financial records
- Multiple filter options (type, status, date ranges, amounts)
- Real-time filtering with instant results

### 🔐 Security & Authentication
- Secure user authentication with Supabase Auth
- Row Level Security (RLS) policies
- Protected routes and middleware
- User data isolation

### 📱 Responsive Design
- Mobile-first responsive design
- Clean, minimalist UI with light theme
- Tailwind CSS for styling
- Optimized for all device sizes

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Styling**: Tailwind CSS
- **Charts**: Recharts
- **Icons**: Lucide React
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd personal-wealth-manager
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Update `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Database Schema

The application uses the following main tables:

- **profiles**: User profile information
- **categories**: Custom categories for organizing financial items
- **assets**: Asset tracking (investments, real estate, etc.)
- **liabilities**: Debt and loan tracking
- **receivables**: Money owed to the user

All tables include Row Level Security policies to ensure data privacy.

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── assets/            # Asset management pages
│   ├── dashboard/         # Dashboard page
│   ├── liabilities/       # Liability management pages
│   ├── login/             # Authentication pages
│   ├── receivables/       # Receivables management pages
│   ├── settings/          # Settings page
│   └── signup/            # User registration
├── components/            # React components
│   ├── assets/           # Asset-related components
│   ├── auth/             # Authentication components
│   ├── dashboard/        # Dashboard components
│   ├── layout/           # Layout components
│   ├── liabilities/      # Liability components
│   ├── receivables/      # Receivables components
│   └── ui/               # Reusable UI components
└── lib/                  # Utility functions and configurations
    ├── api/              # API functions
    ├── supabase/         # Supabase client configuration
    ├── types/            # TypeScript type definitions
    └── utils.ts          # Utility functions
```

## Key Features Implementation

### Net Worth Calculation
```typescript
netWorth = totalAssets + totalReceivables - totalLiabilities
```

### Security Implementation
- Row Level Security (RLS) policies ensure users can only access their own data
- Authentication middleware protects all routes
- Server-side and client-side Supabase clients for optimal security

### Search & Filtering
- Real-time search across all financial records
- Advanced filtering by type, status, date ranges, and amounts
- Optimized with React useMemo for performance

## Application Screenshots

The application features:
- Clean, responsive dashboard with net worth overview
- Comprehensive asset management with search and filtering
- Liability tracking with overdue alerts
- Receivables management with debtor information
- Secure authentication system

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the GitHub repository.
