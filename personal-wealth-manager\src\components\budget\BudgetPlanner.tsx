'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Target, TrendingUp, AlertCircle, CheckCircle, Plus } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { formatCurrency } from '@/lib/utils'

interface Budget {
  id: string
  category: string
  budgeted_amount: number
  spent_amount: number
  period: 'monthly' | 'weekly' | 'yearly'
  currency: string
}

interface BudgetGoal {
  id: string
  name: string
  target_amount: number
  current_amount: number
  target_date: string
  currency: string
}

export default function BudgetPlanner() {
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [goals, setGoals] = useState<BudgetGoal[]>([])
  const [showBudgetForm, setShowBudgetForm] = useState(false)
  const [showGoalForm, setShowGoalForm] = useState(false)
  const [loading, setLoading] = useState(true)

  const [budgetForm, setBudgetForm] = useState({
    category: '',
    budgeted_amount: 0,
    period: 'monthly' as const,
    currency: 'LKR'
  })

  const [goalForm, setGoalForm] = useState({
    name: '',
    target_amount: 0,
    target_date: '',
    currency: 'LKR'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setLoading(true)
    // Mock data for now - replace with actual API calls
    setBudgets([
      {
        id: '1',
        category: 'Food & Dining',
        budgeted_amount: 50000,
        spent_amount: 32000,
        period: 'monthly',
        currency: 'LKR'
      },
      {
        id: '2',
        category: 'Transportation',
        budgeted_amount: 25000,
        spent_amount: 28000,
        period: 'monthly',
        currency: 'LKR'
      }
    ])

    setGoals([
      {
        id: '1',
        name: 'Emergency Fund',
        target_amount: 500000,
        current_amount: 150000,
        target_date: '2024-12-31',
        currency: 'LKR'
      },
      {
        id: '2',
        name: 'Vacation Fund',
        target_amount: 200000,
        current_amount: 75000,
        target_date: '2024-08-15',
        currency: 'LKR'
      }
    ])
    setLoading(false)
  }

  const getBudgetStatus = (budget: Budget) => {
    const percentage = (budget.spent_amount / budget.budgeted_amount) * 100
    if (percentage > 100) return { status: 'over', color: 'text-red-600 bg-red-50', icon: AlertCircle }
    if (percentage > 80) return { status: 'warning', color: 'text-yellow-600 bg-yellow-50', icon: AlertCircle }
    return { status: 'good', color: 'text-green-600 bg-green-50', icon: CheckCircle }
  }

  const getGoalProgress = (goal: BudgetGoal) => {
    return (goal.current_amount / goal.target_amount) * 100
  }

  const handleBudgetSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Add budget logic here
    setShowBudgetForm(false)
    setBudgetForm({ category: '', budgeted_amount: 0, period: 'monthly', currency: 'LKR' })
  }

  const handleGoalSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Add goal logic here
    setShowGoalForm(false)
    setGoalForm({ name: '', target_amount: 0, target_date: '', currency: 'LKR' })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-300 rounded"></div>
            <div className="h-64 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Budget Planner
          </h1>
          <p className="text-gray-600 mt-2">Track your spending and achieve your financial goals</p>
        </div>
        <div className="flex space-x-3">
          <Button onClick={() => setShowBudgetForm(true)} variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Budget
          </Button>
          <Button onClick={() => setShowGoalForm(true)} variant="gradient">
            <Target className="h-4 w-4 mr-2" />
            Add Goal
          </Button>
        </div>
      </div>

      {/* Budget Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Budgets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {budgets.map((budget) => {
                const status = getBudgetStatus(budget)
                const percentage = Math.min((budget.spent_amount / budget.budgeted_amount) * 100, 100)
                const StatusIcon = status.icon

                return (
                  <motion.div
                    key={budget.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 border rounded-xl hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{budget.category}</h3>
                      <div className={`flex items-center px-2 py-1 rounded-full text-xs ${status.color}`}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {status.status}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Spent: {formatCurrency(budget.spent_amount, budget.currency)}</span>
                        <span>Budget: {formatCurrency(budget.budgeted_amount, budget.currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            percentage > 100 ? 'bg-red-500' : percentage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500">
                        {percentage.toFixed(1)}% used
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Financial Goals */}
        <Card>
          <CardHeader>
            <CardTitle>Financial Goals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {goals.map((goal) => {
                const progress = getGoalProgress(goal)
                const daysLeft = Math.ceil((new Date(goal.target_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

                return (
                  <motion.div
                    key={goal.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 border rounded-xl hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{goal.name}</h3>
                      <div className="flex items-center text-blue-600 text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {daysLeft > 0 ? `${daysLeft} days left` : 'Overdue'}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Saved: {formatCurrency(goal.current_amount, goal.currency)}</span>
                        <span>Target: {formatCurrency(goal.target_amount, goal.currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(progress, 100)}%` }}
                        />
                      </div>
                      <div className="text-xs text-gray-500">
                        {progress.toFixed(1)}% complete
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Form Modal */}
      {showBudgetForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Add New Budget</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleBudgetSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={budgetForm.category}
                    onChange={(e) => setBudgetForm(prev => ({ ...prev, category: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="amount">Budget Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={budgetForm.budgeted_amount}
                    onChange={(e) => setBudgetForm(prev => ({ ...prev, budgeted_amount: parseFloat(e.target.value) || 0 }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="period">Period</Label>
                  <Select
                    id="period"
                    value={budgetForm.period}
                    onChange={(e) => setBudgetForm(prev => ({ ...prev, period: e.target.value as any }))}
                  >
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="yearly">Yearly</option>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    id="currency"
                    value={budgetForm.currency}
                    onChange={(e) => setBudgetForm(prev => ({ ...prev, currency: e.target.value }))}
                  >
                    <option value="LKR">LKR (Sri Lankan Rupee)</option>
                    <option value="USD">USD (US Dollar)</option>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowBudgetForm(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Budget</Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Goal Form Modal */}
      {showGoalForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Add Financial Goal</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleGoalSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="goalName">Goal Name</Label>
                  <Input
                    id="goalName"
                    value={goalForm.name}
                    onChange={(e) => setGoalForm(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="targetAmount">Target Amount</Label>
                  <Input
                    id="targetAmount"
                    type="number"
                    step="0.01"
                    value={goalForm.target_amount}
                    onChange={(e) => setGoalForm(prev => ({ ...prev, target_amount: parseFloat(e.target.value) || 0 }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="targetDate">Target Date</Label>
                  <Input
                    id="targetDate"
                    type="date"
                    value={goalForm.target_date}
                    onChange={(e) => setGoalForm(prev => ({ ...prev, target_date: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="goalCurrency">Currency</Label>
                  <Select
                    id="goalCurrency"
                    value={goalForm.currency}
                    onChange={(e) => setGoalForm(prev => ({ ...prev, currency: e.target.value }))}
                  >
                    <option value="LKR">LKR (Sri Lankan Rupee)</option>
                    <option value="USD">USD (US Dollar)</option>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowGoalForm(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">Add Goal</Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
