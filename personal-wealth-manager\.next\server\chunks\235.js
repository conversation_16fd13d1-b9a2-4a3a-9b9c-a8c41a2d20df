exports.id=235,exports.ids=[235],exports.modules={2643:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(60687),s=a(43210),i=a(26001),n=a(4780);let l=(0,s.forwardRef)(({className:e,variant:t="primary",size:a="md",loading:s=!1,children:l,...o},c)=>(0,r.jsxs)(i.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:(0,n.cn)("inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm hover:shadow-md active:scale-95",{primary:"bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-blue-200",secondary:"bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300",outline:"border-2 border-blue-600 text-blue-600 bg-transparent hover:bg-blue-50",ghost:"text-gray-600 hover:bg-gray-100 hover:text-gray-900",destructive:"bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-red-200",gradient:"bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 text-white hover:from-purple-700 hover:via-blue-700 hover:to-teal-700"}[t],{sm:"h-9 px-4 text-sm",md:"h-11 px-6 py-2.5",lg:"h-13 px-8 text-lg"}[a],e),ref:c,disabled:s,...o,children:[s&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]}));l.displayName="Button"},4780:(e,t,a)=>{"use strict";a.d(t,{Yq:()=>l,cn:()=>i,vv:()=>n});var r=a(49384),s=a(82348);function i(...e){return(0,s.QP)((0,r.$)(e))}function n(e,t="LKR"){return"LKR"===t?new Intl.NumberFormat("en-US",{style:"currency",currency:"LKR",currencyDisplay:"symbol",minimumFractionDigits:2,maximumFractionDigits:2}).format(e).replace("LKR","Rs."):new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}},24949:()=>{},28749:(e,t,a)=>{"use strict";a.d(t,{Card:()=>l,CardContent:()=>d,CardHeader:()=>o,CardTitle:()=>c});var r=a(60687),s=a(43210),i=a(26001),n=a(4780);let l=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},ref:a,className:(0,n.cn)("rounded-2xl border border-gray-100 bg-white shadow-lg shadow-gray-100/50 backdrop-blur-sm hover:shadow-xl hover:shadow-gray-200/50 transition-all duration-300",e),...t}));l.displayName="Card";let o=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-2 p-8 pb-4",e),...t}));o.displayName="CardHeader";let c=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,n.cn)("text-xl font-bold leading-none tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",e),...t}));c.displayName="CardTitle",(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-gray-500 leading-relaxed",e),...t})).displayName="CardDescription";let d=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("px-8 pb-8",e),...t}));d.displayName="CardContent",(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center px-8 pb-8 pt-0",e),...t})).displayName="CardFooter"},39727:()=>{},42308:(e,t,a)=>{"use strict";a.d(t,{K7:()=>l,bW:()=>s,st:()=>n,zZ:()=>i});var r=a(79481);async function s(e){let t=(0,r.U)().from("categories").select("*").order("name");e&&(t=t.eq("type",e));let{data:a,error:s}=await t;return{data:a,error:s}}async function i(e){let t=(0,r.U)(),{data:{user:a}}=await t.auth.getUser();if(!a)throw Error("User not authenticated");let{data:s,error:i}=await t.from("categories").insert({...e,user_id:a.id}).select().single();return{data:s,error:i}}async function n(e,t){let a=(0,r.U)(),{data:s,error:i}=await a.from("categories").update(t).eq("id",e).select().single();return{data:s,error:i}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("categories").delete().eq("id",e);return{error:a}}},43949:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(60687),s=a(43210),i=a(4780);let n=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("label",{ref:a,className:(0,i.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...t}));n.displayName="Label"},45225:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(60687),s=a(43210),i=a(4780);let n=(0,s.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Textarea"},46655:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\layout\\DashboardLayout.tsx","default")},47990:()=>{},51907:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(60687),s=a(43210),i=a(26001),n=a(4780);let l=(0,s.forwardRef)(({className:e,type:t,...a},s)=>(0,r.jsx)(i.P.input,{whileFocus:{scale:1.02},type:t,className:(0,n.cn)("flex h-12 w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-sm font-medium ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md",e),ref:s,...a}));l.displayName="Input"},61135:()=>{},63043:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},63087:(e,t,a)=>{"use strict";a.d(t,{default:()=>J});var r=a(60687),s=a(43210),i=a(85814),n=a.n(i),l=a(16189),o=a(49625),c=a(23576),d=a(28947),u=a(53411),m=a(25541),h=a(85778),x=a(41312),p=a(37360),b=a(84027),f=a(40083),g=a(63523),y=a(4780);let v=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Transactions",href:"/transactions",icon:c.A},{name:"Budget",href:"/budget",icon:d.A},{name:"Reports",href:"/reports",icon:u.A},{name:"Assets",href:"/assets",icon:m.A},{name:"Liabilities",href:"/liabilities",icon:h.A},{name:"Receivables",href:"/receivables",icon:x.A},{name:"Categories",href:"/categories",icon:p.A},{name:"Settings",href:"/settings",icon:b.A}];function w(){let e=(0,l.usePathname)(),t=async()=>{await (0,g.CI)(),window.location.href="/login"};return(0,r.jsxs)("div",{className:"flex h-full w-64 flex-col bg-white/80 backdrop-blur-xl border-r border-gray-200/50 shadow-xl",children:[(0,r.jsx)("div",{className:"flex h-20 items-center px-6 border-b border-gray-200/50",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"W"})}),(0,r.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]})}),(0,r.jsx)("nav",{className:"flex-1 space-y-2 px-4 py-6",children:v.map(t=>{let a=e===t.href;return(0,r.jsxs)(n(),{href:t.href,className:(0,y.cn)("group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 hover:scale-105 active:scale-95",a?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25":"text-gray-700 hover:bg-white/60 hover:text-gray-900 hover:shadow-md"),children:[(0,r.jsx)(t.icon,{className:(0,y.cn)("mr-3 h-5 w-5 flex-shrink-0",a?"text-white":"text-gray-500 group-hover:text-gray-700")}),t.name]},t.name)})}),(0,r.jsx)("div",{className:"border-t border-gray-200 p-3",children:(0,r.jsxs)("button",{onClick:t,className:"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(f.A,{className:"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500"}),"Sign out"]})})]})}var j=a(13516),N=a(18908),C=a(11860);let A=[{name:"Dashboard",href:"/dashboard",icon:o.A},{name:"Transactions",href:"/transactions",icon:c.A},{name:"Budget",href:"/budget",icon:d.A},{name:"Reports",href:"/reports",icon:u.A},{name:"Assets",href:"/assets",icon:m.A},{name:"Liabilities",href:"/liabilities",icon:h.A},{name:"Receivables",href:"/receivables",icon:x.A},{name:"Categories",href:"/categories",icon:p.A},{name:"Settings",href:"/settings",icon:b.A}];function U({isOpen:e,onClose:t}){let a=(0,l.usePathname)(),i=async()=>{await (0,g.CI)(),window.location.href="/login"};return(0,r.jsx)(j.e.Root,{show:e,as:s.Fragment,children:(0,r.jsxs)(N.lG,{as:"div",className:"relative z-50 lg:hidden",onClose:t,children:[(0,r.jsx)(j.e.Child,{as:s.Fragment,enter:"transition-opacity ease-linear duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition-opacity ease-linear duration-300",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-900/80"})}),(0,r.jsx)("div",{className:"fixed inset-0 flex",children:(0,r.jsx)(j.e.Child,{as:s.Fragment,enter:"transition ease-in-out duration-300 transform",enterFrom:"-translate-x-full",enterTo:"translate-x-0",leave:"transition ease-in-out duration-300 transform",leaveFrom:"translate-x-0",leaveTo:"-translate-x-full",children:(0,r.jsx)(N.lG.Panel,{className:"relative mr-16 flex w-full max-w-xs flex-1",children:(0,r.jsxs)("div",{className:"flex grow flex-col gap-y-5 overflow-y-auto bg-white/95 backdrop-blur-xl px-6 pb-4 shadow-xl",children:[(0,r.jsxs)("div",{className:"flex h-16 shrink-0 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,r.jsx)("h1",{className:"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]}),(0,r.jsx)("button",{type:"button",className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",onClick:t,children:(0,r.jsx)(C.A,{className:"h-6 w-6"})})]}),(0,r.jsx)("nav",{className:"flex flex-1 flex-col",children:(0,r.jsxs)("ul",{role:"list",className:"flex flex-1 flex-col gap-y-7",children:[(0,r.jsx)("li",{children:(0,r.jsx)("ul",{role:"list",className:"-mx-2 space-y-1",children:A.map(e=>{let s=a===e.href;return(0,r.jsx)("li",{children:(0,r.jsxs)(n(),{href:e.href,onClick:t,className:(0,y.cn)("group flex gap-x-3 rounded-md p-3 text-sm leading-6 font-semibold transition-all duration-200",s?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg":"text-gray-700 hover:text-gray-900 hover:bg-gray-50"),children:[(0,r.jsx)(e.icon,{className:(0,y.cn)("h-6 w-6 shrink-0",s?"text-white":"text-gray-400 group-hover:text-gray-600")}),e.name]})},e.name)})})}),(0,r.jsx)("li",{className:"mt-auto",children:(0,r.jsxs)("button",{onClick:i,className:"group -mx-2 flex w-full gap-x-3 rounded-md p-3 text-sm font-semibold leading-6 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(f.A,{className:"h-6 w-6 shrink-0 text-gray-400 group-hover:text-gray-600"}),"Sign out"]})})]})})]})})})})]})})}var R=a(79481);function k({children:e}){let[t,a]=(0,s.useState)(null),[i,n]=(0,s.useState)(!0);return((0,l.useRouter)(),(0,R.U)(),i)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):t?(0,r.jsx)(r.Fragment,{children:e}):null}let _=function({children:e,variant:t="default"}){return(0,r.jsxs)("div",{className:`min-h-screen ${{default:"bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",dashboard:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",auth:"bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800"}[t]} relative overflow-hidden`,children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-teal-400/20 to-blue-400/20 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-3xl"})]}),(0,r.jsx)("div",{className:"relative z-10",children:e})]})};var L=a(88920),P=a(26001),S=a(96474),I=a(5748),F=a(66594),T=a(67413);function D(){let[e,t]=(0,s.useState)(!1),[a,i]=(0,s.useState)(!1),[n,l]=(0,s.useState)("expense"),o=[{type:"income",icon:S.A,label:"Income",color:"bg-green-500 hover:bg-green-600"},{type:"expense",icon:I.A,label:"Expense",color:"bg-red-500 hover:bg-red-600"},{type:"transfer",icon:F.A,label:"Transfer",color:"bg-blue-500 hover:bg-blue-600"}],c=e=>{l(e),i(!0),t(!1)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"fixed bottom-6 right-6 z-40",children:[(0,r.jsx)(L.N,{children:e&&(0,r.jsx)(P.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"absolute bottom-16 right-0 space-y-3",children:o.map((e,t)=>(0,r.jsxs)(P.P.button,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{delay:.1*t},onClick:()=>c(e.type),className:`
                    flex items-center space-x-3 px-4 py-3 rounded-xl text-white font-medium
                    shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105
                    ${e.color}
                  `,children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"whitespace-nowrap",children:e.label})]},e.type))})}),(0,r.jsx)(P.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>t(!e),className:`
            w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200
            flex items-center justify-center text-white font-bold text-xl
            ${e?"bg-gray-500 hover:bg-gray-600":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"}
          `,children:(0,r.jsx)(P.P.div,{animate:{rotate:45*!!e},transition:{duration:.2},children:e?(0,r.jsx)(C.A,{className:"h-6 w-6"}):(0,r.jsx)(S.A,{className:"h-6 w-6"})})})]}),(0,r.jsx)(L.N,{children:a&&(0,r.jsx)(T.A,{defaultType:n,onSuccess:()=>{i(!1),window.location.reload()},onCancel:()=>i(!1)})})]})}var $=a(12941);function J({children:e}){let[t,a]=(0,s.useState)(!1);return(0,r.jsx)(k,{children:(0,r.jsx)(_,{variant:"dashboard",children:(0,r.jsxs)("div",{className:"flex h-screen",children:[(0,r.jsx)("div",{className:"hidden lg:block",children:(0,r.jsx)(w,{})}),(0,r.jsx)(U,{isOpen:t,onClose:()=>a(!1)}),(0,r.jsxs)("main",{className:"flex-1 overflow-auto",children:[(0,r.jsxs)("div",{className:"lg:hidden bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 py-3 flex items-center justify-between",children:[(0,r.jsx)("button",{onClick:()=>a(!0),className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors",children:(0,r.jsx)($.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"W"})}),(0,r.jsx)("h1",{className:"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Wealth Manager"})]})]}),(0,r.jsx)("div",{className:"p-4 sm:p-6 lg:p-8",children:e})]}),(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)(D,{})})]})})})}},63523:(e,t,a)=>{"use strict";a.d(t,{CI:()=>n,Hh:()=>s,Jv:()=>i});var r=a(79481);async function s(e,t,a){let s=(0,r.U)(),{data:i,error:n}=await s.auth.signUp({email:e,password:t,options:{data:{full_name:a}}});return{data:i,error:n}}async function i(e,t){let a=(0,r.U)(),{data:s,error:i}=await a.auth.signInWithPassword({email:e,password:t});return{data:s,error:i}}async function n(){let e=(0,r.U)(),{error:t}=await e.auth.signOut();return{error:t}}},66805:()=>{},67413:(e,t,a)=>{"use strict";a.d(t,{A:()=>f});var r=a(60687),s=a(43210),i=a(26001),n=a(96474),l=a(5748),o=a(66594),c=a(11860),d=a(2643),u=a(51907),m=a(43949),h=a(70695),x=a(45225),p=a(28749),b=a(99537);function f({onSuccess:e,onCancel:t,defaultType:a="expense"}){let[f,g]=(0,s.useState)(!1),[y,v]=(0,s.useState)([]),[w,j]=(0,s.useState)({type:a,amount:"",description:"",category_id:"",transaction_date:new Date().toISOString().split("T")[0],currency:"LKR",updateAssets:!1,updateLiabilities:!1,updateReceivables:!1,assetName:"",liabilityName:"",receivableName:"",debtorName:""}),N=async t=>{t.preventDefault(),g(!0);try{let t=await (0,b.ON)({type:w.type,amount:parseFloat(w.amount),description:w.description||null,category_id:w.category_id||null,transaction_date:w.transaction_date,currency:w.currency,updateAssets:w.updateAssets,updateLiabilities:w.updateLiabilities,updateReceivables:w.updateReceivables,assetName:w.assetName,liabilityName:w.liabilityName,receivableName:w.receivableName,debtorName:w.debtorName});if(t.error){console.error("Error creating transaction:",t.error),alert("Error creating transaction: "+t.error.message);return}e()}catch(e){console.error("Error creating transaction:",e),alert("Error creating transaction: "+e.message)}finally{g(!1)}},C=(e,t)=>{j(a=>({...a,[e]:t}))},A=[{value:"income",label:"Income",icon:n.A,color:"text-green-600",bg:"bg-green-50"},{value:"expense",label:"Expense",icon:l.A,color:"text-red-600",bg:"bg-red-50"},{value:"transfer",label:"Transfer",icon:o.A,color:"text-blue-600",bg:"bg-blue-50"}],U=A.find(e=>e.value===w.type);return(0,r.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4 z-50",children:(0,r.jsxs)(p.Card,{className:"w-full max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)(p.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)(p.CardTitle,{className:"flex items-center",children:[U&&(0,r.jsx)("div",{className:`p-2 rounded-xl ${U.bg} mr-3`,children:(0,r.jsx)(U.icon,{className:`h-5 w-5 ${U.color}`})}),"Add New Transaction"]}),(0,r.jsx)(d.$,{variant:"ghost",size:"sm",onClick:t,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(p.CardContent,{children:(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{children:"Transaction Type"}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-2 sm:gap-3 mt-2",children:A.map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>C("type",e.value),className:`
                      p-3 sm:p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center space-y-1 sm:space-y-2
                      ${w.type===e.value?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}
                    `,children:[(0,r.jsx)(e.icon,{className:`h-5 w-5 sm:h-6 sm:w-6 ${e.color}`}),(0,r.jsx)("span",{className:"font-medium text-xs sm:text-sm",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"amount",children:"Amount *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.p,{id:"amount",type:"number",step:"0.01",value:w.amount,onChange:e=>C("amount",e.target.value),placeholder:"0.00",required:!0,className:"pl-12"}),(0,r.jsx)("span",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium",children:"LKR"===w.currency?"Rs.":"$"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"currency",children:"Currency"}),(0,r.jsxs)(h.l,{id:"currency",value:w.currency,onChange:e=>C("currency",e.target.value),children:[(0,r.jsx)("option",{value:"LKR",children:"LKR (Sri Lankan Rupee)"}),(0,r.jsx)("option",{value:"USD",children:"USD (US Dollar)"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"category_id",children:"Category"}),(0,r.jsxs)(h.l,{id:"category_id",value:w.category_id,onChange:e=>C("category_id",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),y.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"transaction_date",children:"Date"}),(0,r.jsx)(u.p,{id:"transaction_date",type:"date",value:w.transaction_date,onChange:e=>C("transaction_date",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(m.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)(x.T,{id:"description",value:w.description,onChange:e=>C("description",e.target.value),placeholder:"Add a note about this transaction...",rows:3})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Advanced Options"}),(0,r.jsxs)("div",{className:"space-y-4",children:["income"===w.type&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateAssets",checked:w.updateAssets,onChange:e=>C("updateAssets",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateAssets",children:"Add to Assets"}),w.updateAssets&&(0,r.jsx)(u.p,{placeholder:"Asset name",value:w.assetName,onChange:e=>C("assetName",e.target.value),className:"flex-1"})]}),"expense"===w.type&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateLiabilities",checked:w.updateLiabilities,onChange:e=>C("updateLiabilities",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateLiabilities",children:"Add to Liabilities"}),w.updateLiabilities&&(0,r.jsx)(u.p,{placeholder:"Liability name",value:w.liabilityName,onChange:e=>C("liabilityName",e.target.value),className:"flex-1"})]}),"transfer"===w.type&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"updateReceivables",checked:w.updateReceivables,onChange:e=>C("updateReceivables",e.target.checked),className:"rounded"}),(0,r.jsx)(m.J,{htmlFor:"updateReceivables",children:"Add to Receivables"})]}),w.updateReceivables&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsx)(u.p,{placeholder:"Receivable name",value:w.receivableName,onChange:e=>C("receivableName",e.target.value)}),(0,r.jsx)(u.p,{placeholder:"Debtor name",value:w.debtorName,onChange:e=>C("debtorName",e.target.value)})]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(d.$,{type:"submit",loading:f,children:"Add Transaction"})]})]})})]})})}a(42308)},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70695:(e,t,a)=>{"use strict";a.d(t,{l:()=>n});var r=a(60687),s=a(43210),i=a(4780);let n=(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("select",{className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a,children:t}));n.displayName="Select"},71769:(e,t,a)=>{"use strict";a.d(t,{$o:()=>i,AO:()=>l,Y:()=>s,gT:()=>n});var r=a(79481);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("assets").select(`
      *,
      categories (
        id,
        name,
        color
      )
    `).order("created_at",{ascending:!1});return{data:t,error:a}}async function i(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:i,error:n}=await t.from("assets").insert({...e,user_id:a.id}).select().single();return{data:i,error:n}}async function n(e,t){let a=(0,r.U)(),{data:s,error:i}=await a.from("assets").update(t).eq("id",e).select().single();return{data:s,error:i}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("assets").delete().eq("id",e);return{error:a}}},79481:(e,t,a)=>{"use strict";a.d(t,{U:()=>s});var r=a(59522);function s(){return(0,r.createBrowserClient)("https://lwfiqyypbdphguadzqbe.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3ZmlxeXlwYmRwaGd1YWR6cWJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwODA2NTMsImV4cCI6MjA2NzY1NjY1M30.KaxWQZgd1EBY-ksnefW6pokzLTO4LracWY36dnGC0us")}},83709:(e,t,a)=>{"use strict";a.d(t,{A0:()=>s,Be:()=>o,OX:()=>n,Sc:()=>c,XM:()=>i,eg:()=>l});var r=a(79481);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("receivables").select(`
      *,
      categories (
        id,
        name,
        color
      )
    `).order("created_at",{ascending:!1});return{data:t,error:a}}async function i(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:i,error:n}=await t.from("receivables").insert({...e,user_id:a.id}).select().single();return{data:i,error:n}}async function n(e,t){let a=(0,r.U)(),{data:s,error:i}=await a.from("receivables").update(t).eq("id",e).select().single();return{data:s,error:i}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("receivables").delete().eq("id",e);return{error:a}}async function o(){let e=(0,r.U)(),{data:t,error:a}=await e.from("categories").select("*").eq("type","receivable").order("name");return{data:t,error:a}}function c(e){let t=new Date(e);return Math.max(0,Math.ceil((new Date().getTime()-t.getTime())/864e5))}},86945:(e,t,a)=>{"use strict";a.d(t,{W2:()=>i,mz:()=>s,p9:()=>n,pq:()=>l});var r=a(79481);async function s(){let e=(0,r.U)(),{data:t,error:a}=await e.from("liabilities").select(`
      *,
      categories (
        id,
        name,
        color
      )
    `).order("created_at",{ascending:!1});return{data:t,error:a}}async function i(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:i,error:n}=await t.from("liabilities").insert({...e,user_id:a.id}).select().single();return{data:i,error:n}}async function n(e,t){let a=(0,r.U)(),{data:s,error:i}=await a.from("liabilities").update(t).eq("id",e).select().single();return{data:s,error:i}}async function l(e){let t=(0,r.U)(),{error:a}=await t.from("liabilities").delete().eq("id",e);return{error:a}}},87019:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,metadata:()=>o});var r=a(37413),s=a(22376),i=a.n(s),n=a(68726),l=a.n(n);a(61135);let o={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:e})})}},99537:(e,t,a)=>{"use strict";a.d(t,{I0:()=>l,ON:()=>d,Uw:()=>c,zA:()=>u});var r=a(79481),s=a(71769),i=a(86945),n=a(83709);async function l(){let e=(0,r.U)(),{data:t,error:a}=await e.from("transactions").select(`
      *,
      categories (
        id,
        name,
        color
      )
    `).order("transaction_date",{ascending:!1});return{data:t,error:a}}async function o(e){let t=(0,r.U)(),{data:{user:a},error:s}=await t.auth.getUser();if(s||!a)return{data:null,error:s||Error("User not authenticated")};let{data:i,error:n}=await t.from("transactions").insert({...e,user_id:a.id}).select().single();return{data:i,error:n}}async function c(e){let t=(0,r.U)(),{error:a}=await t.from("transactions").delete().eq("id",e);return{error:a}}async function d(e){let{updateAssets:t,updateLiabilities:a,updateReceivables:r,assetName:l,liabilityName:c,receivableName:d,debtorName:u,...m}=e,{data:h,error:x}=await o(m);return x||!h?{data:null,error:x}:("income"===e.type&&e.updateAssets&&e.assetName&&await (0,s.$o)({name:e.assetName,current_value:e.amount,asset_type:"cash",currency:e.currency||"LKR",description:`From transaction: ${e.description||"Income"}`}),"expense"===e.type&&e.updateLiabilities&&e.liabilityName&&await (0,i.W2)({name:e.liabilityName,principal_amount:e.amount,current_balance:e.amount,liability_type:"other",currency:e.currency||"LKR",description:`From transaction: ${e.description||"Expense"}`}),"transfer"===e.type&&e.updateReceivables&&e.receivableName&&e.debtorName&&await (0,n.XM)({debtor_name:e.debtorName,principal_amount:e.amount,current_balance:e.amount,currency:e.currency||"LKR",description:`From transaction: ${e.description||"Transfer"}`}),{data:h,error:null})}async function u(e,t){let a=(0,r.U)().from("transactions").select("type, amount, currency");e&&(a=a.gte("transaction_date",e)),t&&(a=a.lte("transaction_date",t));let{data:s,error:i}=await a;if(i||!s)return{data:null,error:i};let n=s.reduce((e,t)=>{let{type:a,amount:r}=t;return e[a]||(e[a]=0),e[a]+=r,e},{});return{data:{income:n.income||0,expense:n.expense||0,transfer:n.transfer||0,netIncome:(n.income||0)-(n.expense||0)},error:null}}}};