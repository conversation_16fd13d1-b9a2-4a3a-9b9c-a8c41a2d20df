'use client'

import { useState, useEffect, useMemo } from 'react'
import { Edit, Trash2, Plus } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import SearchInput from '@/components/ui/SearchInput'
import FilterDropdown from '@/components/ui/FilterDropdown'
import { getAssets, deleteAsset } from '@/lib/api/assets'
import { formatCurrency, formatDate } from '@/lib/utils'
import AssetForm from './AssetForm'
import type { Asset } from '@/lib/types/database'

export default function AssetList() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingAsset, setEditingAsset] = useState<Asset | undefined>()
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    asset_type: '',
    min_value: '',
    max_value: '',
    purchase_date_from: '',
    purchase_date_to: ''
  })

  useEffect(() => {
    loadAssets()
  }, [])

  const loadAssets = async () => {
    setLoading(true)
    const { data } = await getAssets()
    if (data) setAssets(data)
    setLoading(false)
  }

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this asset?')) {
      await deleteAsset(id)
      loadAssets()
    }
  }

  const handleEdit = (asset: Asset) => {
    setEditingAsset(asset)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingAsset(undefined)
    loadAssets()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingAsset(undefined)
  }

  // Filter and search assets
  const filteredAssets = useMemo(() => {
    return assets.filter(asset => {
      // Search filter
      const matchesSearch = searchTerm === '' ||
        asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.asset_type.toLowerCase().includes(searchTerm.toLowerCase())

      // Type filter
      const matchesType = filters.asset_type === '' || asset.asset_type === filters.asset_type

      // Value filters
      const matchesMinValue = filters.min_value === '' || asset.current_value >= parseFloat(filters.min_value)
      const matchesMaxValue = filters.max_value === '' || asset.current_value <= parseFloat(filters.max_value)

      // Date filters
      const matchesFromDate = filters.purchase_date_from === '' ||
        !asset.purchase_date ||
        new Date(asset.purchase_date) >= new Date(filters.purchase_date_from)
      const matchesToDate = filters.purchase_date_to === '' ||
        !asset.purchase_date ||
        new Date(asset.purchase_date) <= new Date(filters.purchase_date_to)

      return matchesSearch && matchesType && matchesMinValue && matchesMaxValue && matchesFromDate && matchesToDate
    })
  }, [assets, searchTerm, filters])

  const totalValue = filteredAssets.reduce((sum, asset) => sum + asset.current_value, 0)

  const filterOptions = [
    {
      key: 'asset_type',
      label: 'Asset Type',
      type: 'select' as const,
      options: [
        { value: 'investment', label: 'Investment' },
        { value: 'real_estate', label: 'Real Estate' },
        { value: 'vehicle', label: 'Vehicle' },
        { value: 'cash', label: 'Cash' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      key: 'min_value',
      label: 'Minimum Value',
      type: 'number' as const
    },
    {
      key: 'max_value',
      label: 'Maximum Value',
      type: 'number' as const
    },
    {
      key: 'purchase_date_from',
      label: 'Purchase Date From',
      type: 'date' as const
    },
    {
      key: 'purchase_date_to',
      label: 'Purchase Date To',
      type: 'date' as const
    }
  ]

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleClearFilters = () => {
    setFilters({
      asset_type: '',
      min_value: '',
      max_value: '',
      purchase_date_from: '',
      purchase_date_to: ''
    })
    setSearchTerm('')
  }

  if (showForm) {
    return (
      <div className="flex justify-center">
        <AssetForm
          asset={editingAsset}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Assets</h1>
          <p className="text-gray-600">
            Total Value: {formatCurrency(totalValue)}
            {filteredAssets.length !== assets.length && (
              <span className="text-sm text-gray-500 ml-2">
                ({filteredAssets.length} of {assets.length} shown)
              </span>
            )}
          </p>
        </div>
        <Button onClick={() => setShowForm(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Asset
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <SearchInput
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search assets..."
          className="flex-1"
        />
        <FilterDropdown
          filters={filterOptions}
          values={filters}
          onChange={handleFilterChange}
          onClear={handleClearFilters}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Assets</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredAssets.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {assets.length === 0
                ? "No assets found. Add your first asset to get started."
                : "No assets match your search criteria."
              }
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Current Value</TableHead>
                      <TableHead>Purchase Value</TableHead>
                      <TableHead>Purchase Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAssets.map((asset) => (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{asset.name}</TableCell>
                        <TableCell className="capitalize">{asset.asset_type.replace('_', ' ')}</TableCell>
                        <TableCell>{formatCurrency(asset.current_value, asset.currency)}</TableCell>
                        <TableCell>
                          {asset.purchase_value ? formatCurrency(asset.purchase_value, asset.currency) : '-'}
                        </TableCell>
                        <TableCell>
                          {asset.purchase_date ? formatDate(asset.purchase_date) : '-'}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(asset)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(asset.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="lg:hidden space-y-4">
                {filteredAssets.map((asset) => (
                  <div key={asset.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900">{asset.name}</h3>
                        <p className="text-sm text-gray-500 capitalize">{asset.asset_type.replace('_', ' ')}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(asset)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(asset.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Current Value:</span>
                        <span className="text-sm font-medium">{formatCurrency(asset.current_value, asset.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Purchase Value:</span>
                        <span className="text-sm font-medium">
                          {asset.purchase_value ? formatCurrency(asset.purchase_value, asset.currency) : '-'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Purchase Date:</span>
                        <span className="text-sm font-medium">
                          {asset.purchase_date ? formatDate(asset.purchase_date) : '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
