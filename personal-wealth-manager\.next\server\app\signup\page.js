(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4464:(e,t,r)=>{Promise.resolve().then(r.bind(r,38255))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14192:(e,t,r)=>{Promise.resolve().then(r.bind(r,33221))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24949:()=>{},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33221:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),n=r(16189),i=r(85814),o=r.n(i),l=r(63523);function d(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(""),[d,u]=(0,a.useState)(""),[c,p]=(0,a.useState)(!1),[m,x]=(0,a.useState)(""),[h,b]=(0,a.useState)(!1);(0,n.useRouter)();let g=async t=>{t.preventDefault(),p(!0),x("");let{error:s}=await (0,l.Hh)(e,r,d);s?x(s.message):b(!0),p(!1)};return h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Check your email"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"We've sent you a confirmation link. Please check your email and click the link to activate your account."}),(0,s.jsx)(o(),{href:"/login",className:"mt-4 inline-block font-medium text-blue-600 hover:text-blue-500",children:"Back to sign in"})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(o(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:g,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{id:"fullName",name:"fullName",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your full name",value:d,onChange:e=>u(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Create a password",value:r,onChange:e=>i(e.target.value)})]})]}),m&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center",children:m}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Creating account...":"Create account"})})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\signup\\page.tsx","default")},39727:()=>{},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63043:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},63523:(e,t,r)=>{"use strict";r.d(t,{CI:()=>i,Hh:()=>a,Jv:()=>n});var s=r(79481);async function a(e,t,r){let a=(0,s.U)(),{data:n,error:i}=await a.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});return{data:n,error:i}}async function n(e,t){let r=(0,s.U)(),{data:a,error:n}=await r.auth.signInWithPassword({email:e,password:t});return{data:a,error:n}}async function i(){let e=(0,s.U)(),{error:t}=await e.auth.signOut();return{error:t}}},65601:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38255)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\signup\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66805:()=>{},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var s=r(59522);function a(){return(0,s.createBrowserClient)("https://lwfiqyypbdphguadzqbe.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3ZmlxeXlwYmRwaGd1YWR6cWJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwODA2NTMsImV4cCI6MjA2NzY1NjY1M30.KaxWQZgd1EBY-ksnefW6pokzLTO4LracWY36dnGC0us")}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87019:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),a=r(22376),n=r.n(a),i=r(68726),o=r.n(i);r(61135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:e})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,79,814],()=>r(65601));module.exports=s})();