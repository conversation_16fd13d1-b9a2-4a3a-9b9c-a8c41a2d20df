'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Check, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { getProfile, updateCurrency } from '@/lib/api/profile'
import { SUPPORTED_CURRENCIES, type CurrencyCode } from '@/lib/currency'
import type { Profile } from '@/lib/types/database'

export default function CurrencySettings() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    setLoading(true)
    const { profile } = await getProfile()
    if (profile) setProfile(profile)
    setLoading(false)
  }

  const handleCurrencyChange = async (currency: CurrencyCode) => {
    setUpdating(true)
    try {
      const { data } = await updateCurrency(currency)
      if (data) {
        setProfile(data)
        // Refresh the page to update all currency displays
        window.location.reload()
      }
    } catch (error) {
      console.error('Error updating currency:', error)
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-300 rounded w-1/4"></div>
            <div className="space-y-3">
              <div className="h-16 bg-gray-300 rounded"></div>
              <div className="h-16 bg-gray-300 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          Currency Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-gray-600">
            Choose your preferred currency for displaying amounts throughout the application.
          </p>
          
          <div className="grid gap-4">
            {Object.entries(SUPPORTED_CURRENCIES).map(([code, currency]) => {
              const isSelected = profile?.preferred_currency === code
              
              return (
                <motion.button
                  key={code}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleCurrencyChange(code as CurrencyCode)}
                  disabled={updating || isSelected}
                  className={`
                    p-4 rounded-xl border-2 transition-all duration-200 text-left
                    ${isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                    ${updating ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`
                        w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg
                        ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}
                      `}>
                        {currency.symbol}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{currency.name}</h3>
                        <p className="text-sm text-gray-500">{code}</p>
                      </div>
                    </div>
                    
                    {isSelected && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                      >
                        <Check className="h-4 w-4 text-white" />
                      </motion.div>
                    )}
                  </div>
                </motion.button>
              )
            })}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-xl">
            <h4 className="font-medium text-blue-900 mb-2">Currency Information</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• LKR (Sri Lankan Rupee) is the default currency</li>
              <li>• USD (US Dollar) is also supported</li>
              <li>• You can change your currency preference at any time</li>
              <li>• Existing data will be displayed in your selected currency</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
