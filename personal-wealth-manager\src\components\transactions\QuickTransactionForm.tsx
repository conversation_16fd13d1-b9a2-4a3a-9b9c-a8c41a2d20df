'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Plus, Minus, ArrowRightLeft, X } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { processTransaction } from '@/lib/api/transactions'
import { getCategories } from '@/lib/api/categories'
import type { Category } from '@/lib/types/database'

interface QuickTransactionFormProps {
  onSuccess: () => void
  onCancel: () => void
  defaultType?: 'income' | 'expense' | 'transfer'
}

export default function QuickTransactionForm({ 
  onSuccess, 
  onCancel, 
  defaultType = 'expense' 
}: QuickTransactionFormProps) {
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [formData, setFormData] = useState({
    type: defaultType,
    amount: '',
    description: '',
    category_id: '',
    transaction_date: new Date().toISOString().split('T')[0],
    currency: 'LKR',
    // Advanced options
    updateAssets: false,
    updateLiabilities: false,
    updateReceivables: false,
    assetName: '',
    liabilityName: '',
    receivableName: '',
    debtorName: ''
  })

  useEffect(() => {
    loadCategories()
  }, [formData.type])

  const loadCategories = async () => {
    const { data } = await getCategories(formData.type)
    if (data) setCategories(data)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const result = await processTransaction({
        type: formData.type as 'income' | 'expense' | 'transfer',
        amount: parseFloat(formData.amount),
        description: formData.description || null,
        category_id: formData.category_id || null,
        transaction_date: formData.transaction_date,
        currency: formData.currency,
        updateAssets: formData.updateAssets,
        updateLiabilities: formData.updateLiabilities,
        updateReceivables: formData.updateReceivables,
        assetName: formData.assetName,
        liabilityName: formData.liabilityName,
        receivableName: formData.receivableName,
        debtorName: formData.debtorName
      })

      if (result.error) {
        console.error('Error creating transaction:', result.error)
        alert('Error creating transaction: ' + result.error.message)
        return
      }

      onSuccess()
    } catch (error) {
      console.error('Error creating transaction:', error)
      alert('Error creating transaction: ' + (error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const transactionTypes = [
    { value: 'income', label: 'Income', icon: Plus, color: 'text-green-600', bg: 'bg-green-50' },
    { value: 'expense', label: 'Expense', icon: Minus, color: 'text-red-600', bg: 'bg-red-50' },
    { value: 'transfer', label: 'Transfer', icon: ArrowRightLeft, color: 'text-blue-600', bg: 'bg-blue-50' }
  ]

  const currentType = transactionTypes.find(t => t.value === formData.type)

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
    >
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            {currentType && (
              <div className={`p-2 rounded-xl ${currentType.bg} mr-3`}>
                <currentType.icon className={`h-5 w-5 ${currentType.color}`} />
              </div>
            )}
            Add New Transaction
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Transaction Type */}
            <div>
              <Label>Transaction Type</Label>
              <div className="grid grid-cols-3 gap-3 mt-2">
                {transactionTypes.map((type) => (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => handleChange('type', type.value)}
                    className={`
                      p-4 rounded-xl border-2 transition-all duration-200 flex flex-col items-center space-y-2
                      ${formData.type === type.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <type.icon className={`h-6 w-6 ${type.color}`} />
                    <span className="font-medium text-sm">{type.label}</span>
                  </button>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Amount */}
              <div>
                <Label htmlFor="amount">Amount *</Label>
                <div className="relative">
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => handleChange('amount', e.target.value)}
                    placeholder="0.00"
                    required
                    className="pl-12"
                  />
                  <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                    {formData.currency === 'LKR' ? 'Rs.' : '$'}
                  </span>
                </div>
              </div>

              {/* Currency */}
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  id="currency"
                  value={formData.currency}
                  onChange={(e) => handleChange('currency', e.target.value)}
                >
                  <option value="LKR">LKR (Sri Lankan Rupee)</option>
                  <option value="USD">USD (US Dollar)</option>
                </Select>
              </div>
            </div>

            {/* Category */}
            <div>
              <Label htmlFor="category_id">Category</Label>
              <Select
                id="category_id"
                value={formData.category_id}
                onChange={(e) => handleChange('category_id', e.target.value)}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </Select>
            </div>

            {/* Date */}
            <div>
              <Label htmlFor="transaction_date">Date</Label>
              <Input
                id="transaction_date"
                type="date"
                value={formData.transaction_date}
                onChange={(e) => handleChange('transaction_date', e.target.value)}
                required
              />
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Add a note about this transaction..."
                rows={3}
              />
            </div>

            {/* Advanced Options */}
            <div className="border-t pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Advanced Options</h3>
              <div className="space-y-4">
                {formData.type === 'income' && (
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="updateAssets"
                      checked={formData.updateAssets}
                      onChange={(e) => handleChange('updateAssets', e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="updateAssets">Add to Assets</Label>
                    {formData.updateAssets && (
                      <Input
                        placeholder="Asset name"
                        value={formData.assetName}
                        onChange={(e) => handleChange('assetName', e.target.value)}
                        className="flex-1"
                      />
                    )}
                  </div>
                )}

                {formData.type === 'expense' && (
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="updateLiabilities"
                      checked={formData.updateLiabilities}
                      onChange={(e) => handleChange('updateLiabilities', e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="updateLiabilities">Add to Liabilities</Label>
                    {formData.updateLiabilities && (
                      <Input
                        placeholder="Liability name"
                        value={formData.liabilityName}
                        onChange={(e) => handleChange('liabilityName', e.target.value)}
                        className="flex-1"
                      />
                    )}
                  </div>
                )}

                {formData.type === 'transfer' && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="updateReceivables"
                        checked={formData.updateReceivables}
                        onChange={(e) => handleChange('updateReceivables', e.target.checked)}
                        className="rounded"
                      />
                      <Label htmlFor="updateReceivables">Add to Receivables</Label>
                    </div>
                    {formData.updateReceivables && (
                      <div className="grid grid-cols-2 gap-3">
                        <Input
                          placeholder="Receivable name"
                          value={formData.receivableName}
                          onChange={(e) => handleChange('receivableName', e.target.value)}
                        />
                        <Input
                          placeholder="Debtor name"
                          value={formData.debtorName}
                          onChange={(e) => handleChange('debtorName', e.target.value)}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                Add Transaction
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
