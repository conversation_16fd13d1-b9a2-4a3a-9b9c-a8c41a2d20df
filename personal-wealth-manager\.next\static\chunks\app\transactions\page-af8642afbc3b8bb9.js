(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[790],{18474:(e,s,a)=>{"use strict";a.d(s,{default:()=>N});var t=a(95155),r=a(12115),l=a(60760),n=a(76408),c=a(84616),i=a(87712),d=a(76356),o=a(69074),x=a(62525),m=a(13741),h=a(17703),j=a(75399),u=a(93154),p=a(72901),f=a(59434),y=a(14943);function N(){let[e,s]=(0,r.useState)([]),[a,N]=(0,r.useState)(!0),[g,b]=(0,r.useState)(!1),[v,w]=(0,r.useState)(""),[A,C]=(0,r.useState)("all"),[k,T]=(0,r.useState)({income:0,expense:0,transfer:0,netIncome:0});(0,r.useEffect)(()=>{R(),I()},[]);let R=async()=>{N(!0);let{data:e}=await (0,p.I0)();e&&s(e),N(!1)},I=async()=>{let{data:e}=await (0,p.zA)();e&&T(e)},S=async e=>{confirm("Are you sure you want to delete this transaction?")&&(await (0,p.Uw)(e),R(),I())},_=e.filter(e=>{var s,a;let t=""===v||(null==(s=e.description)?void 0:s.toLowerCase().includes(v.toLowerCase()))||(null==(a=e.categories)?void 0:a.name.toLowerCase().includes(v.toLowerCase())),r="all"===A||e.type===A;return t&&r}),z=e=>{switch(e){case"income":default:return c.A;case"expense":return i.A;case"transfer":return d.A}},L=e=>{switch(e){case"income":return"text-green-600 bg-green-50";case"expense":return"text-red-600 bg-red-50";case"transfer":return"text-blue-600 bg-blue-50";default:return"text-gray-600 bg-gray-50"}};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Transactions"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Track your income, expenses, and transfers"})]}),(0,t.jsxs)(m.$,{onClick:()=>b(!0),variant:"gradient",className:"w-full sm:w-auto",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Add Transaction"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,t.jsx)(h.Card,{className:"bg-gradient-to-br from-green-50 to-emerald-100 border-green-200",children:(0,t.jsx)(h.CardContent,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"Total Income"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-700",children:(0,f.vv)(k.income,"LKR")})]}),(0,t.jsx)("div",{className:"p-3 bg-green-200 rounded-xl",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-green-700"})})]})})}),(0,t.jsx)(h.Card,{className:"bg-gradient-to-br from-red-50 to-rose-100 border-red-200",children:(0,t.jsx)(h.CardContent,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-red-600 text-sm font-medium",children:"Total Expenses"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-700",children:(0,f.vv)(k.expense,"LKR")})]}),(0,t.jsx)("div",{className:"p-3 bg-red-200 rounded-xl",children:(0,t.jsx)(i.A,{className:"h-6 w-6 text-red-700"})})]})})}),(0,t.jsx)(h.Card,{className:"bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200",children:(0,t.jsx)(h.CardContent,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:"Total Transfers"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-700",children:(0,f.vv)(k.transfer,"LKR")})]}),(0,t.jsx)("div",{className:"p-3 bg-blue-200 rounded-xl",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-blue-700"})})]})})}),(0,t.jsx)(h.Card,{className:"bg-gradient-to-br ".concat(k.netIncome>=0?"from-emerald-50 to-green-100 border-emerald-200":"from-red-50 to-rose-100 border-red-200"),children:(0,t.jsx)(h.CardContent,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium ".concat(k.netIncome>=0?"text-emerald-600":"text-red-600"),children:"Net Income"}),(0,t.jsx)("p",{className:"text-2xl font-bold ".concat(k.netIncome>=0?"text-emerald-700":"text-red-700"),children:(0,f.vv)(k.netIncome,"LKR")})]}),(0,t.jsx)("div",{className:"p-3 rounded-xl ".concat(k.netIncome>=0?"bg-emerald-200":"bg-red-200"),children:(0,t.jsx)(o.A,{className:"h-6 w-6 ".concat(k.netIncome>=0?"text-emerald-700":"text-red-700")})})]})})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)(u.A,{value:v,onChange:w,placeholder:"Search transactions...",className:"flex-1"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:["all","income","expense","transfer"].map(e=>(0,t.jsx)(m.$,{variant:A===e?"primary":"outline",size:"sm",onClick:()=>C(e),className:"capitalize flex-1 sm:flex-none min-w-0",children:"all"===e?"All":e},e))})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Recent Transactions"})}),(0,t.jsx)(h.CardContent,{children:a?(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===_.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No transactions found"}),(0,t.jsx)("p",{className:"text-gray-500 mb-6",children:0===e.length?"Start tracking your finances by adding your first transaction":"No transactions match your search criteria"}),(0,t.jsxs)(m.$,{onClick:()=>b(!0),children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Transaction"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"hidden lg:block",children:(0,t.jsxs)(j.XI,{children:[(0,t.jsx)(j.A0,{children:(0,t.jsxs)(j.Hj,{children:[(0,t.jsx)(j.nd,{children:"Type"}),(0,t.jsx)(j.nd,{children:"Description"}),(0,t.jsx)(j.nd,{children:"Category"}),(0,t.jsx)(j.nd,{children:"Amount"}),(0,t.jsx)(j.nd,{children:"Date"}),(0,t.jsx)(j.nd,{children:"Actions"})]})}),(0,t.jsx)(j.BF,{children:(0,t.jsx)(l.N,{children:_.map((e,s)=>{let a=z(e.type),r=L(e.type);return(0,t.jsxs)(n.P.tr,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.05*s},className:"hover:bg-gray-50",children:[(0,t.jsx)(j.nA,{children:(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(r),children:[(0,t.jsx)(a,{className:"h-4 w-4 mr-2"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,t.jsx)(j.nA,{className:"font-medium",children:e.description||"No description"}),(0,t.jsx)(j.nA,{children:e.categories?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.categories.color}}),e.categories.name]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Uncategorized"})}),(0,t.jsx)(j.nA,{className:"font-semibold",children:(0,f.vv)(e.amount,e.currency)}),(0,t.jsx)(j.nA,{children:(0,f.Yq)(e.transaction_date)}),(0,t.jsx)(j.nA,{children:(0,t.jsx)("div",{className:"flex space-x-2",children:(0,t.jsx)(m.$,{size:"sm",variant:"destructive",onClick:()=>S(e.id),children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})})})]},e.id)})})})]})}),(0,t.jsx)("div",{className:"lg:hidden space-y-4",children:(0,t.jsx)(l.N,{children:_.map((e,s)=>{let a=z(e.type),r=L(e.type);return(0,t.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.05*s},className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(r),children:[(0,t.jsx)(a,{className:"h-4 w-4 mr-2"}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]}),(0,t.jsx)(m.$,{size:"sm",variant:"destructive",onClick:()=>S(e.id),className:"ml-2",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.description||"No description"})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center",children:e.categories?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.categories.color}}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:e.categories.name})]}):(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Uncategorized"})}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:(0,f.Yq)(e.transaction_date)})]}),(0,t.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(0,f.vv)(e.amount,e.currency)})})]})]},e.id)})})})]})})]}),(0,t.jsx)(l.N,{children:g&&(0,t.jsx)(y.A,{onSuccess:()=>{b(!1),R(),I()},onCancel:()=>b(!1)})})]})}},40272:(e,s,a)=>{Promise.resolve().then(a.bind(a,13049)),Promise.resolve().then(a.bind(a,18474))},62525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},75399:(e,s,a)=>{"use strict";a.d(s,{A0:()=>c,BF:()=>i,Hj:()=>d,XI:()=>n,nA:()=>x,nd:()=>o});var t=a(95155),r=a(12115),l=a(59434);let n=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",a),...r})})});n.displayName="Table";let c=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",a),...r})});c.displayName="TableHeader";let i=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",a),...r})});i.displayName="TableBody",(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tfoot",{ref:s,className:(0,l.cn)("bg-gray-900/5 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let d=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",a),...r})});d.displayName="TableRow";let o=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",a),...r})});o.displayName="TableHead";let x=(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});x.displayName="TableCell",(0,r.forwardRef)((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-gray-500",a),...r})}).displayName="TableCaption"},93154:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(95155);let r=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var l=a(54416),n=a(93915),c=a(13741);function i(e){let{value:s,onChange:a,placeholder:i="Search...",className:d}=e;return(0,t.jsxs)("div",{className:"relative ".concat(d),children:[(0,t.jsx)(r,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{type:"text",placeholder:i,value:s,onChange:e=>a(e.target.value),className:"pl-10 pr-10"}),s&&(0,t.jsx)(c.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>a(""),children:(0,t.jsx)(l.A,{className:"h-4 w-4"})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[271,874,556,49,441,684,358],()=>s(40272)),_N_E=e.O()}]);