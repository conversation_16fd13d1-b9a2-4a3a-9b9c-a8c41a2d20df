(()=>{var e={};e.id=379,e.ids=[379],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(37413),s=r(46655),l=r(99339);function i(){return(0,a.jsx)(s.default,{children:(0,a.jsx)(l.default,{})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56771:(e,t,r)=>{Promise.resolve().then(r.bind(r,99339)),Promise.resolve().then(r.bind(r,46655))},62124:(e,t,r)=>{"use strict";r.d(t,{default:()=>b});var a=r(60687),s=r(43210),l=r(88920),i=r(26001),n=r(96474),o=r(37360),c=r(63143),d=r(88233),u=r(2643),p=r(28749),x=r(42308),h=r(51907),m=r(43949),g=r(70695);let v=[{value:"#EF4444",label:"Red",bg:"bg-red-500"},{value:"#F59E0B",label:"Orange",bg:"bg-orange-500"},{value:"#EAB308",label:"Yellow",bg:"bg-yellow-500"},{value:"#10B981",label:"Green",bg:"bg-green-500"},{value:"#3B82F6",label:"Blue",bg:"bg-blue-500"},{value:"#8B5CF6",label:"Purple",bg:"bg-purple-500"},{value:"#EC4899",label:"Pink",bg:"bg-pink-500"},{value:"#6B7280",label:"Gray",bg:"bg-gray-500"}];function y({category:e,onSuccess:t,onCancel:r}){let[l,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({name:e?.name||"",type:e?.type||"expense",color:e?.color||"#3B82F6"}),d=async r=>{r.preventDefault(),n(!0);try{e?await (0,x.st)(e.id,o):await (0,x.zZ)(o),t()}catch(e){console.error("Error saving category:",e)}finally{n(!1)}},y=(e,t)=>{c(r=>({...r,[e]:t}))};return(0,a.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,a.jsxs)(p.Card,{className:"w-full max-w-md",children:[(0,a.jsx)(p.CardHeader,{children:(0,a.jsxs)(p.CardTitle,{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:o.color}}),e?"Edit Category":"Add New Category"]})}),(0,a.jsx)(p.CardContent,{children:(0,a.jsxs)("form",{onSubmit:d,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"name",children:"Category Name *"}),(0,a.jsx)(h.p,{id:"name",value:o.name,onChange:e=>y("name",e.target.value),placeholder:"Enter category name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"type",children:"Category Type *"}),(0,a.jsxs)(g.l,{id:"type",value:o.type,onChange:e=>y("type",e.target.value),required:!0,children:[(0,a.jsx)("option",{value:"income",children:"Income"}),(0,a.jsx)("option",{value:"expense",children:"Expense"}),(0,a.jsx)("option",{value:"asset",children:"Asset"}),(0,a.jsx)("option",{value:"liability",children:"Liability"}),(0,a.jsx)("option",{value:"receivable",children:"Receivable"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"color",children:"Color"}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-3 mt-2",children:v.map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>y("color",e.value),className:`
                      w-full h-12 rounded-xl ${e.bg} flex items-center justify-center
                      ${o.color===e.value?"ring-4 ring-blue-500 ring-offset-2":"hover:scale-105"}
                      transition-all duration-200
                    `,children:o.color===e.value&&(0,a.jsx)("svg",{className:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})},e.value))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(u.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,a.jsx)(u.$,{type:"submit",loading:l,children:e?"Update Category":"Create Category"})]})]})})]})})}function b(){let[e,t]=(0,s.useState)([]),[r,h]=(0,s.useState)(!0),[m,g]=(0,s.useState)(!1),[v,b]=(0,s.useState)(),[f,j]=(0,s.useState)("all"),C=async()=>{h(!0);let{data:e}=await (0,x.bW)();e&&t(e),h(!1)},w=async e=>{confirm("Are you sure you want to delete this category?")&&(await (0,x.K7)(e),C())},N=e=>{b(e),g(!0)},k="all"===f?e:e.filter(e=>e.type===f),P=[{value:"all",label:"All Categories",count:e.length},{value:"income",label:"Income",count:e.filter(e=>"income"===e.type).length},{value:"expense",label:"Expense",count:e.filter(e=>"expense"===e.type).length},{value:"asset",label:"Asset",count:e.filter(e=>"asset"===e.type).length},{value:"liability",label:"Liability",count:e.filter(e=>"liability"===e.type).length},{value:"receivable",label:"Receivable",count:e.filter(e=>"receivable"===e.type).length}];return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"Categories"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Organize your financial transactions with custom categories"})]}),(0,a.jsxs)(u.$,{onClick:()=>g(!0),variant:"gradient",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mr-2"}),"Add Category"]})]}),(0,a.jsx)(p.Card,{children:(0,a.jsx)(p.CardContent,{className:"p-6",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-3",children:P.map(e=>(0,a.jsxs)("button",{onClick:()=>j(e.value),className:`
                  px-4 py-2 rounded-xl font-medium transition-all duration-200
                  ${f===e.value?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg":"bg-gray-100 text-gray-700 hover:bg-gray-200"}
                `,children:[e.label," (",e.count,")"]},e.value))})})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,a.jsx)(l.N,{children:r?Array.from({length:6}).map((e,t)=>(0,a.jsx)(p.Card,{className:"animate-pulse",children:(0,a.jsxs)(p.CardContent,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-gray-300 rounded-full"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded flex-1"})]}),(0,a.jsx)("div",{className:"mt-4 h-3 bg-gray-300 rounded w-1/2"})]})},t)):0===k.length?(0,a.jsxs)("div",{className:"col-span-full text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No categories found"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"all"===f?"Create your first category to get started":`No ${f} categories found`}),(0,a.jsxs)(u.$,{onClick:()=>g(!0),children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Add Category"]})]}):k.map((e,t)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.1*t},children:(0,a.jsx)(p.Card,{className:"hover:shadow-xl transition-all duration-300 group",children:(0,a.jsxs)(p.CardContent,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name})]}),(0,a.jsxs)("div",{className:"flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(u.$,{size:"sm",variant:"outline",onClick:()=>N(e),children:(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsx)(u.$,{size:"sm",variant:"destructive",onClick:()=>w(e.id),children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500 capitalize",children:e.type.replace("_"," ")})]})})},e.id))})}),(0,a.jsx)(l.N,{children:m&&(0,a.jsx)(y,{category:v,onSuccess:()=>{g(!1),b(void 0),C()},onCancel:()=>{g(!1),b(void 0)}})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70843:(e,t,r)=>{Promise.resolve().then(r.bind(r,62124)),Promise.resolve().then(r.bind(r,63087))},74075:e=>{"use strict";e.exports=require("zlib")},74881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let c={children:["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8911)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\categories\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\app\\categories\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99339:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Personal_Wealth_Manager\\\\personal-wealth-manager\\\\src\\\\components\\\\categories\\\\CategoryList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Personal_Wealth_Manager\\personal-wealth-manager\\src\\components\\categories\\CategoryList.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,145,79,814,435,235],()=>r(74881));module.exports=a})();